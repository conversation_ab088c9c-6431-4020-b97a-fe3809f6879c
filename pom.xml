<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.4.RELEASE</version>
  </parent>

  <groupId>com.welab</groupId>
  <artifactId>welab-crm-interview</artifactId>
  <version>1.1.4-RELEASE</version>
  <packaging>pom</packaging>

  <name>welab-crm-interview</name>
  <url>https://maven.apache.org</url>


  <distributionManagement>
    <repository>
      <id>releases-repo</id>
      <name>Internal Releases</name>
      <url>http://${nexus.proxy.location}/nexus/content/repositories/releases/</url>
    </repository>
    <snapshotRepository>
      <id>snapshot-repo</id>
      <name>Development Snapshot</name>
      <url>http://${nexus.proxy.location}/nexus/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <!--module??? -->
    <project.version>1.1.2-RELEASE</project.version>

    <welab.common.version>1.7.2-RELEASE</welab.common.version>
    <welab.dds.version>1.2.7-RELEASE</welab.dds.version>
    <welab.web.springboot.version>1.0.6-RELEASE</welab.web.springboot.version>
    <welab.crm.base.version>1.0.0-RELEASE</welab.crm.base.version>
    <welab.collection.interview.version>1.4.6-RELEASE</welab.collection.interview.version>
    <welab.redis.springboot.version>1.0.1-RELEASE</welab.redis.springboot.version>

    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <org.springframework.version>4.3.9.RELEASE</org.springframework.version>
    <org.springframework.boot>1.5.4.RELEASE</org.springframework.boot>

    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons-collections.version>3.2.1</commons-collections.version>
    <tomcat-embed>8.5.15</tomcat-embed>
    <maven.checkstyle.version>2.17</maven.checkstyle.version>
    <maven.jxr.version>2.5</maven.jxr.version>
    <mybatis.plus.boot.starter.version>3.3.1</mybatis.plus.boot.starter.version>
    <mybatis.plus.generator.version>3.3.1</mybatis.plus.generator.version>
    <skipTests>true</skipTests>
  </properties>

  <dependencyManagement>
    <dependencies>

      <!-- ?????????jar?? begin -->
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-common</artifactId>
        <version>${welab.common.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-dds</artifactId>
        <version>${welab.dds.version}</version>
      </dependency>


      <dependency>
        <groupId>com.welab.base</groupId>
        <artifactId>welab-springboot-web</artifactId>
        <version>${welab.web.springboot.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-interview-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-interview-core</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-collection-interview-api</artifactId>
        <version>${welab.collection.interview.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-base-common</artifactId>
        <version>${welab.crm.base.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-base-api</artifactId>
        <version>${welab.crm.base.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>message-sms-api</artifactId>
        <version>1.4.70-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-installment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-repayment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-app-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-payment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>user-center-api</artifactId>
        <version>2.9.9-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>application-center-api</artifactId>
        <version>2.21.18-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.welab.agreement</groupId>
        <artifactId>agreement-api</artifactId>
        <version>2.2.4-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-capital-allocation-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-capital-api</artifactId>
        <version>1.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>product-api</artifactId>
        <version>1.1.1-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>marketing-api</artifactId>
        <version>2.4.24-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-common</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-loan-procedure-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-repayment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-bank-card-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-account-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-accounting-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-payment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <groupId>com.welab.base</groupId>
            <artifactId>welab-springboot-web</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-creditline-api</artifactId>
        <version>2.1.3-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wedefend-gateway-api</artifactId>
        <version>1.2.10-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>meta-space-api</artifactId>
        <version>1.1.6-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>document-api</artifactId>
        <version>1.3.18-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>loan-center-api</artifactId>
        <version>2.5.17-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-event</artifactId>
        <version>1.3.3-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-event-springboot-starter</artifactId>
        <version>1.3.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <groupId>com.welab</groupId>
            <artifactId>welab-event</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>approval-center-api</artifactId>
        <version>1.4.6-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-authority-api</artifactId>
        <version>1.1.2-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-user-center-api</artifactId>
        <version>1.0.0-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>wallet-application-center-api</artifactId>
        <version>1.2.1-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-redis-springboot-starter</artifactId>
        <version>${welab.redis.springboot.version}</version>
      </dependency>
      <!-- ?????????jar?? end -->


      <!-- Spring??????? begin -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-aop</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-core</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-beans</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-expression</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>
      <!-- Spring??????? end -->

      <!-- Spring Boot??????? begin -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>snakeyaml</artifactId>
            <groupId>org.yaml</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>tomcat-embed-el</artifactId>
            <groupId>org.apache.tomcat.embed</groupId>
          </exclusion>
          <exclusion>
            <artifactId>tomcat-embed-websocket</artifactId>
            <groupId>org.apache.tomcat.embed</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>
      <!-- Spring Boot??????? end -->

      <dependency>
        <groupId>com.dangdang</groupId>
        <artifactId>elastic-job-lite-core</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.dangdang</groupId>
        <artifactId>elastic-job-lite-spring</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.dangdang</groupId>
        <artifactId>elastic-job-lite-lifecycle</artifactId>
        <version>2.1.3</version>
        <exclusions>
          <exclusion>
            <groupId>org.eclipse.jetty.aggregate</groupId>
            <artifactId>jetty-all-server</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.boot.starter.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis.plus.generator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>3.4.5</version>
        <exclusions>
          <!-- ????????????????? -->
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>netty</artifactId>
            <groupId>org.jboss.netty</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>2.10.0</version>
      </dependency>

      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-recipes</artifactId>
        <version>2.10.0</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>dubbo</artifactId>
        <version>2.8.4</version>
        <exclusions>
          <exclusion>
            <artifactId>spring-web</artifactId>
            <groupId>org.springframework</groupId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.0.9</version>
      </dependency>

      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>5.1.46</version>
      </dependency>

      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>1.3</version>
      </dependency>

      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>${commons-collections.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>${tomcat-embed}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.16.18</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>1.5.13</version>
      </dependency>

      <!-- ??????????? Begin -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>${org.springframework.boot}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>json-path</artifactId>
            <groupId>com.jayway.jsonpath</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jsonassert</artifactId>
            <groupId>org.skyscreamer</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <!-- ??????????? end -->

      <!--voice-center-->
      <dependency>
        <groupId>com.welab.voice</groupId>
        <artifactId>voice-api</artifactId>
        <version>1.0.2</version>
      </dependency>

      <dependency>
        <groupId>javax.el</groupId>
        <artifactId>javax.el-api</artifactId>
        <version>2.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.web</groupId>
        <artifactId>javax.el</artifactId>
        <version>2.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>2.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>3.17</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>3.17</version>
      </dependency>

      <dependency>
        <groupId>com.mashape.unirest</groupId>
        <artifactId>unirest-java</artifactId>
        <version>1.4.9</version>
      </dependency>
    </dependencies>

  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
            <encoding>UTF-8</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>2.7</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <!--?????????Y??????plugin -->
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <modules>
    <module>welab-crm-interview-api</module>
    <module>welab-crm-interview-core</module>
    <module>welab-crm-interview-web</module>
  </modules>
</project>
