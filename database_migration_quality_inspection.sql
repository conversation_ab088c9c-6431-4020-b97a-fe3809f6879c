-- 为 con_phone_call_info 表添加质检相关字段
-- 执行时间：2024-08-04

ALTER TABLE `con_phone_call_info` 
ADD COLUMN `quality_inspection_status` INT(11) DEFAULT 0 COMMENT '质量检查状态:0-未检查, 1-检查中, 2-已完成, 3-检查失败',
ADD COLUMN `quality_inspection_score` INT(11) DEFAULT NULL COMMENT '质量检查分数',
ADD COLUMN `quality_inspection_time` DATETIME DEFAULT NULL COMMENT '质量检查完成时间',
ADD COLUMN `quality_inspection_task_id` VARCHAR(64) DEFAULT NULL COMMENT '质量检查任务ID';

-- 为质检任务ID字段添加索引，提高查询性能
CREATE INDEX `idx_quality_inspection_task_id` ON `con_phone_call_info` (`quality_inspection_task_id`);

-- 为质检状态字段添加索引，便于统计查询
CREATE INDEX `idx_quality_inspection_status` ON `con_phone_call_info` (`quality_inspection_status`);
