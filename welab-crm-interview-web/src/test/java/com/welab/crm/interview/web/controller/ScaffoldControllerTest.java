/**
 * @Title: ResponsCodeTypeEnum.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.web.controller;

import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.web.servlet.ResultActions;

import com.welab.crm.interview.dto.UserDTO;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.springboot.web.constant.Header;

/**
 * @description 业务控制类测试
 *
 * <AUTHOR> 请填写作者相关信息。例如：jason.zhuo
 * @date TODO 请修改时间，例如：2018-01-02 13:56:00
 * @version v1.0
 */

public class ScaffoldControllerTest extends AbstractControllerTest {

    private static String ROOT = "/welab-crm-interview";
	private Map<String, String> headers = new HashMap<String, String>();

	{
		/**
		 * 测试时往http header时写入userId和orgId
		 */
		headers.put(Header.HEADER_FOR_USER_ID, "1");
		headers.put(Header.HEADER_FOR_ORG_ID, "1");
	}

	/**
	 * 测试用户注册
	 */
	@Test
	public void testUserRegister() {
		try {
			UserDTO user = new UserDTO();
			user.setUserName("zhangsan6");
			user.setUserPassword("123456");

			ResultActions response = this.postJsonRequestMock(ROOT + Urls.User.V1_USER_REGISTER, user, headers);
			this.check(response);
		} catch (Exception e) {
			Assert.fail(e.getMessage());
		}
	}

	/**
	 * 测试逻辑删除用户
	 */
	@Test
	public void testUserDelete() {
		try {
			ResultActions response = this.deleteJsonRequestMock(ROOT + Urls.User.V1_USER.replace("{id}", "1"),
					null, headers);
			this.check(response);
		} catch (Exception e) {
			Assert.fail(e.getMessage());
		}
	}

	/**
	 * 测试更新用户
	 */
	@Test
	public void testUserUpdate() {
		try {
			UserDTO user = new UserDTO();
			user.setUserName("zhangsan9");
			user.setUserPassword("123456");
			ResultActions response = this.putJsonRequestMock(ROOT + Urls.User.V1_USER.replace("{id}", "1"), user,
					headers);
			this.check(response);
		} catch (Exception e) {
			Assert.fail(e.getMessage());
		}
	}

	/**
	 * 测试查询用户
	 */
	@Test
	public void testQueryUser() {
		try {
			ResultActions response = this.getJsonRequestMock(ROOT + Urls.User.V1_USER.replace("{id}", "170927312399761408"), null,
					headers);
			this.check(response);
		} catch (Exception e) {
			Assert.fail(e.getMessage());
		}
	}

}