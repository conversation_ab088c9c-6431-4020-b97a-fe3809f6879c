/**
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.web.controller;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.welab.common.response.Response;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.springboot.web.annotation.OrgParam;
import com.welab.springboot.web.annotation.UserParam;
import com.welab.springboot.web.holder.RequestHolder;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @description
 *
 * <AUTHOR>
 * @date 2018-01-24 19:12:02
 * @version v1.0
 */

@Slf4j
@RestController
@Validated
@Api(value = "ProductController", description = "产品Controller")
@RequestMapping
public class ProductController {

	/**
	 * Demo:用户id和机构id的获取方法一<br>
	 * 原理:使用@UserParam和@OrgParam的使用，用于绑定参数userId和orgId<br>
	 * 优缺点:与spring @RequestParam类似，可在注解上配置参数校验，但代码比较烦琐<br>
	 * 附：经过权限校验的请求http header都有userId和orgId值<br>
	 * 已在swagger全局参数增加，所以增加@ApiIgnore进行忽略
	 * 
	 * @param userId
	 *            用户id
	 * @param orgId
	 *            机构id
	 * @return
	 */
	@ApiOperation(value = Urls.Product.V1_PRODUCTS, notes = Urls.Product.V1_QUERY_PRODUCTS_DESC)
	@GetMapping(value = Urls.Product.V1_PRODUCTS)
	public Response<List<Object>> queryProducts(@ApiIgnore @UserParam Long userId, @ApiIgnore @OrgParam Long orgId) {
		log.info("this is for test...");
		log.info("用户id:{}", userId);
		log.info("机构id:{}", orgId);

		Response<List<Object>> response = new Response<>();
		return response;
	}

	/**
	 * Demo:用户id和机构id的获取方法二<br>
	 * 原理:在http filter解析userId和orgId至RequestHolder对象中<br>
	 * 优缺点:service层也很方便获取userId和orgId，不需要参数传递，但使用时最好进行userId非空判断
	 * 
	 * @return
	 */
	@ApiOperation(value = Urls.Product.V1_BUY_PRODUCT, notes = Urls.Product.V1_BUY_PRODUCT_DESC)
	@PostMapping(value = Urls.Product.V1_BUY_PRODUCT)
	public Response<List<Object>> buyProduct() {
		log.info("this is for test...");
		log.info("用户id:{}", RequestHolder.getUserId());
		log.info("机构id:{}", RequestHolder.getOrgId());

		Response<List<Object>> response = new Response<>();
		return response;
	}
}
