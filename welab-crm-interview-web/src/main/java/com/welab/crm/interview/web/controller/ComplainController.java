package com.welab.crm.interview.web.controller;

import com.welab.collection.interview.dto.complain.ComplainResultDTO;
import com.welab.common.response.Response;
import com.welab.crm.interview.service.ComplainOrderService;
import com.welab.crm.interview.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 催收系统推送客服投诉的处理结果
 *
 * <AUTHOR>
 * @date 2022/06/07 11:58
 */

@Slf4j
@RestController
@RequestMapping(Urls.Complain.ROOT)
@Api(value = "ComplainController", description = "投诉结果处理ComplainController")
public class ComplainController {

    @Resource
    private ComplainOrderService complainOrderService;

    /**
     * 催收系统推送客服投诉的处理结果
     */
    @ApiOperation(value = Urls.Complain.V1_COMPLAIN_UPDATE_RESULT_DESC, notes = Urls.Complain.V1_COMPLAIN_UPDATE_RESULT_DESC)
    @PostMapping(value = Urls.Complain.V1_COMPLAIN_UPDATE_RESULT)
    public Response<Void> updateComplainResult(@RequestBody ComplainResultDTO resultDTO) {
        log.info("updateComplainResult param: {}", resultDTO);
        complainOrderService.returnResultForUpdate(resultDTO);
        return Response.success();
    }
}
