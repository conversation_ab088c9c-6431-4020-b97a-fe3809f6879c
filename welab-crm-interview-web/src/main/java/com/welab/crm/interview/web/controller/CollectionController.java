package com.welab.crm.interview.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.welab.common.response.Response;
import com.welab.crm.interview.service.ConPhoneSummaryService;
import com.welab.crm.interview.service.KfPartnerInfoService;
import com.welab.crm.interview.service.WorkOrderService;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.vo.online.OnlineHistoryModel;
import com.welab.crm.interview.vo.partnerInfo.PartnerInfoVO;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.web.HttpClients;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 提供给催收系统的接口
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping(Urls.Collection.ROOT)
@Api(value = "CollectionController", description = "提供给催收系统调用的接口")
public class CollectionController {

    @Resource
    private WorkOrderService workOrderService;
    
    @Resource
    private ConPhoneSummaryService conPhoneSummaryService;
    
    @Resource
    private KfPartnerInfoService kfPartnerInfoService;

    /**
     * 供催收系统查询工单备用手机号
     */
    @ApiOperation(value = Urls.Collection.V1_MOBILE_BAK_QUERY_DESC, notes = Urls.Collection.V1_MOBILE_BAK_QUERY_DESC)
    @GetMapping(value = Urls.Collection.V1_MOBILE_BAK_QUERY)
    public Response<List<JSONObject>> queryMobileBak(@RequestParam String mobile) {
        return Response.success(workOrderService.queryOrderMobileBak(mobile));
    }

    @ApiOperation(value = Urls.Collection.V1_ONLINE_CONTACT_QUERY_DESC, notes = Urls.Collection.V1_ONLINE_CONTACT_QUERY_DESC)
    @GetMapping(value = Urls.Collection.V1_ONLINE_CONTACT_QUERY)
    public Response<List<OnlineHistoryModel>> queryOnlineContactRecord(@RequestParam Integer userId) {
        return Response.success(conPhoneSummaryService.queryOnlineHistoryContactRecord(userId));
    }

    @ApiOperation(value = Urls.Collection.V1_PARTNER_INFO_QUERY_DESC, notes = Urls.Collection.V1_PARTNER_INFO_QUERY_DESC)
    @GetMapping(value = Urls.Collection.V1_PARTNER_INFO_QUERY)
    public Response<PartnerInfoVO> queryPartnerInfo(@RequestParam String partnerName) {
        return Response.success(kfPartnerInfoService.queryPartnerInfo(partnerName));
    }
}
