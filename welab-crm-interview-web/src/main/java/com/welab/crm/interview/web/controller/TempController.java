package com.welab.crm.interview.web.controller;

import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.ai.AiTmkConfigDTO;
import com.welab.crm.interview.dto.callback.AiCallBackDTO;
import com.welab.crm.interview.service.AiPushJobService;
import com.welab.crm.interview.service.AiPushService;
import com.welab.crm.interview.service.RepayLinkService;
import com.welab.crm.interview.service.WorkOrderService;
import com.welab.crm.interview.service.impl.FinanceServiceImpl;
import com.welab.crm.interview.vo.callback.AiCallBackVo;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.domain.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 及时改变ai外呼推送任务定时器的时间设定
 *
 * <AUTHOR>
 * @date 2022/03/03 11:58
 * @module 客服项目
 */

@Slf4j
@RestController
@RequestMapping("/temp")
@Api(value = "TempController", description = "临时接口")
public class TempController {

    @Resource
    private FinanceServiceImpl financeService;

    @Resource
    private WorkOrderService workOrderService;
    
    @Resource
    private RepayLinkService repayLinkService;
    
    /**
     * 更新部分异常的代扣数据
     * @return
     */
    @GetMapping(value = "/withholdUpdate")
    public Response<Void> withholdUpdate(@Param("startTime") String startTime, @Param("endTime") String endTime) {
        financeService.syncWithholdRecordTemp(startTime, endTime);
        return Response.success();
    }


    @GetMapping(value = "/orderUpdate")
    public Response<Void> orderUpdate() {
        workOrderService.batchUpdateWoTaskSucLoanCount();
        return Response.success();
    }


    @GetMapping(value = "/h5/repay-link/update")
    public Response<Void> h5repayLinkUpdateRepayResult(@RequestParam String startTime, @RequestParam String endTime) {
        Date startDate = DateUtil.stringToDate(startTime);
        Date endDate = DateUtil.stringToDate(endTime);
        for (int i = 0; i < DateUtil.getDaysBetween(startDate, endDate); i++) {
            Date queryDate = DateUtil.plusDays(startDate, i);
            repayLinkService.saveRepayResult(queryDate);
            
        }
        
        return Response.success();
    }

    @GetMapping(value = "/order-notice/over-twenty-four-hour")
    public Response<Void> orderNoticeOverTwentyFourHour() {
        workOrderService.queryOverTwentyFourHourUnContactOrderAndNotice();
        return Response.success();
    }

}