/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.web.constants;

/**
 * @description 所有请求URL
 *
 * <AUTHOR> 请填写作者相关信息。例如：jason.zhuo
 * @date TODO 请填写时间。例如：2017-12-27 17:11:01
 * @version v1.0
 */

public interface Urls {

	/**
	 * 1.0版本接口
	 */
	String V1 = "/v1";

	interface User {
		String ROOT = V1 + "/user";

		String V1_USER_REGISTER = V1 + "/user";
		String V1_USER_REGISTER_DESC = "用户注册";

		String V1_USER = V1 + "/user/{id}";
		String V1_USER_QUERY_DESC = "查询单个用户";
		String V1_USER_UPDATE_DESC = "用户更新";
		String V1_USER_DELETE_DESC = "用户删除";

		String V1_USERS = V1 + "/users";
		String V1_USERS_QUERY_DESC = "分页查询用户列表";

		String V1_ALL_USER_QUERY = "/queryByMobile";
		String V1_ALL_USER_QUERY_DESC = "根据手机号查询用户全部信息,给创研调用";

		String V1_card_USER_QUERY = "/privilegeUser";
		String V1_card_USER_QUERY_DESC = "根据手机号查询用户是否属于拥有特权卡的用户类型";

		String V1_VIP_USER = "/vipUser";
		String V1_VIP_USER_DESC = "根据手机号查询是否是vip 客户";


		String V1_OVERDUE_USER = "/overdue-user";
		String V1_OVERDUE_USER_DESC = "根据手机号判断客户是否逾期";
		
		
		String V1_QUERY_USER_BY_MOBILE = "/query-by-mobile";
		String V1_QUERY_USER_BY_MOBILE_DESC = "根据手机号查询用户信息";

		String V1_QUERY_USER_BY_MOBILE_NO_AUTH = "/query-by-mobile/no-auth";
		String V1_QUERY_USER_BY_MOBILE_NO_AUTH_DESC = "根据手机号查询用户信息无权限校验";


		String V1_SEND_TRANSFER_SMS = "/send-sms";
		String V1_SEND_TRANSFER_SMS_DESC = "发送债转贷款信息短信";
	}

	interface Product {

		String V1_PRODUCTS = V1 + "/products";
		String V1_QUERY_PRODUCTS_DESC = "查询产品列表";

		String V1_BUY_PRODUCT = V1 + "/buy-product";
		String V1_BUY_PRODUCT_DESC = "购买产品";

	}


	interface PhoneRecord {

		String V1_PHONE = V1 + "/phone";
		String V1_PHONE_CALL_BACK = "/callBack";
		String V1_PHONE_CALL_BACK_DESC = "软电话回调";

		String V1_PHONE_RECORD_QUERY = "/kfRecord/query";
		String V1_PHONE_RECORD_QUERY_DESC = "查询通话记录";
		
		String V1_PHONE_REPORT_MANUAL = "/report/manual";
		String V1_PHONE_REPORT_MANUAL_DESC = "报表手动触发";

		String V1_PHONE_QUALITY_CALLBACK = "/quality/callback";
		String V1_PHONE_QUALITY_CALLBACK_DESC = "质检回调接口";
	}

	interface SatisfactionVerify {

		String V1_SATISFACTION_VERIFY = V1 + "/satisfaction/verify";
		String V1_SATISFACTION_VERIFY_DESC = "满意度调查";
	}

	interface AI {
		String ROOT = V1 + "/sale/ai";
		String V1_AI_UPDATE_CRON = "/cron";
		String V1_AI_UPDATE_CRON_DESC = "改变cron表达式让定时任务及时生效";

		String V1_AI_CALLBACK = "/callback";
		String V1_AI_CALLBACK_DESC = "ai外呼voice-ivr回调接口";
	}

	interface Complain {
		String ROOT = V1 + "/complain";
		String V1_COMPLAIN_UPDATE_RESULT = "/update/result";
		String V1_COMPLAIN_UPDATE_RESULT_DESC = "催收系统返回待更新的投诉核实和处理结果";
	}

	interface Collection {
		String ROOT = V1 + "/collection";
		String V1_MOBILE_BAK_QUERY = "/mobile_bak/query";
		String V1_MOBILE_BAK_QUERY_DESC = "查询客服工单备用号码(提供给催收系统使用)";
		
		
		String V1_ONLINE_CONTACT_QUERY = "/online/contact/query";
		String V1_ONLINE_CONTACT_QUERY_DESC = "查询在线系统联系记录(供催收系统查询)";

		String V1_PARTNER_INFO_QUERY = "/partner-info/query";
		String V1_PARTNER_INFO_QUERY_DESC = "/partner-info/query";
	}


	interface Video {
		String ROOT = V1 + "/video";
		String V1_READ_MSG = "/readMsg";
		String V1_READ_MSG_DESC = "获取需要客户读的信息";
		String V1_UPLOAD = "/upload";
		String V1_UPLOAD_DESC = "上传客户录制的视频";
	}
	
	interface Lender {
		String ROOT = V1 + "/lender";
		String V1_REDUCE_REFUND_CALLBACK = "/reduce-refund/callback";
		String V1_REDUCE_REFUND_CALLBACK_DESC = "保存减免、退款回调";
		
		String V1_QUERY_CONTRACT_SEND_INFO = "/contract-send-info/{id}";
		String V1_QUERY_CONTRACT_SEND_INFO_DESC = "查询联系记录发送信息";
	}
	
	interface External{
		String ROOT = V1 + "/external";
		
		String V1_BAIRONG_SUBMIT_ORDER = "/bairong/submit-order";
		String V1_BAIRONG_SUBMIT_ORDER_DESC = "百融上传工单";

		String V1_BAIRONG_REMINDER_ORDER = "/bairong/reminder-order";
		String V1_BAIRONG_REMINDER_ORDER_DESC = "百融催单";
	}
	
	interface QaBot{
		String ROOT = V1 + "/qa-bot";
		String V1_LOAN_QUERY = "/loan";
		String V1_LOAN_QUERY_DESC = "查询贷款信息";
		
		String V1_REPAYMENT_SCHEME = "/repayment-scheme";
		String V1_REPAYMENT_SCHEME_DESC = "还款方案查询";
		
		String V1_COLLECTION_STAFF = "/collection-staff";
		String V1_COLLECTION_STAFF_DESC = "催收员工信息";
		
		String V1_TRANSFER_COMPANY = "/transfer-company";
		String V1_TRANSFER_COMPANY_DESC = "债转公司信息";
		
		String V1_VIP_ORDER = "/vip-order";
		String V1_VIP_ORDER_DESC = "VIP订单信息";
	}
}
