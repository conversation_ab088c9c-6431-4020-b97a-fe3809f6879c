package com.welab.crm.interview.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.collection.interview.service.ComplainService;
import com.welab.collection.interview.service.FaSuPushService;
import com.welab.collection.interview.service.ReduceSchemeService;
import com.welab.collection.interview.vo.CollectionStaffVO;
import com.welab.collection.interview.vo.legal.FaSuPushVO;
import com.welab.collection.interview.vo.reduceScheme.ReduceSchemeVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.bo.OpDictInfoBO;
import com.welab.crm.interview.dto.vip.SVipQueryDTO;
import com.welab.crm.interview.enums.UserStateEnum;
import com.welab.crm.interview.enums.VipStatus;
import com.welab.crm.interview.service.FaSuService;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.vo.fasu.FaSuVO;
import com.welab.crm.interview.vo.vip.VipInfoVO;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.finance.loanprocedure.vo.DueVO;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping(Urls.QaBot.ROOT)
@Api(value = "QaBotController", tags = "供问答机器人调用")
public class QaBotController {


	@Resource
	private UserService userService;

	@Resource
	private LoanApplicationService loanApplicationService;

	@Resource
	private ReduceSchemeService reduceSchemeService;

	@Resource
	private ComplainService complainService;

	@Resource
	private FinanceService financeService;

	@Resource
	private OpDictInfoBO opDictInfoBO;

	@Resource
	private FaSuService faSuService;

	@Resource
	private VipService vipService;
	
	@Resource
	private LoanApplicationServiceFacade loanApplicationServiceFacade;

	@ApiOperation(value = Urls.QaBot.V1_LOAN_QUERY_DESC, notes = Urls.QaBot.V1_LOAN_QUERY_DESC)
	@GetMapping(value = Urls.QaBot.V1_LOAN_QUERY)
	public Response<List<JSONObject>> queryLoans(@RequestParam String mobile) {
		UserInfo userInfo = userService.getUserInfo(mobile);
		if (userInfo == null) {
			return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode());
		} else {
			List<JSONObject> resultList = new ArrayList<>();
			List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userInfo.getUserId());
			for (LoanVO loan : loanVOList) {
				List<DueVO> totalDues = loan.getDueList();
				JSONObject result = new JSONObject();
				result.put("appNo", loan.getApplicationId());
				result.put("amount", loan.getAmount());
				
				if (org.apache.commons.collections.CollectionUtils.isNotEmpty(totalDues)) {
					//期数排序
					totalDues.sort(Comparator.comparing(DueVO::getDueDate));
					String deadDate;
					for (DueVO due : totalDues) {
						if (due.getAmount().compareTo(due.getSettledAmount()) != 0) {
							deadDate = DateUtil.dateToString(due.getDueDate(), DateUtil.TimeFormatter.YYYY_MM_DD);
							result.put("deadDate", deadDate);
							break;
						}
					}
				}
				
				resultList.add(result);
			}

			return Response.success(resultList);
		}
	}

	@ApiOperation(value = Urls.QaBot.V1_REPAYMENT_SCHEME_DESC, notes = Urls.QaBot.V1_REPAYMENT_SCHEME_DESC)
	@GetMapping(value = Urls.QaBot.V1_REPAYMENT_SCHEME)
	public Response<List<ReduceSchemeVO>> queryRepaymentScheme(@RequestParam String mobile) {
		UserInfo userInfo = userService.getUserInfo(mobile);
		if (userInfo == null) {
			return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode());
		} else {
			List<ReduceSchemeVO> reduceSchemeVOS = reduceSchemeService.queryCollectionReduceScheme(userInfo.getUserId());
			return Response.success(reduceSchemeVOS);
		}
	}

	@ApiOperation(value = Urls.QaBot.V1_COLLECTION_STAFF_DESC, notes = Urls.QaBot.V1_COLLECTION_STAFF_DESC)
	@GetMapping(value = Urls.QaBot.V1_COLLECTION_STAFF)
	public Response<List<JSONObject>> queryCollectionStaff(@RequestParam String mobile) {
		UserInfo userInfo = userService.getUserInfo(mobile);
		if (userInfo == null) {
			return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode());
		} else {
			List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userInfo.getUserId());
			List<JSONObject> resultList = new ArrayList<>();
			loanVOList.forEach(loanVO -> {
				CollectionStaffVO staffInfo = complainService.getCollectionStaffInfo(loanVO.getApplicationId());
				if (Objects.nonNull(staffInfo)) {
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("appNo", loanVO.getApplicationId());
					jsonObject.put("staffId", staffInfo.getStaffId());
					jsonObject.put("amount", loanVO.getAmount());
					resultList.add(jsonObject);
				}
			});
			return Response.success(resultList);
		}


	}

	@ApiOperation(value = Urls.QaBot.V1_TRANSFER_COMPANY_DESC, notes = Urls.QaBot.V1_TRANSFER_COMPANY_DESC)
	@GetMapping(value = Urls.QaBot.V1_TRANSFER_COMPANY)
	public Response<List<JSONObject>> queryTransferCompany(@RequestParam String mobile) {
		UserInfo userInfo = userService.getUserInfo(mobile);
		if (userInfo == null) {
			return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode());
		} else {
			List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userInfo.getUserId());
			if (CollectionUtils.isEmpty(loanVOList)) {
				return Response.success();
			}
			List<JSONObject> resultList = new ArrayList<>();

			Map<String, FaSuVO> faSuVOMap = faSuService.listFaSuData(loanVOList.stream().map(LoanVO::getApplicationId).collect(Collectors.toList()));

			// 查询 loan_application 表中状态为CLOSED的贷款
			List<String> closeLoanList = loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(userInfo.getUuid(),
							Collections.singletonList(LoanApplicationStateEnum.CLOSED.getValue()))
					.stream().map(LoanApplicationDTO::getApplicationId).collect(Collectors.toList());

			loanVOList.forEach(loanVO -> {

				if (closeLoanList.contains(loanVO.getApplicationId())) {
					return;
				}
				if (faSuVOMap.containsKey(loanVO.getApplicationId())) {
					FaSuVO faSuVO = faSuVOMap.get(loanVO.getApplicationId());
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("appNo", loanVO.getApplicationId());
					jsonObject.put("amount", loanVO.getAmount());
					jsonObject.put("telNo", faSuVO.getCompanyTel());
					resultList.add(jsonObject);
					return;
				}
				WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(loanVO.getApplicationId());
				if (Objects.nonNull(writeoffDTO)) {
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("appNo", loanVO.getApplicationId());
					jsonObject.put("companyName", writeoffDTO.getCompanyName());
					jsonObject.put("amount", loanVO.getAmount());
					jsonObject.put("telNo",
							opDictInfoBO.queryValidDictMap("loan_transfer_dept_mobile", writeoffDTO.getCompanyName())
									.get(writeoffDTO.getCompanyName()));
					resultList.add(jsonObject);
				}
			});
			return Response.success(resultList);
		}


	}


	@ApiOperation(value = Urls.QaBot.V1_VIP_ORDER_DESC, notes = Urls.QaBot.V1_VIP_ORDER_DESC)
	@GetMapping(value = Urls.QaBot.V1_VIP_ORDER)
	public Response<List<VipInfoVO>> queryVipOrder(@RequestParam String mobile) {
		UserInfo userInfo = userService.getUserInfo(mobile);
		if (userInfo == null) {
			return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode());
		} else {
			SVipQueryDTO queryDTO = new SVipQueryDTO();
			queryDTO.setUuid(userInfo.getUuid());
			queryDTO.setUserId(userInfo.getUserId());
			List<VipInfoVO> vipInfoList = vipService.getVipInfoList(queryDTO)
					.stream().filter(vipInfoVO -> VipStatus.PaymentStatusEnum.SUCCEED.getText().equals(vipInfoVO.getPaymentStatus())
							&& (StringUtils.isBlank(vipInfoVO.getRefundStatus()) || !VipStatus.RefundStatusEnum.SUCCEED.getText().equals(vipInfoVO.getRefundStatus())))
					.collect(Collectors.toList());
			return Response.success(vipInfoList);
		}


	}


}