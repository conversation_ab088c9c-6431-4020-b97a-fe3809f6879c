package com.welab.crm.interview.web.controller;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.constant.SmsKeyConstant;
import com.welab.crm.interview.domain.CsVideoCheck;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.CsVideoCheckMapper;
import com.welab.crm.interview.service.VideoCheckService;
import com.welab.crm.interview.util.FfmpegUtils;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.welab.common.response.Response;
import com.welab.crm.interview.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.JedisCommands;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Date;
import java.util.Objects;

/**
 * 视频验证相关服务
 * 
 * <AUTHOR>
 * @module 客服项目
 */

@Slf4j
@RestController
@RequestMapping(Urls.Video.ROOT)
@Api(value = "VideoCheckController", description = "视频验证相关服务")
public class VideoCheckController extends BaseController {

    @Value("${video.upload.path}")
    private String videoPath;

    @Resource
    private VideoCheckService videoCheckService;

    @Resource
    private JedisCommands jedisCommands;

    @Resource
    private CsVideoCheckMapper csVideoCheckMapper;

    @ApiOperation(value = Urls.Video.V1_READ_MSG_DESC, notes = Urls.Video.V1_READ_MSG_DESC)
    @GetMapping(value = Urls.Video.V1_READ_MSG)
    public Response<String> getReadMsg(@RequestHeader("token") String token) {
        return Response.success(videoCheckService.getReadMsg(token));
    }

    @ApiOperation(value = Urls.Video.V1_UPLOAD_DESC, notes = Urls.Video.V1_UPLOAD_DESC)
    @PostMapping(value = Urls.Video.V1_UPLOAD)
    public Response<Void> uploadVideo(@RequestHeader("token") String token, @RequestPart("file") MultipartFile file,
        @RequestParam("startTime") Date startTime, @RequestParam("endTime") Date endTime) throws IOException {
        // 限制上传
        limitUpload(token);
        byte[] bytes = file.getBytes();
        // 把视频保存到本地，并且重新编码
        byte[] data = saveFileToLocalAndCode(file, token);
        videoCheckService.uploadVideoAndPushToAi(data, bytes, token, startTime, endTime);
        return Response.success();
    }

    private byte[] saveFileToLocalAndCode(MultipartFile file, String token) throws IOException {
        // 把文件保存到本地
        // 创建上传目录
        File uploadDir = new File(videoPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        String filename = token + ".mp4";
        String oldFilePath = videoPath + filename;
        File oldFile = new File(videoPath + filename);
        file.transferTo(oldFile);

        String newFileName = "fix_" + token + ".mp4";
        String newFilePath = videoPath + newFileName;

        // 视频重新编码
        FfmpegUtils.videoReCode(oldFilePath, newFilePath);

        // 创建文件对象
        File newFile = new File(newFilePath);
        // 获取文件长度
        long length = newFile.length();

        // 创建字节数组
        byte[] data = new byte[(int)length];
        // 创建字节输入流
        try (FileInputStream fis = new FileInputStream(newFile)) {
            // 读取文件内容
            int offset = 0;
            int numRead = 0;
            while (offset < data.length && (numRead = fis.read(data, offset, data.length - offset)) >= 0) {
                offset += numRead;
            }

            // 确认所有数据均已读取
            if (offset != data.length) {
                throw new IOException("无法完整读取文件：" + file.getName());
            }
            
            Files.delete(oldFile.toPath());
            Files.delete(newFile.toPath());
        }

        
        return data;
    }

    private void limitUpload(String token) {
        if (StringUtils.isBlank(token)) {
            log.warn("uploadVideoAndPushToAi token校验失败");
            throw new CrmInterviewException("链接已过期");
        }

        log.info("uploadVideoAndPushToAi start,token:{}", token);

        String uploadTime = jedisCommands.get(SmsKeyConstant.REDIS_PRE_UPLOAD_KEY + token);
        if (StringUtils.isBlank(uploadTime) || Integer.parseInt(uploadTime) <= 0) {
            throw new CrmInterviewException("链接已过期或者已有重复上传视频");
        }

        CsVideoCheck check =
            csVideoCheckMapper.selectOne(Wrappers.lambdaQuery(CsVideoCheck.class).eq(CsVideoCheck::getToken, token));
        if (Objects.isNull(check)) {
            log.warn("uploadVideoAndPushToAi 查不到token信息");
            throw new CrmInterviewException("上传视频失败");
        }

        if (StringUtils.isNotBlank(check.getVideoName())) {
            throw new CrmInterviewException("不允许重复上传视频");
        }
    }

}