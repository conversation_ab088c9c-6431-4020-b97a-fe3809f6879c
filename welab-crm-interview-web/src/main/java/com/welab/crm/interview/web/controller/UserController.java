package com.welab.crm.interview.web.controller;

import com.welab.collection.interview.dto.UserOverdueObject;
import com.welab.collection.interview.service.FaSuPushService;
import com.welab.collection.interview.service.IUserCenterService;
import com.welab.collection.interview.vo.legal.FaSuPushVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.bo.MessageBO;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.UserQueryDTO;
import com.welab.crm.interview.enums.UserStateEnum;
import com.welab.crm.interview.service.FaSuService;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.impl.UserInfoServiceImpl;
import com.welab.crm.interview.util.SignUtils;
import com.welab.crm.interview.vo.UserInfoForChat;
import com.welab.crm.interview.vo.fasu.FaSuVO;
import com.welab.crm.interview.vo.loan.LoanImportLabelVO;
import com.welab.crm.interview.web.constants.Urls.User;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.loancenter.interfaces.dto.VipMemberResponseDTO;
import com.welab.loancenter.interfaces.facade.NewMemberServiceFacade;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.UserServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息查询controller
 *
 * @module 客服项目
 *
 * <AUTHOR>
 * @date 2021/10/21 13:58
 */

@Slf4j
@RestController
@RequestMapping(User.ROOT)
@Api(value = "UserController", description = "用户信息查询")
public class UserController {

    @Resource
    private UserInfoServiceImpl userInfoServiceimpl;

    @Resource
    private UserServiceFacade userServiceFacade;

    @Resource
    private NewMemberServiceFacade memberServiceFacade;

    @Resource
    private IUserCenterService userCenterService;
    
    
    @Resource
    private FinanceService financeService;


    @Resource
    private FaSuPushService faSuPushService;
    
    @Resource
    private FaSuService faSuService;
    
    @Resource
    private MessageBO messageBO;
    
    
    @Resource
    private LoanApplicationService loanApplicationService;
    
    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;

    @PostMapping(User.V1_ALL_USER_QUERY)
    @ApiOperation(value = User.V1_ALL_USER_QUERY_DESC, notes = User.V1_ALL_USER_QUERY_DESC)
    public Response<List<UserInfoForChat>> phoneRecordCallBack(@RequestBody UserDetailQueryDTO dto) {
        try {
            return Response.success(userInfoServiceimpl.queryAllUserInfoByMobile(dto));
        } catch (Exception e) {
            log.error("queryByMobile error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 天润查询用户是否是特权卡用户类型接口
     */
    @ApiOperation(value = User.V1_card_USER_QUERY_DESC, notes = User.V1_card_USER_QUERY_DESC)
    @PostMapping(value = User.V1_card_USER_QUERY)
    public Response<String> getPrivilegeUser(@Validated @BeanParam UserQueryDTO queryDTO) {
        try {
            log.info("天润开始调用接口");
            if (!SignUtils.checkSign(queryDTO.getMobile(), queryDTO.getSign(), queryDTO.getTimestamp())) {
                log.info("getPrivilegeUser verify failed, param:{}", queryDTO);
                return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode(), ResponsCodeTypeEnum.PARAMETER_ERROR.getMessage(), "");
            }
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(queryDTO.getMobile());
            if (Objects.isNull(userDTO)) {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), ResponsCodeTypeEnum.DATA_NOT_EXISTS.getMessage(), "");
            }
            List<VipMemberResponseDTO> cards = memberServiceFacade.getActivePrivilegeCards(userDTO.getUuid());
            if (Objects.isNull(cards) || cards.isEmpty()) {
                return new Response<>(ResponsCodeTypeEnum.SUCCESS.getCode(), ResponsCodeTypeEnum.SUCCESS.getMessage(), "0");
            } else {
                return new Response<>(ResponsCodeTypeEnum.SUCCESS.getCode(), ResponsCodeTypeEnum.SUCCESS.getMessage(), "1");
            }
        } catch (Exception e) {
            log.warn("查询getPrivilegeUser接口异常: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(), ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(), "");
        }
    }


    @ApiOperation(value = User.V1_VIP_USER_DESC, notes = User.V1_VIP_USER_DESC)
    @RequestMapping(value = User.V1_VIP_USER, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<Integer> getVipUser(@RequestParam("mobile") String mobile,
                                        @RequestParam("timestamp") Long timestamp,
                                        @RequestParam("sign") String sign) {
        try {
            log.info("天润开始调用接口");
            if(!SignUtils.checkSign(mobile,sign,timestamp)){
                log.info("getVipUser verify failed,mobile:{},timestamp:{},sign:{}",mobile,timestamp,sign);
                return new Response(ResponsCodeTypeEnum.REQUEST_TIME_OUT.getCode(),ResponsCodeTypeEnum.REQUEST_TIME_OUT.getMessage(),"");
            }
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(mobile);
            if(Objects.isNull(userDTO)){
                return new Response(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(),ResponsCodeTypeEnum.DATA_NOT_EXISTS.getMessage(),"");
            }
            VipMemberResponseDTO dto = memberServiceFacade.getActiveVipMember(userDTO.getUuid());
            if(Objects.isNull(dto)){
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(),ResponsCodeTypeEnum.SUCCESS.getMessage(),"0");
            }
        } catch (RuntimeException e) {
            log.error("查询getVipUser接口异常", e);
            return new Response(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(),ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(),"");
        }
        return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(),"成功","1");
    }


    @ApiOperation(value = User.V1_OVERDUE_USER_DESC, notes = User.V1_OVERDUE_USER_DESC)
    @RequestMapping(value = User.V1_OVERDUE_USER, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<Integer> getOverdueUser(@RequestParam("mobile") String mobile,
        @RequestParam("timestamp") Long timestamp, @RequestParam("sign") String sign) {
        try {
            log.info("天润查询客户逾期状态");
            if (!SignUtils.checkSign(mobile, sign, timestamp)) {
                log.info("getOverdueUser verify failed,mobile:{},timestamp:{},sign:{}", mobile, timestamp, sign);
                return new Response(ResponsCodeTypeEnum.REQUEST_TIME_OUT.getCode(),
                    ResponsCodeTypeEnum.REQUEST_TIME_OUT.getMessage(), "");
            }
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(mobile);
            if (Objects.isNull(userDTO)) {
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), OVERDUE_USER_STATE.USER_NOT_EXISTS.getMsg(),
                    OVERDUE_USER_STATE.USER_NOT_EXISTS.getCode());
            }

            // 查询逾期信息
            UserOverdueObject userOverdueObject = userCenterService.queryUserOverdueState(userDTO.getId());
            if (Objects.isNull(userOverdueObject) || !userOverdueObject.getIsOverdue()) {
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), OVERDUE_USER_STATE.USER_NOT_OVERDUE.getMsg(),
                    OVERDUE_USER_STATE.USER_NOT_OVERDUE.getCode());
            } else {
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), OVERDUE_USER_STATE.USER_OVERDUE.getMsg(),
                    OVERDUE_USER_STATE.USER_OVERDUE.getCode());
            }

        } catch (RuntimeException e) {
            log.error("查询getOverdueUser接口异常", e);
            return new Response(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(), ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(),
                "");
        }
    }

    @ApiOperation(value = User.V1_QUERY_USER_BY_MOBILE_DESC, notes = User.V1_QUERY_USER_BY_MOBILE_DESC)
    @RequestMapping(value = User.V1_QUERY_USER_BY_MOBILE, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<Integer> queryUserInfo(@RequestParam("mobile") String mobile,
        @RequestParam("timestamp") Long timestamp, @RequestParam("sign") String sign) {
        try {
            log.info("天润查询客户信息");
            if (!SignUtils.checkSign(mobile, sign, timestamp)) {
                log.info("queryUserInfo verify failed,mobile:{},timestamp:{},sign:{}", mobile, timestamp, sign);
                return new Response(ResponsCodeTypeEnum.REQUEST_TIME_OUT.getCode(),
                    ResponsCodeTypeEnum.REQUEST_TIME_OUT.getMessage(), "");
            }

            return queryUserStatus(mobile, 1);


        } catch (Exception e) {
            log.error("queryUserInfo error", e);
            return new Response(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(), ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(),
                "");
        }
    }

    @ApiOperation(value = User.V1_QUERY_USER_BY_MOBILE_NO_AUTH_DESC, notes = User.V1_QUERY_USER_BY_MOBILE_NO_AUTH_DESC)
    @RequestMapping(value = User.V1_QUERY_USER_BY_MOBILE_NO_AUTH, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<Integer> queryUserInfoNoAuth(@RequestParam("mobile") String mobile) {
        try {
            return queryUserStatus(mobile, 2);
        } catch (Exception e) {
            log.error("queryUserInfoNoAuth error", e);
            return new Response(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(), ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(),
                    "");
        }
    }

    private Response<Integer> queryUserStatus(String mobile, Integer type) {
        // 查询用户是否注册
        UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(mobile);
        if (Objects.isNull(userDTO)) {
            return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), UserStateEnum.NOT_REGISTER.getMsg(),
                    UserStateEnum.NOT_REGISTER.getCode());
        }

        // 查询会员特权卡
        if (type == 1) {
            VipMemberResponseDTO dto = memberServiceFacade.getActiveVipMember(userDTO.getUuid());
            if (Objects.nonNull(dto)) {
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), UserStateEnum.VIP.getMsg(),
                        UserStateEnum.VIP.getCode());
            }
            List<VipMemberResponseDTO> cards = memberServiceFacade.getActivePrivilegeCards(userDTO.getUuid());

            if (CollectionUtils.isNotEmpty(cards)) {
                return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), UserStateEnum.VIP.getMsg(),
                        UserStateEnum.VIP.getCode());
            }
        }

//        // 查询逾期信息
//        UserOverdueObject userOverdueObject = userCenterService.queryUserOverdueState(userDTO.getId());
//        if (Objects.isNull(userOverdueObject) || !userOverdueObject.getIsOverdue()) {
//            return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), UserStateEnum.NORMAL.getMsg(),
//                    UserStateEnum.NORMAL.getCode());
//        }
        // 查询全部在途贷款
        List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userDTO.getId());

        // 查询 loan_application 表中状态为CLOSED的贷款
        List<String> closeLoanList = loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(userDTO.getUuid(),
                Collections.singletonList(LoanApplicationStateEnum.CLOSED.getValue()))
                .stream().map(LoanApplicationDTO::getApplicationId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(loanVOList)) {
            for (LoanVO loanVO : loanVOList) {
                if ("2".equals(loanVO.getWriteoff()) && !closeLoanList.contains(loanVO.getApplicationId())) {
                    return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(),
                            UserStateEnum.TRANSFER_OR_FASU.getMsg(), UserStateEnum.TRANSFER_OR_FASU.getCode());
                }
            }
            for (LoanVO loanVO : loanVOList) {
                if (loanVO.getOverdueDay() > 0 && !closeLoanList.contains(loanVO.getApplicationId())){
                    return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(),
                            UserStateEnum.OVERDUE.getMsg(), UserStateEnum.OVERDUE.getCode());
                }
            }
        }

        return new Response(ResponsCodeTypeEnum.SUCCESS.getCode(), UserStateEnum.NORMAL.getMsg(),
                UserStateEnum.NORMAL.getCode());
    }

    @ApiOperation(value = User.V1_SEND_TRANSFER_SMS_DESC, notes = User.V1_SEND_TRANSFER_SMS_DESC)
    @RequestMapping(value = User.V1_SEND_TRANSFER_SMS, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<Void> sendTransferSms(@RequestParam("callMobile") String callMobile,
        @RequestParam("registerMobile") String registerMobile, @RequestParam("timestamp") Long timestamp,
        @RequestParam("sign") String sign) {
        try {
            log.info("发送债转用户贷款信息");
            if (!SignUtils.checkSign(callMobile, sign, timestamp)) {
                log.info("sendTransferSms verify failed,callMobile:{},timestamp:{},sign:{}", callMobile, timestamp,
                    sign);
                return new Response(ResponsCodeTypeEnum.REQUEST_TIME_OUT.getCode(),
                    ResponsCodeTypeEnum.REQUEST_TIME_OUT.getMessage(), "");
            }

            // 查询用户是否注册
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(registerMobile);
            if (Objects.isNull(userDTO)) {
                return new Response(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), "注册手机号不存在", "");
            }
            StringBuilder content = new StringBuilder();
            // 查询全部在途贷款
            List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userDTO.getId());
            int index = 1;
            for (int i = 0; i < loanVOList.size(); i++) {
                LoanVO loanVO = loanVOList.get(i);
                String applicationId = loanVO.getApplicationId();
                LoanImportLabelVO importLabelInfo = loanApplicationService.getImportLabelInfo(applicationId);
                if (Objects.nonNull(importLabelInfo)) {
                    if (StringUtils.isNotBlank(importLabelInfo.getCompanyTel())) {
                        appendContent(content, index, loanVO.getAmount(), importLabelInfo.getCompanyTel(),
                            importLabelInfo.getCompanyName());
                        index++;
                    }
                    continue;
                }
                Map<String, FaSuVO> faSuMap = faSuService.listFaSuData(Collections.singletonList(applicationId));
                if (MapUtils.isNotEmpty(faSuMap)) {
                    FaSuVO faSuVO = faSuMap.get(applicationId);
                    appendContentNoCompany(content, index, loanVO.getAmount(), faSuVO.getCompanyTel());
                    index++;
                }

            }
            
            if (StringUtils.isBlank(content)){
                log.warn("sendTransferSms,用户:{} 无债转贷款或者债转信息为空", userDTO.getId());
                return new Response(ResponsCodeTypeEnum.FAILURE.getCode(), "用户无债转贷款或者债转信息为空",
                        "");
            }

            Map<String, String> params = new HashMap<>();
            params.put("content", content.toString());
            messageBO.sendSms(callMobile, "zz_yh_dx", params);

            return new Response<>(ResponsCodeTypeEnum.SUCCESS.getCode(), "短信发送成功", null);

        } catch (Exception e) {
            log.error("sendTransferSms error", e);
            return new Response(ResponsCodeTypeEnum.SYSTEM_BUSY.getCode(), ResponsCodeTypeEnum.SYSTEM_BUSY.getMessage(),
                "");
        }
    }

    private void appendContent(StringBuilder content, int index, BigDecimal amount, String companyTel, String companyName) {
        content.append("编号").append(index);
        content.append(":金额 ").append(amount);
        if (StringUtils.isNotBlank(companyTel)){
            content.append("、").append("电话 ").append(companyTel);
        }
        if (StringUtils.isNotBlank(companyName)){
            content.append("、").append("公司 ").append(companyName);
        }
        content.append(";");
    }

    private void appendContentNoCompany(StringBuilder content, int index, BigDecimal amount, String companyTel) {
        content.append("编号").append(index);
        content.append(":金额 ").append(amount);
        if (StringUtils.isNotBlank(companyTel)) {
            content.append("、").append("电话 ").append(companyTel);
        }
        content.append(";");
    }

    enum OVERDUE_USER_STATE {
        USER_OVERDUE("1","用户逾期"),
        USER_NOT_OVERDUE("2","用户不逾期"),
        USER_NOT_EXISTS("3","用户不存在");
        private String code;
        
        private String msg;

        OVERDUE_USER_STATE(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

}
