package com.welab.crm.interview.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.interview.bo.SatisfactionVerifyBO;
import com.welab.crm.interview.dto.satisfaction.SatisfactionVerifyDTO;
import com.welab.crm.interview.web.constants.Urls.SatisfactionVerify;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 满意度调查服务
 * @date 2022/2/23
 */
@Slf4j
@RestController
@Api(value = "SatisfactionVerifyController", description = "满意度调查服务")
public class SatisfactionVerifyController {

    @Resource
    private SatisfactionVerifyBO satisfactionVerifyBO;

    @PostMapping(SatisfactionVerify.V1_SATISFACTION_VERIFY)
    @ApiOperation(value = SatisfactionVerify.V1_SATISFACTION_VERIFY_DESC, notes = SatisfactionVerify.V1_SATISFACTION_VERIFY_DESC)
    public Response<Void> addRecord(@RequestBody SatisfactionVerifyDTO dto) {
        try {
            satisfactionVerifyBO.addRecord(dto);
        } catch (Exception e) {
            log.error("SatisfactionVerifyController addRecord error. {}", dto.toString(), e);
        }
        return Response.success();
    }
}
