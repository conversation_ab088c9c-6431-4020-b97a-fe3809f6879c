/**
 * @Title: BaseController.java
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.web.controller;

import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @version v1.0
 * @description Controller类的基类
 * @date 2018-04-11 10:44:50
 */

public class BaseController {

    protected HttpServletRequest baseRequest;
    protected HttpServletResponse baseResponse;
    protected HttpSession baseSession;

    @ModelAttribute public void setReqAndRes(HttpServletRequest baseRequest, HttpServletResponse baseResponse) {
        this.baseRequest = baseRequest;
        this.baseResponse = baseResponse;
        this.baseSession = baseRequest.getSession();
    }

}
