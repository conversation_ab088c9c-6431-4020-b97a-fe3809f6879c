package com.welab.crm.interview.web.controller;

import com.alibaba.fastjson.JSON;
import com.welab.collection.interview.dto.reduce.ReduceAndRefundCallbackDTO;
import com.welab.common.response.Response;
import com.welab.crm.interview.bo.ReduceAndRefundBO;
import com.welab.crm.interview.service.ContractSendService;
import com.welab.crm.interview.vo.ContractSendVO;
import com.welab.crm.interview.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 资金回调接口
 * @module 客服项目
 */

@Slf4j
@RestController
@RequestMapping(Urls.Lender.ROOT)
@Api(value = "资金回调接口")
public class LenderCallbackController {

    @Resource
    private ReduceAndRefundBO reduceAndRefundBO;
    
    @Resource
    private ContractSendService contractSendService;


    @ApiOperation(value = Urls.Lender.V1_REDUCE_REFUND_CALLBACK_DESC, notes = Urls.Lender.V1_REDUCE_REFUND_CALLBACK_DESC)
    @PostMapping(value = Urls.Lender.V1_REDUCE_REFUND_CALLBACK)
    public Response<Void> saveReduceAndRefundCallback(@RequestBody ReduceAndRefundCallbackDTO callbackDTO) {
        log.info("saveReduceAndRefundCallback:{}", JSON.toJSONString(callbackDTO));
        reduceAndRefundBO.saveReduceAndRefundCallback(callbackDTO);
        return Response.success();
    }

    @ApiOperation(value = Urls.Lender.V1_QUERY_CONTRACT_SEND_INFO_DESC, notes = Urls.Lender.V1_QUERY_CONTRACT_SEND_INFO_DESC)
    @GetMapping(value = Urls.Lender.V1_QUERY_CONTRACT_SEND_INFO)
    public Response<ContractSendVO> queryContactSendInfo(@PathVariable Long id) {
        log.info("queryContactSendInfo:{}", id);
        return Response.success(contractSendService.queryContractSendById(id));
    }

}