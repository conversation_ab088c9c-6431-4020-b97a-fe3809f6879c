package com.welab.crm.interview.web.controller;

import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.dto.BairongReminderOrderReqDTO;
import com.welab.crm.interview.dto.BairongSubmitOrderReqDTO;
import com.welab.crm.interview.service.BairongOrderService;
import com.welab.crm.interview.util.BairongRequestVO;
import com.welab.crm.interview.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Slf4j
@RestController
@RequestMapping(Urls.External.ROOT)
@Api(value = "ExternalController", tags = "外部供应商调用接口")
public class ExternalController {

	@Value("${bairong.public.key}")
	private String bairongPublicKey;

	@Value("${bairong.private.key}")
	private String bairongPrivateKey;

	@Value("${bairong.aes.key}")
	private String bairiongAesKey;
	
	private static final long EXPIRE_SECONDS = 300;

	@Resource
	private BairongOrderService bairongOrderService;

	@ApiOperation(value = Urls.External.V1_BAIRONG_SUBMIT_ORDER_DESC, notes = Urls.External.V1_BAIRONG_SUBMIT_ORDER_DESC)
	@PostMapping(value = Urls.External.V1_BAIRONG_SUBMIT_ORDER)
	public Response<Void> bairongSubmitOrder(@Valid @RequestBody BairongRequestVO<BairongSubmitOrderReqDTO> bairongRequestVO) {
		if (!bairongRequestVO.checkSign(bairongPublicKey)) {
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "签名校验失败", null);
		}
		if (bairongRequestVO.expire(EXPIRE_SECONDS)){
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "请求已过期", null);
		}
		BairongSubmitOrderReqDTO dto = bairongRequestVO.toObject(BairongSubmitOrderReqDTO.class, bairiongAesKey);
		try {
			bairongOrderService.submitOrder(dto);
			return new Response<>();
		} catch (Exception e) {
			log.error("bairongSubmitOrder error", e);
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
		}
	}

	@ApiOperation(value = Urls.External.V1_BAIRONG_REMINDER_ORDER_DESC, notes = Urls.External.V1_BAIRONG_REMINDER_ORDER_DESC)
	@PostMapping(value = Urls.External.V1_BAIRONG_REMINDER_ORDER)
	public Response<Void> bairongReminderOrder(@Valid @RequestBody BairongRequestVO<BairongReminderOrderReqDTO> bairongRequestVO) {
		if (!bairongRequestVO.checkSign(bairongPublicKey)) {
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "签名校验失败", null);
		}
		if (bairongRequestVO.expire(EXPIRE_SECONDS)){
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "请求已过期", null);
		}
		BairongReminderOrderReqDTO dto = bairongRequestVO.toObject(BairongReminderOrderReqDTO.class, bairiongAesKey);
		try {
			bairongOrderService.reminderOrder(dto);
			return new Response<>();
		} catch (Exception e) {
			log.error("bairongReminderOrder error", e);
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
		}
	}


}