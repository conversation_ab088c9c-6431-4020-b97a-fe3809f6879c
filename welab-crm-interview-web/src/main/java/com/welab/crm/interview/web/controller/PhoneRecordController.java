package com.welab.crm.interview.web.controller;

import com.welab.collection.interview.vo.PhoneRecordVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.dto.ConPhoneCallInfoDTO;
import com.welab.crm.interview.dto.QualityInspectionCallbackDTO;
import com.welab.crm.interview.service.ConphoneCallInfoService;
import com.welab.crm.interview.service.QualityInspectionService;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.web.constants.Urls.PhoneRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 软电话记录controller
 * <AUTHOR>
 * @date 2021/10/21 13:58
 * @module 客服项目
 */

@Slf4j
@RestController
@RequestMapping(PhoneRecord.V1_PHONE)
@Api(value = "PhoneRecordController", description = "软电话controller")
public class PhoneRecordController {

    @Resource
    private ConphoneCallInfoService callInfoService;
    @Resource
    private TrTokenService trTokenService;
    @Resource
    private QualityInspectionService qualityInspectionService;

    @PostMapping(PhoneRecord.V1_PHONE_CALL_BACK)
    @ApiOperation(value = PhoneRecord.V1_PHONE_CALL_BACK, notes = PhoneRecord.V1_PHONE_CALL_BACK_DESC)
    public Response<String> phoneRecordCallBack(ConPhoneCallInfoDTO conPhoneCallInfoDTO){
        return Response.success(callInfoService.saveConPhoneCallBackInfo(conPhoneCallInfoDTO));
    }

    @GetMapping(PhoneRecord.V1_PHONE_RECORD_QUERY)
    @ApiOperation(value = PhoneRecord.V1_PHONE_RECORD_QUERY_DESC, notes = PhoneRecord.V1_PHONE_RECORD_QUERY_DESC)
    public Response<List<PhoneRecordVO>> phoneRecordQuery(@RequestParam String queryDate){
        return Response.success(callInfoService.queryRecord(queryDate));
    }

    @GetMapping(PhoneRecord.V1_PHONE_REPORT_MANUAL)
    @ApiOperation(value = PhoneRecord.V1_PHONE_REPORT_MANUAL_DESC, notes = PhoneRecord.V1_PHONE_REPORT_MANUAL_DESC)
    public Response<Void> phoneReportManual(@RequestParam String countDate) {
        trTokenService.saveTrunkReportIbToDb(countDate);
        return Response.success();
    }

    @PostMapping(PhoneRecord.V1_PHONE_QUALITY_CALLBACK)
    @ApiOperation(value = PhoneRecord.V1_PHONE_QUALITY_CALLBACK_DESC, notes = PhoneRecord.V1_PHONE_QUALITY_CALLBACK_DESC)
    public Response<String> qualityInspectionCallback(@RequestBody QualityInspectionCallbackDTO callbackDTO,
                                                    HttpServletRequest request) {
        try {
            log.info("接收到阿里云质检回调，参数：{}", callbackDTO);

            // 从URL参数中获取回调信息（阿里云回调方式）
            String taskId = request.getParameter("taskId");
            String timestamp = request.getParameter("timestamp");
            String aliUid = request.getParameter("aliUid");
            String signature = request.getParameter("signature");
            String event = request.getParameter("event");

            if (StringUtils.isNotBlank(taskId)) {
                // 设置URL参数到DTO中
                callbackDTO.setTaskId(taskId);
                callbackDTO.setTimestamp(timestamp);
                callbackDTO.setAliUid(aliUid);
                callbackDTO.setSignature(signature);
                callbackDTO.setEvent(event);
            }

            qualityInspectionService.handleQualityInspectionCallback(callbackDTO);
            return Response.success("success");
        } catch (Exception e) {
            log.error("处理阿里云质检回调异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "处理失败：" + e.getMessage());
        }
    }


}
