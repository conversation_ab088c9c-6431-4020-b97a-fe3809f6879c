package com.welab.crm.interview.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.ai.AiTmkConfigDTO;
import com.welab.crm.interview.dto.callback.AiCallBackDTO;
import com.welab.crm.interview.service.AiPushJobService;
import com.welab.crm.interview.service.AiPushService;
import com.welab.crm.interview.vo.callback.AiCallBackVo;
import com.welab.crm.interview.web.constants.Urls;
import com.welab.domain.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 及时改变ai外呼推送任务定时器的时间设定
 *
 * <AUTHOR>
 * @date 2022/03/03 11:58
 */

@Slf4j
@RestController
@RequestMapping(Urls.AI.ROOT)
@Api(value = "AiPushController", description = "ai外呼controller")
public class AiPushController {

    @Resource
    private AiPushJobService aiPushJobService;

    @Resource
    private AiPushService callbackService;

    /**
     * 及时更新定时器的设置使得任务按照新的定时规则推送
     *
     * @param configDTO 新的cron表达式
     */
    @ApiOperation(value = Urls.AI.V1_AI_UPDATE_CRON_DESC, notes = Urls.AI.V1_AI_UPDATE_CRON_DESC)
    @PostMapping(value = Urls.AI.V1_AI_UPDATE_CRON)
    public Response<Void> updateCron(@RequestBody AiTmkConfigDTO configDTO) {
        log.info("ai push updateCron start...");
        aiPushJobService.updatePushJob(configDTO);
        log.info("ai push updateCron end.");
        return Response.success();
    }

    /**
     * 保存创研的回调数据
     *
     * @param callBackDTO 对应一个电话回调数据,包括通话时长,呼叫结果等
     */
    @ApiOperation(value = Urls.AI.V1_AI_CALLBACK_DESC, notes = Urls.AI.V1_AI_CALLBACK_DESC)
    @PostMapping(value = Urls.AI.V1_AI_CALLBACK)
    public ResponseVo<?> saveCallbackData(@RequestBody AiCallBackDTO callBackDTO) {
        String phoneNo = callBackDTO.getPhoneNo();
        String coverPhone = phoneNo.substring(0, 3) + "****" + phoneNo.substring(7);
        log.info("ai callback saveCallbackData start, param : phone-{}, extraParam-{}",
                coverPhone, callBackDTO.getExtraParam());
        if(StringUtils.isBlank(callBackDTO.getExtraParam())){
            // 当用户在创研界面手动导入打电话数据时,也会发生回调,这样的数据不保存从而也不计入ai报表
            return new ResponseVo<>();
        }
        long startTime = System.currentTimeMillis();
        AiCallBackVo backVo = callbackService.saveCallbackData(callBackDTO);
        log.info("ai callback saveCallbackData end, take time {}ms", System.currentTimeMillis() - startTime);
        return new ResponseVo<>(backVo);
    }
}