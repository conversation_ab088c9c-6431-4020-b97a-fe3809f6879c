/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import lombok.extern.slf4j.Slf4j;

/**
 * @description 项目服务启动类
 *
 * <AUTHOR> 请修改用户名，如：dawn.deng
 * @date TODO 请修改时间，例如：2017-12-18 18:02:22
 * @version v1.0
 */

@SpringBootApplication
@EnableScheduling
@Slf4j
@EnableAsync
public class Application {

    public static void main(String[] args) {
        try {
            SpringApplication.run(Application.class, args);
        } catch (Exception e) {
            log.error("Start FAIL.", e);
        }
    }
}
