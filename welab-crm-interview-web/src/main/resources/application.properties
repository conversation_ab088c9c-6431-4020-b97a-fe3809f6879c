## \u628A\u9879\u76EE\u4E2D\u9700\u8981\u6839\u636E\u4E0D\u540C\u73AF\u5883\u914D\u7F6E\u4E0D\u540C\u53C2\u6570\u7684\u914D\u7F6E\u5199\u5230\u8FD9\u91CC
server.port = 8080
server.context-path=/welab-crm-interview
management.port=9090
management.context-path=/welab-crm-interview/manage
server.tomcat.compressable-mime-types = text/html,text/xml,text/plain,text/javascript,application/json,application/xml
server.tomcat.compression = 2048 # is compression enabled (off, on, or an integer content length limit)
server.tomcat.max-threads = 500
server.tomcat.uri-encoding = UTF-8

spring.http.encoding.charset = UTF-8
spring.http.encoding.enabled = true
spring.http.encoding.force = true
spring.http.multipart.maxFileSize = 5Mb

swagger.enabled = true
swagger.title = '\u6587\u6863\u670D\u52A1Restful API\u5217\u8868'
swagger.description = '\u8BE5\u5217\u8868\u662F\u6587\u6863\u670D\u52A1\u63D0\u4F9B\u7ED9\u5176\u4ED6\u7CFB\u7EDF\u8BBF\u95EE\u7684Restful API'

tomcat.accessLogEnabled = false
tomcat.backgroundProcessorDelay = 30 # secs
tomcat.basedir = /data/logs/anti-fraud/
tomcat.protocolHeader = x-forwarded-proto
tomcat.remoteIpHeader = x-forwarded-for

#endpoints.health.sensitive=false
#management.endpoints.web.exposure.include=*
management.security.enabled=false




