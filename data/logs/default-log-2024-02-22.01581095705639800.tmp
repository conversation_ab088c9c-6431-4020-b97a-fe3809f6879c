[INFO ] [default] 2024-02-22 09:42:15.370 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-02-22 09:42:15.476 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-02-22 09:42:15.529 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-02-22 09:42:15.531 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-02-22 09:42:15.532 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-02-22 09:42:15.534 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-02-22 09:42:15.536 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-02-22 09:45:13.798 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-02-22 09:45:13.905 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-02-22 09:45:13.957 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-02-22 09:45:13.959 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-02-22 09:45:13.960 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-02-22 09:45:13.962 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-02-22 09:45:13.962 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
