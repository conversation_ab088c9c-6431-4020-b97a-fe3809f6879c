[INFO ] [default] 2024-08-26 10:06:11.696 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:06:11.931 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:06:11.934 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:06:11.935 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:06:11.937 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:06:11.937 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:06:11.938 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:08:55.539 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:08:55.773 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:08:55.777 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:08:55.779 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:08:55.779 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:08:55.781 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:08:55.781 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:10:19.326 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:10:19.534 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:10:19.542 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:10:19.544 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:10:19.544 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:10:19.545 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:10:19.547 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:19:32.473 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:19:32.612 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:19:32.693 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:19:32.695 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:19:32.695 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:19:32.698 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:19:32.699 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:20:49.169 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:20:49.326 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:20:49.391 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:20:49.393 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:20:49.394 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:20:49.396 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:20:49.397 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:28:01.064 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:28:01.193 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:28:01.269 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:28:01.271 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:28:01.281 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:28:01.283 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:28:01.284 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 10:33:17.608 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 10:33:17.832 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 10:33:17.911 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 10:33:17.913 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 10:33:17.914 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 10:33:17.915 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 10:33:17.916 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
[INFO ] [default] 2024-08-26 11:05:47.774 [main] [] [] [] com.welab.common.config.DefaultConfigService - load default config from ApplicationContextInitializer
[INFO ] [default] 2024-08-26 11:05:47.946 [background-preinit] [] [] [] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 5.3.5.Final
[INFO ] [default] 2024-08-26 11:05:48.003 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider - App ID is set to welab-crm-interview by app.id property from System Property
[INFO ] [default] 2024-08-26 11:05:48.004 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Loading C:\opt\settings\server.properties
[INFO ] [default] 2024-08-26 11:05:48.004 [main] [] [] [] com.ctrip.framework.foundation.internals.provider.DefaultServerProvider - Environment is set to [local] by property 'env' in server.properties.
[INFO ] [default] 2024-08-26 11:05:48.006 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET appId = welab-crm-interview.
[INFO ] [default] 2024-08-26 11:05:48.006 [main] [] [] [] com.welab.common.config.DefaultConfigService - INFO SET ENV = LOCAL.
