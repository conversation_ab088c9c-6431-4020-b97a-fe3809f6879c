<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.welab</groupId>
    <artifactId>welab-crm-interview</artifactId>
    <version>1.1.4-RELEASE</version>
  </parent>

  <artifactId>welab-crm-interview-core</artifactId>
  <name>welab-crm-interview-core</name>
  <packaging>jar</packaging>
  <url>https://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <dependencies>

    <!-- 依赖的内部jar包 begin -->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>message-sms-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-capital-allocation-api</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.welab</groupId>
          <artifactId>welab-common2</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>marketing-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-dds</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-interview-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>user-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>application-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>product-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-loan-procedure-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-repayment-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-bank-card-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-account-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-payment-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-accounting-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-creditline-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wedefend-gateway-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>meta-space-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>document-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>loan-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-user-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>approval-center-api</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.welab</groupId>
          <artifactId>welab-springboot-actuator</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-authority-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-capital-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab.agreement</groupId>
      <artifactId>agreement-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-installment-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-repayment-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-app-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-application-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>wallet-payment-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-event</artifactId>
      <version>1.3.3-RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.mongodb</groupId>
          <artifactId>mongodb-driver</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-event-springboot-starter</artifactId>
      <version>1.3.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.welab</groupId>
          <artifactId>welab-event</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.welab</groupId>
          <artifactId>welab-mybatis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.welab.voice</groupId>
      <artifactId>voice-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-redis-springboot-starter</artifactId>
    </dependency>
    <!-- 依赖的内部jar包 end -->

    <!--加解密 begin-->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-privacy-api</artifactId>
      <version>1.0.8-RELEASE</version>
    </dependency>
    <!--加解密 end-->

    <!-- Spring Boot相关依赖 begin -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <!-- Spring Boot相关依赖 end -->

    <dependency>
      <groupId>com.dangdang</groupId>
      <artifactId>elastic-job-lite-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dangdang</groupId>
      <artifactId>elastic-job-lite-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dangdang</groupId>
      <artifactId>elastic-job-lite-lifecycle</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-recipes</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-text</artifactId>
      <version>1.9</version>
    </dependency>
    <!-- mybatis-plus  Begin -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
    </dependency>
    <!-- mybatis-plus  End -->

    <!-- 测试相关依赖 Begin -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- 测试相关依赖 end -->
    <dependency>
      <groupId>javax.el</groupId>
      <artifactId>javax.el-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.glassfish.web</groupId>
      <artifactId>javax.el</artifactId>
    </dependency>

    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itext-asian</artifactId>
      <version>5.2.0</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itextpdf</artifactId>
      <version>5.5.13</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mashape.unirest</groupId>
      <artifactId>unirest-java</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-logback</artifactId>
      <version>1.0-RELEASE</version>
    </dependency>

  </dependencies>


</project>
