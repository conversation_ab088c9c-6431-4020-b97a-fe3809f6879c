package com.welab.crm.interview.test;

import com.welab.crm.interview.dto.coupon.CouponCardDTO;
import com.welab.crm.interview.dto.coupon.CouponCardSendDTO;
import com.welab.crm.interview.dto.coupon.CouponSendLogDTO;
import com.welab.crm.interview.service.CouponService;
import com.welab.crm.interview.vo.coupon.CouponCardVO;
import com.welab.crm.interview.vo.coupon.CouponSendLogVO;
import com.welab.crm.interview.vo.coupon.CouponVO;
import com.welab.crm.interview.vo.coupon.CouponWithdrawVO;
import com.welab.marketing.vo.PagedResponse;
import com.welab.xdao.context.page.Page;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/17
 */
public class CouponServiceTest extends SimpleTest {

    @Resource
    private CouponService couponService;

    @Test
    public void getUserCardPageTest() {
        CouponCardDTO dto = new CouponCardDTO();
        //dto.setStart("2022-03-07");
        //dto.setEnd("2022-03-10");
        PagedResponse page = couponService.getUserCardPage(dto);
        if (Objects.isNull(page)) {
            System.out.println("null");
            return;
        }
        System.out.println("TotalRows=" + page.getTotal());
        System.out.println("CurrentPage=" + page.getPageNo());
        System.out.println("RowsPerPage=" + page.getPageSize());
        System.out.println("TotalPage=" + page.getPages());
        System.out.println("PageSize=" + page.getSize());
        page.getList().forEach(System.out::println);
    }

    @Test
    public void couponGlobalMonitoringTest() {
        CouponCardDTO dto = new CouponCardDTO();
        //dto.setBusinessType("loan");
        PagedResponse page = couponService.couponGlobalMonitoring(dto);
        if (Objects.isNull(page)) {
            System.out.println("null");
            return;
        }
        System.out.println("TotalRows=" + page.getTotal());
        System.out.println("CurrentPage=" + page.getPageNo());
        System.out.println("RowsPerPage=" + page.getPageSize());
        System.out.println("TotalPage=" + page.getPages());
        System.out.println("PageSize=" + page.getSize());
        page.getList().forEach(System.out::println);
    }

    @Test
    public void getCouponGlobalMonitoringListTest() {
        CouponCardDTO dto = new CouponCardDTO();
        //dto.setBusinessType("loan");
        List<CouponCardVO> list = couponService.getCouponGlobalMonitoringList(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getWithdrawCouponsTest() {
        CouponWithdrawVO vo = couponService.getWithdrawCoupons(1053734694534774784L);
        if (Objects.isNull(vo)) {
            System.out.println("null");
        }
        System.out.println(vo.toString());
    }

    @Test
    public void getUserCouponListTest() {
        List<CouponVO> list = couponService.getUserCouponList(Long.valueOf("1017932292556849152"));
        list.forEach(System.out::println);
    }

    @Test
    public void sendCouponTest() {
        CouponCardSendDTO dto = new CouponCardSendDTO();
        dto.setCouponId(Long.valueOf("113339"));
        dto.setUserId(23815551);
        dto.setUuid(Long.valueOf("1017932292556849152"));
        dto.setBusinessType("loan");
        dto.setAmount("10");
        dto.setAmountType("period_amount");
        dto.setAvailableDays(180);
        dto.setDescription("test7");
        dto.setOperationType("jgts");
        dto.setCreateStaffId("39");
        dto.setCreateStaffGroup("clz");
        List<Long> list = couponService.sendCoupon(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getSendLogTest() {
        CouponSendLogDTO dto = new CouponSendLogDTO();
        dto.setCouponId(Long.valueOf("113339"));
        dto.setBusinessType("loan");
        dto.setAmountType("period_amount");
        dto.setDescription("test7");
        dto.setOperationType("kfrx");
        dto.setGroupCode("clz");
        Page<CouponSendLogVO> page = couponService.getSendLog(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        System.out.println("PageSize=" + page.getList().size());
        page.getList().forEach(System.out::println);
    }

    @Test
    public void getSendLogListTest() {
        CouponSendLogDTO dto = new CouponSendLogDTO();
        //dto.setCouponId(Long.valueOf("113339"));
        //dto.setBusinessType("loan");
        //dto.setAmountType("period_amount");
        //dto.setDescription("test7");
        //dto.setOperationType("jgts");
        //dto.setGroupCode("wddxz");
        List<CouponSendLogVO> list = couponService.getSendLogList(dto);
        list.forEach(System.out::println);
    }
}
