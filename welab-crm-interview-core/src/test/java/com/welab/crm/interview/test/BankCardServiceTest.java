package com.welab.crm.interview.test;

import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.BairongReminderOrderReqDTO;
import com.welab.crm.interview.dto.BairongSubmitOrderReqDTO;
import com.welab.crm.interview.service.BairongOrderService;
import com.welab.crm.interview.service.BankCardService;
import com.welab.crm.interview.service.TmkUuidService;
import com.welab.crm.interview.vo.bankcard.BankCardUserVO;
import javax.annotation.Resource;

import com.welab.finance.bankcard.dubbo.BankCardDubboService;
import com.welab.finance.bankcard.vo.BankCardVO;
import com.welab.loancenter.interfaces.dto.AssetBenefitOrderResponseDTO;
import com.welab.loancenter.interfaces.facade.AssetBenefitServiceFacade;
import com.welab.wallet.payment.dubbo.WalletOrderDubboService;
import com.welab.wallet.payment.vo.WalletOrderVO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/3
 */
public class BankCardServiceTest extends SimpleTest {

    @Resource
    private BankCardService bankCardService;
    
    @Resource
    private TmkUuidService tmkUuidService;
    
    @Resource
    private BairongOrderService bairongOrderService;

    @Resource
    private AssetBenefitServiceFacade assetBenefitServiceFacade;

    @Resource(name = "walletPaymentOrderDubboService")
    private WalletOrderDubboService walletOrderDubboService;


    @Test
    public void changeBankCardTest(){
        bankCardService.changeBankCard("",77653L);
    }

    @Test
    public void getUserBankCardInfoTest(){
        BankCardUserVO userBankCardInfo = bankCardService.getUserBankCardInfo(********);
        userBankCardInfo.getBankCard().forEach(System.out::println);
    }

    @Test
    public void test01(){
        WalletOrderVO wl220613114228255418538 = walletOrderDubboService.findWalletOrderByApplicationId("WL220613114228255418538");
        System.out.println("wl220613114228255418538 = " + wl220613114228255418538);
    }
    
    @Test
    public void testRelease(){
        String s = bankCardService.releaseBankCardAuth(********, "cabank");
        System.out.println("s = " + s);
    }

    @Test
    public void testBairongPush() {
        BairongSubmitOrderReqDTO dto = new BairongSubmitOrderReqDTO();
        dto.setUniqueOrderNo("ltw20241224001");
//        dto.setUid(Arrays.asList("2209001"));
        dto.setLoanOrderId(Arrays.asList("2021","2021080400000002316"));
        dto.setContent("问题问题");
        dto.setQuestionType("银行卡问题");
        bairongOrderService.submitOrder(dto);
    }

    @Test
    public void testBairongReminder() {

        BairongReminderOrderReqDTO dto = new BairongReminderOrderReqDTO();
        dto.setUniqueOrderNo("ltw20241224001");
        dto.setContent("请尽快处理");
        bairongOrderService.reminderOrder(dto);
    }

    @Test
    public void testAssetOrder(){
        List<AssetBenefitOrderResponseDTO> assetBenefitOrderByUUid = assetBenefitServiceFacade.getAssetBenefitOrderByUUid(1146395754781298688L);
        System.out.println("assetBenefitOrderByUUid = " + assetBenefitOrderByUUid);
    }
    
}
