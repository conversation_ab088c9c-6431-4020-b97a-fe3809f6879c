package com.welab.crm.interview.test;

import com.welab.crm.base.service.StaffService;
import com.welab.crm.interview.service.LabelService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/19
 */
public class LabelTest extends SimpleTest {

    @Resource
    private LabelService labelService;
    @Resource
    private StaffService staffService;

    @Test
    public void getUserAllLabelTest() {
        Long uuid = Long.parseLong("18000001140");
        List<String> list = labelService.getUserAllLabel(uuid);
        list.forEach(System.err::println);
    }
}
