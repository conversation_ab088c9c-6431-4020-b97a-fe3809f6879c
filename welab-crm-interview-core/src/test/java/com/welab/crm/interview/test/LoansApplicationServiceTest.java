package com.welab.crm.interview.test;

import com.welab.crm.interview.dto.ApplicationOperateDTO;
import com.welab.crm.interview.service.LoansApplicationService;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021/9/30 14:12
 */
public class LoansApplicationServiceTest extends SimpleTest{

    @Resource
    private LoansApplicationService loansApplicationService;

    @Test
    public void applicationOperateTest(){
        ApplicationOperateDTO operateDTO = new ApplicationOperateDTO();
        operateDTO.setAdminId("1671385");
        operateDTO.setApplicationId("CA21093013512628113871904");
        operateDTO.setCustServiceMobile("13600000001");
        operateDTO.setType("3");
        operateDTO.setComment("取消订单test222");
        Boolean res = loansApplicationService.applicationOperate(operateDTO);
        System.out.println("res = " + res);
    }

    @Test
    public void urgentApprovalTest(){
        String s = loansApplicationService.sendUrgentApproval("21051216481587707395929");
        System.out.println("s = " + s);

    }

}
