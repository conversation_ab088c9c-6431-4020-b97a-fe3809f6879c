package com.welab.crm.interview.test;

import com.welab.crm.interview.dto.tmk.PushUuidDTO;
import com.welab.crm.interview.service.impl.TmkUuidServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/2/25 11:28
 */
public class TmkUuidServiceTest extends SimpleTest {

    @Autowired
    private TmkUuidServiceImpl tmkUuidService;


    @Test
    public void testPushUuid() {
//        PushUuidDTO pushUuidDTO = new PushUuidDTO();
//        pushUuidDTO.setPackageDefine("spencer人工触发");
//        pushUuidDTO.setType("tx");
//        pushUuidDTO.setCallType("man");
//        pushUuidDTO.setNumPackageId("op_trigger_all2022082643140");
//        List<Long> uuidList = Arrays.asList(752224867810373632L,
//                752443543762501632L,
//                1371191845639421952L,
//                751895691605667840L,
//                1371180376415797248L,
//                1371218380928843776L,
//                1371250758313312256L,
//                752226688884895744L,
//                1371529823435882496L,
//                752076390212136960L);
//        pushUuidDTO.setUuid(uuidList);
//        tmkUuidService.saveUuidListFromOss(pushUuidDTO);
    }

}
