package com.welab.crm.interview.test;

import com.welab.crm.interview.dto.bankcard.BankCardMatchDTO;
import com.welab.crm.interview.service.BankCardService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.vo.bankcard.BankChannelVO;
import com.welab.crm.interview.vo.loan.LoanApplicationVO;
import com.welab.crm.interview.vo.loan.LoanDetailsVO;
import com.welab.crm.interview.vo.loan.LoanOutstandingVO;
import com.welab.crm.interview.vo.loan.PaymentsDetailsVO;
import com.welab.crm.interview.vo.loan.RepaymentPlanVO;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
public class LoanTest extends SimpleTest {

    @Resource
    private LoanApplicationService loanApplicationService;
    @Resource
    private BankCardService bankCardService;

    @Test
    public void getLoanApplicationListTest() {
        List<LoanApplicationVO> list = loanApplicationService.getLoanApplicationList(1146395754781298688L);
        list.forEach(System.out::println);
    }

    @Test
    public void getLoanDetailsTest() {
        LoanDetailsVO loanDetailsVO = loanApplicationService.getLoanDetails("18120611193306934684977");
        System.out.println(loanDetailsVO.toString());
    }

    @Test
    public void getRepaymentPlanTest() {
        List<RepaymentPlanVO> list = loanApplicationService.getRepaymentPlan("19011710304068037478528");
        list.forEach(System.out::println);
    }

    @Test
    public void bankCardMatchTest() {
        String applicationId = "21011215594550413408287";
        LoanDetailsVO loanDetailsVO = loanApplicationService.getLoanDetails(applicationId);
        System.out.println(loanDetailsVO.toString());
        BankCardMatchDTO dto = new BankCardMatchDTO();
        dto.setApplicationId(applicationId);
        dto.setAmount(loanDetailsVO.getAmount());
        dto.setTenor(loanDetailsVO.getTenor());
        dto.setProductCode(loanDetailsVO.getProductCode());
        dto.setUserId(loanDetailsVO.getUserId());
        List<BankChannelVO> list = bankCardService.bankCardMatch(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getPaymentsDetailsTest() {
        List<PaymentsDetailsVO> list = loanApplicationService.getPaymentsDetails(232382);
        list.forEach(System.out::println);
    }

    @Test
    public void earlySettleTest() {
        loanApplicationService.earlySettle("", "QB200219144830915278971");
    }

    @Test
    public void getOutstandingLoanListTest() {
        List<LoanOutstandingVO> list = loanApplicationService.getOutstandingLoanList(********);
        list.forEach(System.out::println);
    }

}
