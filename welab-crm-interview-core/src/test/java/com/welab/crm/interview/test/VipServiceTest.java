package com.welab.crm.interview.test;

import com.alibaba.fastjson.JSON;
import com.welab.common.response.Response;
import com.welab.crm.interview.dto.vip.VipLockDTO;
import com.welab.crm.interview.service.BankCardService;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.vo.bankcard.BankCardUserVO;
import com.welab.crm.interview.vo.vip.PrivilegeCardVO;
import javax.annotation.Resource;

import com.welab.crm.interview.vo.vip.VipBenefitOrderResVO;
import com.welab.crm.interview.vo.vip.VipOperateLogVO;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/3
 */
public class VipServiceTest extends SimpleTest {

    @Resource
    private VipService vipService;

    @Test
    public void test01(){
        PrivilegeCardVO userPrivilegeCardList = vipService.getUserPrivilegeCardList(761610575439466496L);
        System.out.println("userPrivilegeCardList = " + JSON.toJSONString(userPrivilegeCardList));
    }

    @Test
    public void test02(){
        PrivilegeCardVO userPrivilegeCardList = vipService.getUserPrivilegeCardListPlus(1370559558514114560L, null);
        System.out.println("userPrivilegeCardList = " + JSON.toJSONString(userPrivilegeCardList));
    }

    @Test
    public void test03(){
        List<VipBenefitOrderResVO> vip22090717015423807437994 = vipService.getVipBenefitOrders("VIP22090717015423807437994");
        System.out.println("vip22090717015423807437994 = " + vip22090717015423807437994);
    }

    @Test
    public void test04(){
        List<VipOperateLogVO> vip22090914265827543217764 = vipService.getVipOperateLog("VIP22090914265827543217764");
        System.out.println("vip22090914265827543217764 = " + vip22090914265827543217764);
    }


    @Test
    public void test05(){
        VipLockDTO dto = new VipLockDTO();
        dto.setOperatorEmail("<EMAIL>");
        dto.setOrderNo("VIP22090915341449612535585");
        dto.setUuid("1394013406041735168");
        dto.setRefundCause("其他");
        Response<String> lock = vipService.lock(dto);
        System.out.println("lock = " + lock);
    }

    @Test
    public void test06(){
        VipLockDTO dto = new VipLockDTO();
        dto.setOperatorEmail("<EMAIL>");
        dto.setOrderNo("VIP22090915341449612535585");
        dto.setUuid("1394013406041735168");
        Response<String> lock = vipService.unlock(dto);
        System.out.println("lock = " + lock);
    }
}
