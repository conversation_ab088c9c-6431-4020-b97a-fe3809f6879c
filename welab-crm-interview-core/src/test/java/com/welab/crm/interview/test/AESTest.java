package com.welab.crm.interview.test;

import com.welab.security.util.AESUtil;
import org.junit.Test;

public class AESTest {

    @Test
    public void testBcrypt() {
        String data = "18b8f088-9624-465f-9ebc-4c2fdfee8c8b";
        String key = "we*!@796PXdya953";
        String encrypt = AESUtil.encrypt(data, key);
        System.out.println(encrypt);
        String decrypt = AESUtil.decrypt(encrypt, key);
        System.out.println(data.equals(decrypt));
    }
}
