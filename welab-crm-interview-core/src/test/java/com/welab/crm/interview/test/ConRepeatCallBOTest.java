package com.welab.crm.interview.test;

import com.welab.crm.interview.bo.ConRepeatCallBO;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.xdao.context.page.Page;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
public class ConRepeatCallBOTest extends SimpleTest {

    @Resource
    private ConRepeatCallBO conRepeatCallBO;

    @Test
    public void addRecordTest() {
        ConPhoneCallInfo info = new ConPhoneCallInfo();
        info.setCdrCalleeCno("2001");
        info.setCdrHotline("4006000799");
        info.setStaffId("40");
        info.setCdrCustomerNumber("13425190423");
        info.setCdrCallType("1");
        conRepeatCallBO.addRecord(info);
    }

    @Test
    public void queryRecordTest() {
        RepeatCallDTO dto = new RepeatCallDTO();
        dto.setStartTime("2022-04-01 00:49:46");
        dto.setEndTime("2022-04-06 14:49:46");
        dto.setHotline("10100518,4006000799");
        Page<ReportResolvedRateVO> page = conRepeatCallBO.queryRecord(dto);
        if (Objects.nonNull(page) && Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void queryRecordListTest() {
        RepeatCallDTO dto = new RepeatCallDTO();
        dto.setStartTime("2022-02-25 00:49:46");
        dto.setEndTime("2022-02-26 14:49:46");
        dto.setHotline("10100518");
        List<ReportResolvedRateVO> list = conRepeatCallBO.queryRecordList(dto);
        list.forEach(System.out::println);
    }
}
