package com.welab.crm.interview.test;

import com.welab.crm.interview.dto.wallet.WalletBillQueryDTO;
import com.welab.crm.interview.dto.wallet.WalletOrderRecordDTO;
import com.welab.crm.interview.service.WalletService;
import com.welab.crm.interview.vo.wallet.WalletLoanDueInfoVO;
import com.welab.crm.interview.vo.wallet.WalletMonthBillVO;
import com.welab.crm.interview.vo.wallet.WalletOrderRecordVO;
import com.welab.crm.interview.vo.wallet.WalletRepayRecordVO;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
public class WalletTest extends SimpleTest {

    @Resource
    private WalletService walletService;

    @Test
    public void getWalletLoanDueTest() {
        WalletLoanDueInfoVO vo = walletService.getWalletLoanDue(2428896, "2021-10");
        if (Objects.nonNull(vo)) {
            vo.getList().forEach(System.out::println);
        } else {
            System.out.println("null");
        }
    }

    @Test
    public void getMonthBillTest() {
        WalletBillQueryDTO dto = new WalletBillQueryDTO();
        dto.setUserId(Long.valueOf("2426894"));
        dto.setBillStart("2020-09");
        dto.setBillEnd("2020-11");
        List<WalletMonthBillVO> list = walletService.getMonthBill(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getWalletOrderRecordsTest() {
        WalletOrderRecordDTO dto = new WalletOrderRecordDTO();
        dto.setUserId(2380371L);
        List<WalletOrderRecordVO> list = walletService.getWalletOrderRecords(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getWalletRepayRecordsTest() {
        List<WalletRepayRecordVO> list = walletService.getWalletRepayRecords(2364270L);
        list.forEach(System.out::println);
    }

    @Test
    public void updateLoanYBStatusTest() {
        Boolean result = walletService.updateLoanYBStatus("18101613202993715065499");
        System.out.println(result);
    }

    @Test
    public void updateLoanYBStatusBatchTest() {
        List<String> applicationIdList = Arrays.asList("18101613202993715065499",
            "18080316415573093316030",
            "18080316431707840195295");
        List<String> list = walletService.allowEarlyRepayBatch(applicationIdList);
        list.forEach(System.out::println);
    }
}
