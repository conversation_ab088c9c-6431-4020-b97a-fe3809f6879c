package com.welab.crm.interview.test;

import cn.hutool.core.lang.Assert;
import com.welab.crm.interview.dto.workorder.WorkOrderNoticeDTO;
import com.welab.crm.interview.service.WorkOrderService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class WorkOrderServiceTest extends SimpleTest{

    @Resource
    private WorkOrderService workOrderService;
    @Test
    public void test01(){
//        List<String> list1 = workOrderService.queryOrderMobileBak("13077973938");
//        System.out.println("list1 = " + list1);
    }

    @Test
    public void testBatchUpdate() {
        workOrderService.orderResponseTimeOutStateUpdate();
    }
}
