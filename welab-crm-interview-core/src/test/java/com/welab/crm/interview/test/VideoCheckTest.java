package com.welab.crm.interview.test;

import javax.annotation.Resource;

import com.welab.crm.interview.service.impl.VideoCheckServiceImpl;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class VideoCheckTest extends SimpleTest {

    @Resource
    private VideoCheckServiceImpl videoCheckService;

    @Test
    public void getUserPhoto() {
        String userPhotoUrl = videoCheckService.getUserPhotoUrl(24860712);
        System.out.println("userPhotoUrl = " + userPhotoUrl);
    }
    

}
