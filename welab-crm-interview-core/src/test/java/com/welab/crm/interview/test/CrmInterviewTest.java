/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.test;

import javax.annotation.Resource;
import org.junit.Test;
import com.welab.crm.interview.service.CrmInterviewService;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class CrmInterviewTest extends SimpleTest {

    @Resource
    private CrmInterviewService crmInterviewService;


    @Test
    public void testSaveUserInfo() {
        String cdrCustomerNumber = "cn"+System.currentTimeMillis();
        Long id= crmInterviewService.AddInRobatPushRecord(cdrCustomerNumber);
        log.info("complete AddInRobatPushRecord id = {}",id);
        log.info("===============================================");
    }

    // @Resource
    // private ScaffoldService scaffoldService;
    //
    // /**
    // * 为了方便测试批量保存功能，会对数据做循环处理，这里指定为循环3次
    // */
    // private static final int SAVE_BATCH_COUNT = 3;
    //
    // /**
    // * 测试保存用户信息
    // */
    // @Test
    // public void testSaveUserInfo() {
    // UserDTO user = new UserDTO();
    // user.setUserName("赵六");
    // user.setUserPassword("123456");
    //
    // Long userId = scaffoldService.saveUserInfo(user);
    //
    // System.err.println(JSONUtil.toJson(userId));
    // }
    //
    // /**
    // * 测试批量保存用户信息
    // */
    // @Test
    // public void testSaveUserInfoBatch() {
    // List<UserDTO> userList = new ArrayList<UserDTO>();
    // for (int i = 0; i < SAVE_BATCH_COUNT; i++) {
    // UserDTO user = new UserDTO();
    // user.setUserName("niuer" + i);
    // user.setUserPassword("123456");
    // userList.add(user);
    // }
    //
    // scaffoldService.saveUserInfoBatch(userList);
    // }
    //
    // /**
    // * 测试更新用户信息示例一
    // */
    // @Test
    // public void testUpdateUserInfo() {
    // UserDTO user = new UserDTO();
    // user.setId(1L);
    // user.setUserName("zhaoliu1");
    // user.setUserPassword("654321");
    // // 没抛异常就是正常，不需要比对
    // scaffoldService.updateUserInfo(user);
    // }
    //
    // /**
    // * 测试逻辑删除用户
    // */
    // @Test
    // public void testDeleteUserInfoById() {
    // scaffoldService.deleteUserInfoById(5L);
    // }
    //
    // /**
    // * 测试根据用户id查询用户信息
    // */
    // @Test
    // public void testGetUserInfoById() {
    // UserVO userInfoVO = scaffoldService.getUserInfoById(11L);
    //
    // System.err.println(JSONUtil.toJson(userInfoVO));
    // }
    //
    // /**
    // * 测试批量查询用户信息
    // */
    // @Test
    // public void testGetUserInfoList() {
    // List<UserVO> userInfoVOList = scaffoldService.getUserInfoList(true);
    //
    // System.err.println(JSONUtil.toJson(userInfoVOList));
    // }
    //
    // /**
    // * 测试查询用户信息返回Map
    // */
    // @Test
    // public void testGetUserInfoMap() {
    // Map<Long, UserVO> userInfoMaps = scaffoldService.getUserInfoMap(170927312399761408L, true);
    //
    // System.err.println(JSONUtil.toJson(userInfoMaps));
    // }
    //
    // /**
    // * 测试查询用户信息分页返回
    // */
    // @Test
    // public void testGetUserInfoListPage() {
    // Page<UserVO> userInfoVOPage = scaffoldService.getUserInfoListPage(true, 1, 10);
    //
    // System.err.println(JSONUtil.toJson(userInfoVOPage));
    // }

}
