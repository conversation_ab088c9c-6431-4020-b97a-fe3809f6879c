package com.welab.crm.interview.test;

import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.bo.SatisfactionVerifyBO;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.dto.satisfaction.SatisfactionVerifyDTO;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import com.welab.xdao.context.page.Page;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/24
 */
public class SatisfactionVerifyBOTest extends SimpleTest {

    @Resource
    private SatisfactionVerifyBO satisfactionVerifyBO;

    @Test
    public void addRecordTest() {
        SatisfactionVerifyDTO dto = new SatisfactionVerifyDTO();
        dto.setCdr_main_unique_id("sip-29-1645754272.20662");
        dto.setCdr_enterprise_id("7600088");
        dto.setCdr_number_trunk("89175726");
        dto.setCdr_hotline("10100518");
        dto.setCdr_customer_number("13587655902");
        dto.setCdr_call_type("1");
        dto.setSv_start_time("1645754490");
        dto.setSv_end_time("1645754500");
        dto.setSv_keys("1");
        dto.setBridged_cno("6437");
        dto.setCdr_transfer(null);
        satisfactionVerifyBO.addRecord(dto);
    }

    @Test
    public void getSatisfactionReportTest() {
        RepeatCallDTO dto = new RepeatCallDTO();
        dto.setStartTime("2022-05-01 00:00:00");
        dto.setEndTime("2022-05-31 00:00:00");
        dto.setHotline("21246446");
        dto.setPeriod("range");
        Page<ReportSatisfactionVO> page = satisfactionVerifyBO.getSatisfactionReport(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getSatisfactionReportListTest() {
        RepeatCallDTO dto = new RepeatCallDTO();
        dto.setStartTime("2022-02-01 00:00:00");
        dto.setEndTime("2022-02-29 00:00:00");
        dto.setHotline("10100518");
        List<ReportSatisfactionVO> list = satisfactionVerifyBO.getSatisfactionReportList(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void DateTest() {
        long timestamp = Long.valueOf("1645754490") * 1000;
        System.err.println(DateUtil.dateToString(new Date(timestamp)));
    }
}
