package com.welab.crm.interview.test;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.bo.MessageBO;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.dto.message.MessageTemplateAddDTO;
import com.welab.crm.interview.dto.message.MessageTemplateQueryDTO;
import com.welab.crm.interview.dto.message.MessageTemplateUpdateDTO;
import com.welab.crm.interview.dto.message.SmsQueryReportDTO;
import com.welab.crm.interview.dto.message.SmsReqDTO;
import com.welab.crm.interview.service.MessageService;
import com.welab.crm.interview.util.Md5Util;
import com.welab.crm.interview.vo.message.MessageHistoryVO;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.message.sms.response.Response;
import com.welab.message.sms.service.MessageSendV4Service;
import com.welab.web.HttpClients;
import com.welab.xdao.context.page.Page;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/8
 */
public class MessageTest extends SimpleTest {

    @Resource
    private MessageSendV4Service messageSendV4Service;
    @Resource
    private MessageService messageService;
    @Resource
    private MessageBO messageBO;
    @Value("${sms.url}")
    private String smsUrl;

    /**
     * 客服系统标识
     */
    private static final String APP_TAGS = "KF.kefu";

    @Test
    public void sendMessageHttpTest() {
        SmsReqDTO smsReqDTO = new SmsReqDTO();
        String userId = "kf_ghb";
        String mobile = "18000001140";
        String reqTime = String.valueOf(new Date().getTime() / 1000);
        String secretKey = "xxx";
        String sign = mobile + secretKey + reqTime;
        sign = Md5Util.md5(sign).toUpperCase();
        String params = "{\"name\":\"诸葛亮\"}";
        Map map = JSONObject.parseObject(params);
        smsReqDTO.setMobile(mobile);
        smsReqDTO.setAppTags(APP_TAGS);
        smsReqDTO.setReqTime(reqTime);
        smsReqDTO.setSign(sign);
        smsReqDTO.setUserId(userId);
        smsReqDTO.setTemplateName("kfdx_dgx");
        smsReqDTO.setContent("尊敬的${name}，您本次提供资料不符合借款要求");
        smsReqDTO.setParams(map);
        System.out.println(JSONObject.toJSONString(smsReqDTO));
        String responseStr = HttpClients.create().setURL(smsUrl + "/open/sms/signSend")
            .addHeader("Content-Type", "application/json; charset=utf-8")
            .setRequestBody(JSONObject.toJSONString(smsReqDTO)).doPost();
        System.err.println("responseStr=" + responseStr);
        Response<String> response = JSONObject.parseObject(responseStr, Response.class);
        System.out.println(JSONObject.toJSONString(response));
        SmsQueryReportDTO smsQueryReportDTO = new SmsQueryReportDTO();
        smsQueryReportDTO.setMobile(mobile);
        smsQueryReportDTO.setReqTime(reqTime);
        smsQueryReportDTO.setRspMsgid(response.getResult());
        smsQueryReportDTO.setUserId(userId);
        smsQueryReportDTO.setSign(sign);
        String str2 = HttpClients.create().setURL(smsUrl + "/open/sms/kfdxQueryReport")
            .addHeader("Content-Type", "application/json; charset=utf-8")
            .setRequestBody(JSONObject.toJSONString(smsReqDTO)).doPost();
        System.out.println(str2);
    }

    @Test
    public void getTest() {
        Response<String> response = messageSendV4Service
            .getContentByTemplateName("AT_KEFU_YUNRONGT@KEFU_gxh_20200514120409");
        if (Objects.isNull(response)) {
            System.out.println("null");
        } else {
            System.out.println(response.getResult());
        }
    }

    @Test
    public void addMessageTemplateTest() {
        MessageTemplateAddDTO dto = new MessageTemplateAddDTO();
        dto.setTitle("测试短信2");
        dto.setSmsCode("kfdx_dgx");
        dto.setDirectory("kf_message");
        dto.setSmsType("smsType_kf");
        Long id = messageService.addMessageTemplate(dto);
        System.out.println(id);
    }

    @Test
    public void updateMessageTemplateTest() {
        MessageTemplateUpdateDTO dto = new MessageTemplateUpdateDTO();
        dto.setId(Long.valueOf("1468765024771121154"));
        dto.setSmsCode("AT_KEFU_YUNRONGT@KEFU_gxh_20200514120409");
        dto.setSmsType("smsType_kf");
        messageService.updateMessageTemplate(dto);
    }

    @Test
    public void deleteMessageTemplateTest() {
        messageService.deleteMessageTemplate(Long.valueOf("1468765024771121154"));
    }

    @Test
    public void getMessageTemplateListTest() {
        MessageTemplateQueryDTO dto = new MessageTemplateQueryDTO();
        //dto.setTitle("测试短信1");
        //dto.setDirectory("客服短信模板");
        dto.setSmsType("smsType_kf");
        Page<MessageTemplateVO> page = messageService.getMessageTemplateList(dto);
        page.getList().forEach(System.out::println);
    }

    @Test
    public void sendMessageTest() {
        MessageSendDTO dto = new MessageSendDTO();
        dto.setSendTime("2021-12-12 17:00:00");
        dto.setMessageId(1468770079661486081L);
        dto.setStaffId("1");
        dto.setName("zhangsan");
        dto.setMobile("123");
        messageBO.sendMessage(dto);
    }

    @Test
    public void getMessageHistoryTest() {
        List<MessageHistoryVO> list = messageBO.getMessageHistory("15889978184",123L);
        list.forEach(System.out::println);
    }

    @Test
    public void sendMessageImmediatelyTest() {
        messageBO.sendMessage();
    }

    @Test
    public void confirmReceivedTest() {
        messageBO.confirmReceived();
    }
}
