package com.welab.crm.interview.test;

import com.alibaba.fastjson.JSON;
import com.welab.crm.interview.service.FinanceService;
import com.welab.finance.repayment.dto.WriteoffDTO;
import org.junit.Test;

import javax.annotation.Resource;

public class FinanceServiceTest extends SimpleTest{


    @Resource
    private FinanceService financeService;


    @Test
    public void test01(){
        WriteoffDTO loanTransferByApplicationId = financeService.getLoanTransferByApplicationId("21102111271710661411923");
        System.out.println("JSON.toJSONString(loanTransferByApplicationId) = " + JSON.toJSONString(loanTransferByApplicationId));
    }
}
