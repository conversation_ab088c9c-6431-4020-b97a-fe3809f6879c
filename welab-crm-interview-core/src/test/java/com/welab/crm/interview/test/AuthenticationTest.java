package com.welab.crm.interview.test;

import com.welab.security.util.MD5Util;
import org.junit.Test;

import java.util.UUID;

public class AuthenticationTest {

    @Test
    public void test(){
        String flowCode = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis();
        String sign = MD5Util.md5("aS5u68Ke" + timestamp + flowCode);
        System.out.println(sign);
    }
}
