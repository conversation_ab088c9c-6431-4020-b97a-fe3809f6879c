package com.welab.crm.interview.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.UserInfoModifyDTO;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>

 * @date 2021/9/30 10:48
 */
public class UserInfoServiceTest extends SimpleTest{

    @Resource
    private UserInfoService userInfoService;

    @Test
    public void queryUserInfoTest(){
        UserDetailQueryDTO userDetailQueryDTO = new UserDetailQueryDTO();
//        userDetailQueryDTO.setUuid(693635002147012608L);
        userDetailQueryDTO.setMobile("");
        userDetailQueryDTO.setUserId(24874988);
        PersonalDetailsVoExpand personalDetailsVoExpand = userInfoService.queryUserInfo(userDetailQueryDTO);
        System.out.println("personalDetailsVoExpand = " + JSON.toJSONString(personalDetailsVoExpand));
    }

    @Test
    public void updateUserInfoTest(){

        UserInfoModifyDTO modifyDTO = new UserInfoModifyDTO();
        modifyDTO.setCnid("520303********1892");
        modifyDTO.setName("测试修改");
//        modifyDTO.setOldMobile("13326006545");
//        modifyDTO.setNewMobile("13326006545");
//        Boolean result = userInfoService.updateUserInfo(modifyDTO);
//        System.out.println("result = " + result);

    }

    @Test
    public void queryBlockUseInfoTest(){
        JSONObject jsonObject = userInfoService.queryBlockUserInfo("13326006545");
        System.out.println("jsonObject.toJSONString() = " + jsonObject.toJSONString());
    }

    @Test
    public void blockUserTest(){
        String res = userInfoService.blockUser("231753");
        System.out.println("res = " + res);
    }

    @Test
    public void queryWalletUserTest(){
        UserDetailQueryDTO dto = new UserDetailQueryDTO();
        dto.setUuid(642095433157447680L);
        dto.setType("PAY-QIANBAO-APP");

        WalletUserDTO walletUserDTO = userInfoService.queryWalletUser(dto);
        System.out.println("walletUserDTO = " + walletUserDTO);
    }

}
