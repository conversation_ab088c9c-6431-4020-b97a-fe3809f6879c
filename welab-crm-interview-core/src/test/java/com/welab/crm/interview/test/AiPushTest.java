package com.welab.crm.interview.test;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.agreement.service.ILoanAgreementService;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.tmk.PushUuidDTO;
import com.welab.crm.interview.dto.tmk.UuidDTO;
import com.welab.crm.interview.mapper.*;
import com.welab.crm.interview.service.impl.TmkUuidServiceImpl;
import com.welab.util.DateUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class AiPushTest extends SimpleTest {

    @Resource
    private AiTmkPushMapper pushMapper;

    @Resource
    private AiTmkCallbackMapper callbackMapper;

    @Resource
    private TmkLoanInviteMapper tmkLoanInviteMapper;

    @Resource
    private TmkUuidConfigMapper tmkUuidConfigMapper;
    
    @Resource
    private ILoanAgreementService loanAgreementService;
    
    @Resource
    private TmkUuidServiceImpl tmkUuidService;
    
    
    @Test
    public void testAgreement(){
        String s = loanAgreementService.viewLoanAgreement("23070414200326404360810");
        System.out.println("s = " + s);
    }

    @Test
    public void getHistoryByDay() {
        Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Long id = Long.valueOf("1499218915760840706");
        LambdaQueryWrapper<AiTmkPush> wrapper = Wrappers.<AiTmkPush>lambdaQuery()
                .eq(AiTmkPush::getConfigId, id).eq(AiTmkPush::getPushDay, date);
        AiTmkPush push = pushMapper.selectOne(wrapper);
        System.out.println(push);
    }

    @Test
    public void updateTmkUUId() {
        TmkUuidConfig tmkUuid = new TmkUuidConfig();
        tmkUuid.setId(148);
        tmkUuid.setCallInstruction("测试");
        tmkUuidConfigMapper.updateById(tmkUuid);
    }

    @Test
    public void insertCallback() {
        AiTmkCallback callback = new AiTmkCallback();
        callback.setConfigId(Long.valueOf("23353425345"));
        callback.setTmkTaskId("8995342534999");
        callbackMapper.insert(callback);
    }

    /**
     * 调试目前查询出的未能解密sql
     */
    @Test
    public void getUnCorrectSecurityData() throws ParseException {
        List<String> productNames = Arrays.asList("小米金融标准API-A");
        List<String> channels = Arrays.asList("on_prm_cor_23300011");
        Date approvedAtStar = DateUtils.parseDate("2022-06-10 00:00:00", DateUtils.DATE_TIME_FORMAT);
        Date approvedAtEnd = DateUtils.parseDate("2022-06-11 00:00:00", DateUtils.DATE_TIME_FORMAT);
        Integer minAmount = Integer.valueOf("1000");
        Integer maxAmount = Integer.valueOf("100000");
        String flag = "0";
        List<TmkLoanInvite> data = tmkLoanInviteMapper.selectPushData(productNames, channels,null,
                approvedAtStar, approvedAtEnd, minAmount, maxAmount, flag);
        System.out.println(data);
    }
    
    @Test
    public void testSaveUuid(){
        PushUuidDTO dto = new PushUuidDTO();
        List<UuidDTO> uuid = new ArrayList<>();
        UuidDTO u1 = new UuidDTO();
        u1.setUuid(1L);
        UuidDTO u2 = new UuidDTO();
        u2.setUuid(2L);
        UuidDTO u3 = new UuidDTO();
        u3.setUuid(3L);
        UuidDTO u4 = new UuidDTO();
        u4.setUuid(123456L);
        UuidDTO u5 = new UuidDTO();
        u5.setUuid(807634938958352384L);
        uuid.add(u1);
        uuid.add(u2);
        uuid.add(u3);
        uuid.add(u4);
        uuid.add(u5);
        dto.setUuid(uuid);
        dto.setType("132");
        dto.setCallType("132");
        dto.setPackageDefine("132");
        dto.setNumPackageId("132");
        tmkUuidService.saveUuidListFromOss(dto);
    }

}
