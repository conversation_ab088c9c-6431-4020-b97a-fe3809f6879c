package com.welab.crm.interview.test;

import cn.hutool.core.lang.Assert;
import com.welab.crm.interview.service.TrTokenService;
import org.junit.Test;

import javax.annotation.Resource;

public class TrTokenServiceTest extends SimpleTest{

    @Resource
    private TrTokenService trTokenService;


    @Test
    public void test01(){
        String token = trTokenService.getTokenByEnterpriseId("7600104");
        Assert.notBlank(token);
    }

    @Test
    public void test02(){
        trTokenService.saveTrunkReportIbToDb(null);
    }
}
