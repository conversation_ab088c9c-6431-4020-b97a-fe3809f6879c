/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.test;

import javax.annotation.Resource;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import com.welab.crm.interview.CrmInterviewServer;

/**
 * @description 测试依赖资源加载类
 *
 * <AUTHOR>
 * @date 请修改时间，例如：2018-01-03 10:29:31
 * @version v1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CrmInterviewServer.class)
public class SimpleTest extends AbstractJUnit4SpringContextTests {

	
}
