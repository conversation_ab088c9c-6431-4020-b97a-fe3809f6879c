package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.SettleProofApplyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 结清证明申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
public interface SettleProofApplyRecordMapper extends BaseMapper<SettleProofApplyRecord> {

    /**
     * 查询申请成功，但是文件路径为空的蓝海银行申请记录
     * @return
     */
    List<SettleProofApplyRecord> selectSuccessButNoFileLhRecord();
}
