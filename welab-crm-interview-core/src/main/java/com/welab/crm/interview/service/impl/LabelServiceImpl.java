package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.dto.EncryptedDTO;
import com.welab.crm.interview.dto.label.LabelNewDTO;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.LabelService;
import com.welab.crm.interview.util.AESUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.vo.EncryptedVO;
import com.welab.crm.interview.vo.label.LabelNewVO;
import com.welab.domain.vo.ResponseVo;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelServiceImpl implements LabelService {

    @Resource
    private ProfileService profileService;
    @Resource
    private UserService userService;
    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Value("${label.aes.key}")
    private String labelAesKey;

    @Value("${label.system.url}")
    private String labelUrl;

    @Override
    public List<String> getUserAllLabel(Long uuid) {
        String idNoQuery = null;
        if (uuid != null) {
            Profile profile = profileService.getProfileByUuid(uuid);
            if (!Objects.isNull(profile)) {
                idNoQuery = profile.getCnid();
            }
        }
        if (idNoQuery == null) {
            return Collections.emptyList();
        }

        String encryptParam = getEncryptParam(idNoQuery);
        try {
            String result = HttpClientUtil.doPost(labelUrl, encryptParam);
            EncryptedVO responseVo = JSON.parseObject(result, EncryptedVO.class);
            String jsonData = AESUtil.decryptData(responseVo, labelAesKey);
            ResponseVo<List<LabelNewVO>> response = JSONObject.parseObject(jsonData,
                    new TypeReference<ResponseVo<List<LabelNewVO>>>() {
                    });
            if (!"0".equals(response.getCode())) {
                log.warn("查询单个用户标签,标签系统返回错误码: {}", jsonData);
                throw new CrmInterviewException("获取标签系统数据返回错误码,uuid:" + uuid);
            }
            List<LabelNewVO> dataList = response.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                return Collections.emptyList();
            } else {
                List<String> shieldLabels = getShieldLabel();
                return dataList.stream().filter(labelNewVO -> !shieldLabels.contains(labelNewVO.getCode()))
                    .map(LabelNewVO::getName).distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("获取标签系统数据失败,uuid:{}, errMsg:{}", uuid, e.getMessage(), e);
            throw new CrmInterviewException("获取标签系统数据失败,uuid:" + uuid);
        }
    }

    /**
     * 获取需要屏蔽的标签码
     * 
     * @return
     */
    private List<String> getShieldLabel() {
        return opDictInfoMapper.getAllByCategoryAndType("shield_label", "").stream().map(OpDictInfo::getType)
            .collect(Collectors.toList());
    }

    private String getEncryptParam(String idNo) {
        LabelNewDTO labelQuery = new LabelNewDTO();
        labelQuery.setCustomer_id(idNo);
        labelQuery.setSource(8);
        EncryptedDTO encryptedDTO = new EncryptedDTO();
        encryptedDTO.setTimestamp(System.currentTimeMillis());
        encryptedDTO.setEncryptedData(AESUtil.aesEncrypt(JSON.toJSONString(labelQuery), labelAesKey));
        encryptedDTO.setSign(AESUtil.generateSign(encryptedDTO.getEncryptedData() + encryptedDTO.getTimestamp()));
        return JSON.toJSONString(encryptedDTO);
    }

}
