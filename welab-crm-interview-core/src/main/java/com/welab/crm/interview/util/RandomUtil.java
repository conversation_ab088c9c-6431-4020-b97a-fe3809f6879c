package com.welab.crm.interview.util;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 生成随机service_no
 */
public class RandomUtil {

    /**
     * 生成随机文件名：前缀+年月日时分秒+指定位随机数，共25位
     *
     * @return
     */
    public static String getRandomFileName(String prefix, int length) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);
        String rannum = getRandomStr(length);
        String serviceNo = prefix + str + rannum;
        // 当前时间
        return serviceNo;
    }

    /**
     * 获取指定位数的随机数
     *
     * @param length
     * @return
     */
    public static String getRandomStr(int length) {
        String str = "0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder res = new StringBuilder();
        for (int i = 0; i < length; i++) {
            res.append(str.charAt(random.nextInt(str.length())));
        }
        return res.toString();
    }

    public static void main(String[] args) {
        String fileName = RandomUtil.getRandomFileName("KF", 11);
        System.out.println(fileName);
    }
}
