package com.welab.crm.interview.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.capital.dto.CapitalVo;
import com.welab.capital.provider.CapitalRuleProvider;
import com.welab.collection.interview.utils.AesUtils;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.bankcard.BankCardMatchDTO;
import com.welab.crm.interview.enums.BankCardStatusEnum;
import com.welab.crm.interview.enums.ChannelCodeEnum;
import com.welab.crm.interview.enums.PartnerCodeEnum;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.service.BankCardService;
import com.welab.crm.interview.util.AESUtil;
import com.welab.crm.interview.util.BankNameUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.bankcard.*;
import com.welab.finance.accounting.dto.QueryBatchAccountDTO;
import com.welab.finance.accounting.dubbo.FinanceAccountsDubboService;
import com.welab.finance.accounting.vo.AccountVO;
import com.welab.finance.bankcard.dto.BankCardUnbindDTO;
import com.welab.finance.bankcard.dto.BankCardVerifyDTO;
import com.welab.finance.bankcard.dubbo.BankCardDubboService;
import com.welab.finance.bankcard.dubbo.BankDubboService;
import com.welab.finance.bankcard.vo.BankCardVO;
import com.welab.finance.bankcard.vo.BankCardVerifyVO;
import com.welab.finance.bankcard.vo.BankVO;
import com.welab.finance.capital.dto.ChannelsDTO;
import com.welab.finance.capital.dubbo.CapitalMatchDubboService;
import com.welab.finance.capital.vo.ForecastResultVO;
import com.welab.finance.capital.vo.MatchChannelVO;
import com.welab.support.credit.dto.GetUserQuotaReq;
import com.welab.support.credit.dto.GetUserQuotaResp;
import com.welab.support.credit.service.QuotaService;
import com.welab.user.interfaces.dto.NotesDTO;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.NotesServiceFacade;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.AdminService;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import com.welab.wdfgateway.service.IWeDefendService;
import com.wolaidai.appcenter.service.LoanApplicationServiceInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/22
 */
@Slf4j
@Service
public class BankCardServiceImpl implements BankCardService {

    @Resource
    private UserServiceFacade userServiceFacade;
    @Resource
    private FinanceAccountsDubboService financeAccountsDubboService;
    @Resource
    private UserService userService;
    @Resource
    private BankCardDubboService bankCardDubboService;
    @Resource
    private BankDubboService bankDubboService;
    @Resource
    private LoanApplicationServiceInterface loanApplicationServiceInterface;
    @Resource
    private QuotaService quotaService;
    @Resource
    private ProfileService profileService;
    @Resource
    private IWeDefendService iWeDefendService;
    @Resource
    private AdminService adminService;
    @Resource
    private NotesServiceFacade notesServiceFacade;
    @Resource
    private CapitalMatchDubboService capitalMatchDubboService;
    @Resource
    private CapitalRuleProvider capitalRuleProviderImpl;
    @Value("${welab.query.origin.name}")
    private String originNameUrl;
    @Value("${welab.ca.bank.investment}")
    private String caBankInvestment;
    @Value("${bank.release.url}")
    private String bankReleaseUrl;

    /**
     * 银行卡解除授权类型：信用卡
     */
    private static final String CREDIT_CARD_AUTH_TYPE = "credit_card";

    /**
     * @see BankCardService#getUserBankCardInfo(Integer)
     */
    @Override
    public BankCardUserVO getUserBankCardInfo(Integer userId) {
        BankCardUserVO bankCardUserVO = new BankCardUserVO();
        if (Objects.isNull(userId)) {
            return bankCardUserVO;
        }
        bankCardUserVO.setChannelInfoVO(buildChannelInfo(userId));
        bankCardUserVO.setBankCard(buildBankCardInfoVOList(userId));
        getUserQuota(bankCardUserVO, userId);
        List<BankCardInfoVO> bankCardInfoVOList = bankCardUserVO.getBankCard();
        // 银行卡账号脱敏
        bankCardInfoVOList = bankCardInfoVOList.stream()
            .map(bankCardInfoVO -> {
                bankCardInfoVO.setAccountEncrypt(AesUtils.encrypt(bankCardInfoVO.getAccount()));
                bankCardInfoVO.setAccount(StringUtil.hideBankCard(bankCardInfoVO.getAccount()));
                if (Objects.isNull(bankCardInfoVO.getIsDefault())){
                    bankCardInfoVO.setIsDefault(false);
                }
                return bankCardInfoVO;
            }).sorted(Comparator.comparing(BankCardInfoVO::getIsDefault)
                        .thenComparing(BankCardInfoVO::getStatus)
                        .thenComparing(BankCardInfoVO::getCreatedTime).reversed()).collect(Collectors.toList());
        bankCardUserVO.setBankCard(bankCardInfoVOList);
        return bankCardUserVO;
    }

    @Override
    public Response<Object> openUnbind(BankCardUnbindDTO dto) {
        try {
            // 记录调用前的日志
            log.info("Calling openUnbind with DTO: {}", dto);

            // 调用远程服务
            Response response = bankCardDubboService.openUnbind(dto);

            // 记录调用后的日志
            log.info("Received response from openUnbind: {}", response);

            return response;
        } catch (Exception e) {
            // 记录异常信息
            log.error("Error occurred while unbinding bank card", e);

            // 返回错误响应
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "请求异常", null);
        }
    }


    @Override
    public Response<List<BankCardVerifyVO>> getChannelVerify(BankCardVerifyDTO dto) {
        // 输入验证
        if (dto == null) {
            log.warn("Invalid input: BankCardVerifyDTO is null.");
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "请求参数异常", null);
        }

        try {
            // 记录调用前的日志
            log.info("Calling remote service with DTO: {}", dto);

            // 调用远程服务
            Response<List<BankCardVerifyVO>> response = bankCardDubboService.getChannelVerify(dto);

            // 记录调用后的日志
            log.info("Remote service call successful, returning response: {}", response);

            return response;
        } catch (Exception e) {
            // 异常处理
            log.error("Error calling remote service: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "请求异常", null);
        }
    }


    @Override
    public void changeBankCard(String staffMobile, Long bankCardId) {
        if (Objects.isNull(bankCardId)) {
            throw new CrmInterviewException("银行卡id不能为空");
        }
//        List<Admin> admins = adminService.queryAdminByMobile(Arrays.asList(staffMobile));
//        if (CollectionUtils.isEmpty(admins)) {
//            log.error("changeBankCard 该员工没有Admin权限, mobile={}", staffMobile);
//            throw new CrmInterviewException("该员工没有Admin权限");
//        }
//        Integer adminId = admins.get(0).getId();
        Integer adminId = 1;
        //根据id查询银行卡信息
        Response<BankCardVO> response = bankCardDubboService.queryBankCardById(bankCardId);
        if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
            log.info("changeBankCard queryBankCardById return null, bankCardId:{}", bankCardId);
            throw new CrmInterviewException("未找到银行卡信息");
        }
        BankCardVO bankCardVO = response.getResult();
        boolean unchanged = (Objects.isNull(bankCardVO.getRenew()) || !bankCardVO.getRenew()) &&
            Objects.isNull(bankCardVO.getDeletedAt()) &&
            BankCardStatusEnum.PASS.getValue().equals(bankCardVO.getStatus());
        if (!unchanged) {
            log.error("不满足打开换卡操作条件, {}", bankCardVO.toString());
            throw new CrmInterviewException("不满足打开换卡操作条件!!");
        }
        Boolean isChange = false;
        //打开换卡开关
        try {
            Response result = bankCardDubboService.openReplaceIdentification(bankCardId, bankCardVO.getUserId());
            if (Objects.nonNull(result) && ResponsCodeTypeEnum.SUCCESS.getCode().equals(result.getCode())) {
                isChange = true;
            }
        } catch (Exception e) {
            log.error("changeBankCard openReplaceIdentification error, bankCardId:{}, userId:{}", bankCardId,
                bankCardVO.getUserId(), e);
        }
        //根据银行卡id查询银行卡名称
        String bankName = "";
        try {
            Response<BankVO> bankResp = bankDubboService.queryBankInfoById(bankCardVO.getBankId());
            if (Objects.nonNull(bankResp) && Objects.nonNull(bankResp.getResult())) {
                bankName = bankResp.getResult().getName();
            }
        } catch (Exception e) {
            log.error("changeBankCard queryBankInfoById error, bankCardId:{}", bankCardVO.getBankId(), e);
        }
        //该银行卡是否已换卡
        if (isChange && !Boolean.TRUE.equals(bankCardVO.getRenew())) {
            //拼接银行卡换卡操作内容
            StringBuffer content = new StringBuffer("[换卡中]:");
            content.append(bankName).append(" ").append(bankCardVO.getAccountNo()).append(" ");
            content.append(DateUtil.dateToString(bankCardVO.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            if (bankCardVO.getChannelCode() != null) {
                String channelName = ChannelCodeEnum.getChannelNameByChannelCode(bankCardVO.getChannelCode());
                content.append(" 绑卡渠道").append(channelName);
            }
            content.append(" 已在").append(DateUtil.currentDateStr()).append("时间解绑");
            //打开换卡标识成功后保存操作日志
            saveNoteLog(bankCardVO.getId().intValue(), "BankCard", content.toString(), adminId);
        }
    }

    @Override
    public List<BankChannelVO> bankCardMatch(BankCardMatchDTO dto) {
        List<BankChannelVO> resultList = new ArrayList<>();
        if (Objects.isNull(dto) || Objects.isNull(dto.getTenor()) || Objects.isNull(dto.getAmount()) ||
            Objects.isNull(dto.getProductCode()) || Objects.isNull(dto.getUserId()) || StringUtils.isBlank(dto.getOrigin())) {
            return resultList;
        }
        ChannelsDTO channelsDTO = new ChannelsDTO();
        BeanUtils.copyProperties(dto, channelsDTO);
        channelsDTO.setWelabProductCode(dto.getProductCode());
        channelsDTO.setOriginCode(dto.getOrigin());
        Response<ForecastResultVO> response = capitalMatchDubboService.capitalMatchForecast(channelsDTO);
        if (Objects.nonNull(response)) {
            log.info("银行卡查询 bankCardMatch 调用资金 capitalMatchForecast 返回结果:{}", JSON.toJSONString(response));
            if(response.getResult() == null){
                return resultList;
            }
            List<MatchChannelVO> list = response.getResult().getMatchChannelVOList();
            if (CollectionUtils.isNotEmpty(list)) {
                resultList = list.stream().map(vo -> {
                    BankChannelVO bankChannelVO = new BankChannelVO();
                    BeanUtils.copyProperties(vo, bankChannelVO);
                    bankChannelVO.setPartnerName(PartnerCodeEnum.getPartnerNameByCode(vo.getPartnerCode()));
                    try {
                    	//返回放款时间
                    	Response<List<CapitalVo>> capitalResponse = capitalRuleProviderImpl.getCapitalRuleByCode(vo.getPartnerCode(), 0L);
                    	if (Objects.nonNull(capitalResponse) && CollectionUtils.isNotEmpty(capitalResponse.getResult())) {
                            List<CapitalVo> capitalList = capitalResponse.getResult();
                            if(capitalList != null && capitalList.size() > 0) {
                            	bankChannelVO.setLoanTime(capitalList.get(0).getLoanStartTime()+"-"+capitalList.get(0).getLoanEndTime());
                            }
                        }
                        //根据支付渠道返回银行列表
                        Response<List<BankVO>> bankResponse = bankDubboService.bankList(vo.getChannelCode());
                        if (Objects.nonNull(bankResponse) && CollectionUtils.isNotEmpty(bankResponse.getResult())) {
                            List<BankVO> bankList = bankResponse.getResult();
                            String bankName = bankList.stream().map(BankVO::getName).distinct()
                                .collect(Collectors.joining(","));
                            bankChannelVO.setBankcardName(bankName);
                        }
                    } catch (Exception e) {
                        log.error("查询银行列表接口异常, channelCode:{}", vo.getChannelCode(), e);
                    }
                    if ("matched".equals(bankChannelVO.getMatchResult())) {
                        bankChannelVO.setMatchResult("匹配");
                    } else {
                        bankChannelVO.setMatchResult("不匹配");
                    }
                    if ("binding".equals(bankChannelVO.getBankcardStatus())) {
                        bankChannelVO.setBankcardStatus("绑定");
                    } else {
                        bankChannelVO.setBankcardStatus("未绑定");
                    }
                    return bankChannelVO;
                }).collect(Collectors.toList());
            }
        }
        return resultList;
    }

    /**
     * @see BankCardService#releaseBankCardAuth(Integer, String)
     */
    @Override
    public String releaseBankCardAuth(Integer userId, String bankName) {
        if (Objects.isNull(userId)) {
            throw new CrmInterviewException("userId为空");
        }
        Profile profile = profileService.getProfileByUserId(userId);
        if (Objects.isNull(profile)) {
            log.error("releaseBankCardAuth profile is null. userId={}", userId);
            throw new CrmInterviewException("profile为空");
        }
        UserInfo userInfo = userService.getUserInfo(userId);
        if (Objects.isNull(userInfo)) {
            log.error("releaseBankCardAuth userInfo is null. userId={}", userId);
            throw new CrmInterviewException("userInfo为空");
        }
        //iWeDefendService.releaseAuth(userInfo.getMobile(), profile.getName(), CREDIT_CARD_AUTH_TYPE);
        String url = MessageFormat.format(bankReleaseUrl, ChannelCodeEnum.getAppNameByChannelCode(bankName));
        try {
            JSONObject json = new JSONObject();
            json.put("custName", profile.getName());
            json.put("reason", "客服主动注销");
            json.put("userId", userId.toString());
            String result = HttpClientUtil.doPost(url, json.toJSONString());
            log.info("releaseBankCardAuth url:{}, result:{}", url, result);
            return result;
        } catch (Exception e) {
            log.error("doPost url: {}, error: {}", url, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存银行卡换卡操作日志
     *
     * @param subjectId
     * @param subjectType
     * @param content
     * @param adminId
     */
    private void saveNoteLog(Integer subjectId, String subjectType, String content, Integer adminId) {
        Date curDate = new Date();
        NotesDTO note = new NotesDTO();
        note.setSubjectId(subjectId);
        note.setSubjectType(subjectType);
        note.setAdminId(adminId);
        note.setCreatedAt(curDate);
        note.setUpdatedAt(curDate);
        note.setContent(content);
        notesServiceFacade.saveNotes(note);
    }

    private List<ChannelInfoVO> buildChannelInfo(Integer userId) {
        List<ChannelInfoVO> list = new ArrayList<>();
        QueryBatchAccountDTO queryAccount = new QueryBatchAccountDTO();
        queryAccount.setOwnerId(userId.longValue());
        queryAccount.setOwnerType("User");
        queryAccount.setOrgId(0L);
        Response<List<AccountVO>> response = financeAccountsDubboService.queryAccountList(queryAccount);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            List<AccountVO> accountList = response.getResult();
            list = accountList.stream().map(account -> {
                ChannelInfoVO channelInfoVO = new ChannelInfoVO();
                channelInfoVO.setPaymentChannel(ChannelCodeEnum.getChannelNameByChannelCode(account.getChannelCode()));
                channelInfoVO.setCashBalance(account.getCashBalance());
                channelInfoVO.setFreezeBalance(account.getFreezeBalance());
                channelInfoVO.setRewardBalance(BigDecimal.ZERO);
                return channelInfoVO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    private List<BankCardInfoVO> buildBankCardInfoVOList(Integer userId) {
        List<BankCardInfoVO> list = new ArrayList<>();
        Response<List<BankCardVO>> response = bankCardDubboService.queryUserAllBankCard(userId.longValue());
        if (Objects.nonNull(response)) {
            List<BankCardVO> bankCards = response.getResult();
            bankCards = bankCards.stream().filter(item -> BankCardStatusEnum.PASS.getValue().equals(item.getStatus())).collect(Collectors.toList());
            //绑卡来源
            if (CollectionUtils.isNotEmpty(bankCards)) {
                bankCards = bankCardsGroupAndGetNewest(bankCards);

                List<String> spilitOriginCodeList = new ArrayList<>();
                for (BankCardVO bankCard : bankCards) {
                    String originCode = bankCard.getOriginCode();
                    if (StringUtils.isBlank(originCode)) {
                        continue;
                    }
                    if (originCode.contains(",")) {
	                    spilitOriginCodeList.addAll(Arrays.asList(originCode.split(",")));
                    } else {
                        spilitOriginCodeList.add(originCode);
                    }

                }
                if (CollectionUtil.isNotEmpty(spilitOriginCodeList)) {
                    log.info("buildBankCardInfoVOList userId:{}", userId);
                    Map<String, List<OriginInfo>> originCodeMap = getOriginName(spilitOriginCodeList);
                    if (CollectionUtil.isNotEmpty(originCodeMap)) {
                        bankCards.stream().forEach(bankCardVO -> {
                            /*if(ObjectUtil.isNotEmpty(bankCardVO.getDeletedAt())){
                                //已删除的卡状态改为已换卡
                                bankCardVO.setStatus("replace");
                            }*/
                            String originCode = bankCardVO.getOriginCode();
                            if (StringUtils.isBlank(originCode)){
                                return;
                            }
                            StringBuilder originName = new StringBuilder();
                            if (originCode.contains(",")){
                                for (String splitOriginCode : originCode.split(",")) {
                                    List<OriginInfo> originInfos = originCodeMap.get(splitOriginCode);
                                    originName.append(buildOriginName(originInfos));
                                    originName.append(",");
                                }
                                originName.deleteCharAt(originName.length() - 1);
                            } else {
                                originName.append(buildOriginName(originCodeMap.get(originCode)));

                            }
                            
                            bankCardVO.setOriginCode(originName.toString());
                            

                        });
                    }
                }
                return bankCards.stream().map(this::buildBankCardInfoVO).collect(Collectors.toList());
            }
        }
        return list;
    }

    /**
     * 银行卡分组,优先取状态为 pass ，并且未删除的数据，如果没有则取最新的数据
     * @param bankCards
     * @return
     */
    private List<BankCardVO> bankCardsGroupAndGetNewest(List<BankCardVO> bankCards) {
        Map<String, BankCardVO> groupedCards = bankCards.stream().collect(Collectors.groupingBy(BankCardVO::getAccountNo,
                Collectors.reducing(null, (b1, b2) -> {
                    if (b1 == null) return b2;
                    if (b2 == null) return b1;
                    if (BankCardStatusEnum.PASS.getValue().equals(b1.getStatus()) && Objects.isNull(b1.getDeletedAt())) {
                        return b1;
                    } else if (BankCardStatusEnum.PASS.getValue().equals(b2.getStatus()) && Objects.isNull(b2.getDeletedAt())) {
                        return b2;
                    } else {
                        return b1.getLatestBindedAt().after(b2.getLatestBindedAt()) ? b1 : b2;
                    }
                })));

        return new ArrayList<>(groupedCards.values());
    }

    private String buildOriginName(List<OriginInfo> originInfoList) {
        if (CollectionUtil.isNotEmpty(originInfoList)) {
            OriginInfo originInfo = originInfoList.stream().findFirst().orElse(null);
	        if (Objects.isNull(originInfo)){
                return "";
            }
	        if (originInfo.getSelfSupport().compareTo(1) == 0) {
                return "自营";
            } else {
                return originInfo.getName();
            }
        }
        return "";
    }

    private BankCardInfoVO buildBankCardInfoVO(BankCardVO bankCard) {
        BankCardInfoVO bankCardInfoVO = new BankCardInfoVO();
        if (Objects.isNull(bankCard)) {
            return bankCardInfoVO;
        }
        bankCardInfoVO.setIsDefault(bankCard.getIsDefault());
        bankCardInfoVO.setCanUnbind(bankCard.getCanUnbind());
        bankCardInfoVO.setOriginCode(bankCard.getOriginCode());
        bankCardInfoVO.setBankCardId(bankCard.getId().toString());
        bankCardInfoVO.setStatus(BankCardStatusEnum.getText(bankCard.getStatus()));
        bankCardInfoVO.setCreatedTime(bankCard.getCreatedAt());
        String channelName = ChannelCodeEnum.getChannelNameByChannelCode(bankCard.getChannelCode());
        bankCardInfoVO.setPaymentChannel(channelName);
        String payCode = ChannelCodeEnum.getPayCodeByChannelCode(bankCard.getChannelCode());
        if (payCode.length() > 2){
            bankCardInfoVO.setPaymentNo(payCode.substring(2));
        }
        bankCardInfoVO.setQuickPaymentState(bankCard.getQuickPaymentStatus());
        bankCardInfoVO.setAccount(bankCard.getAccountNo());
        bankCardInfoVO.setBankName(BankNameUtil.getBankName(bankCardInfoVO.getAccount()));
        if (Objects.nonNull(bankCard.getDeletedAt())) {
            bankCardInfoVO.setDeletedAt(bankCard.getDeletedAt());
            bankCardInfoVO.setStatus(BankCardStatusEnum.DELETE.getText());
        }
        Boolean renew = bankCard.getRenew();
        if (Objects.nonNull(renew)) {
            bankCardInfoVO.setRenew(renew);
            if (renew && !BankCardStatusEnum.DELETE.getText().equals(bankCardInfoVO.getStatus())) {
                bankCardInfoVO.setStatus(BankCardStatusEnum.CHANGE.getText());
            }
        }
        try {
            Response<BankVO> bankResp = bankDubboService.queryBankInfoById(bankCard.getBankId());
            if (Objects.nonNull(bankResp) && Objects.nonNull(bankResp.getResult())) {
                bankCardInfoVO.setBankName(bankResp.getResult().getName());
            }
        } catch (Exception e) {
            log.warn("buildBankCardInfoVO queryBankInfoById error. bankId():{}", bankCard.getBankId(), e);
        }
        return bankCardInfoVO;
    }

    private Map<String, List<OriginInfo>> getOriginName(List<String> originCodeList) {
        Map<String, Object> params = new HashMap<>();
        params.put("origins", originCodeList);
        log.info("获取渠道信息请求参数：{}", JSONUtil.toJsonStr(params));
        String result = HttpUtil.get(originNameUrl, params);
        log.info("获取渠道信息：{}", result);
        cn.hutool.json.JSONObject object = JSONUtil.parseObj(result);

        if ("9200".equals(object.get("code").toString())) {
            JSONArray jsonArray = JSONUtil.parseArray(object.get("data"));
            List<OriginInfo> originInfoList = JSONUtil.toList(jsonArray, OriginInfo.class);
            Map<String, List<OriginInfo>> originCodeMap = originInfoList.stream().collect(Collectors.groupingBy(OriginInfo::getOrigin));
            return originCodeMap;
        }
        return null;
    }

    private void getUserQuota(BankCardUserVO bankCardUserVO, Integer userId) {
        UserInfo userInfo = userService.getUserInfo(userId);
        if (Objects.isNull(userInfo)) {
            return;
        }
        //获取用户已用额度
        BigDecimal creditLine = BigDecimal.ZERO;
        BigDecimal avlCreditLine = BigDecimal.ZERO;
        BigDecimal aipAmount = BigDecimal.ZERO;
        try {
            aipAmount = loanApplicationServiceInterface.getAipAmountByUuid(userInfo.getUuid());
        } catch (Exception e) {
            log.warn("获取application-center的用户已用额度, error. uuid:{}", userInfo.getUuid(), e);
        }
        //查询额度管理接口查询用户总额度
        Response<GetUserQuotaResp> userQuota = null;
        GetUserQuotaReq quotaReq = new GetUserQuotaReq();
        quotaReq.setUserId(userInfo.getUuid());
        try {
            userQuota = quotaService.getUserQuota(quotaReq);
        } catch (Exception e) {
            log.warn("调用额度管理接口异常,error. quotaReq:{}", quotaReq.toString(), e);
        }
        if (userQuota != null && Objects.nonNull(userQuota.getResult())) {
            creditLine = userQuota.getResult().getCreditLine();
            avlCreditLine = userQuota.getResult().getAvlCreditLine().subtract(aipAmount);
        }
        bankCardUserVO.setCreditLine(creditLine);
        bankCardUserVO.setAvalCreditLine(avlCreditLine);
    }

    private UserDTO getUserDTOByMobile(String mobile) {
        UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(mobile);
        if (Objects.isNull(userDTO)) {
            log.error("userDTO is null. mobile:{}", mobile);
            throw new CrmInterviewException("手机号为" + mobile + "的用户不存在!");
        }
        return userDTO;
    }
}
