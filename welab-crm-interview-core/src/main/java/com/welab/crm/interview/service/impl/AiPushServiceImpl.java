package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.domain.AiTmkCallback;
import com.welab.crm.interview.domain.AiTmkConfig;
import com.welab.crm.interview.domain.AiTmkUuidCallback;
import com.welab.crm.interview.domain.TmkLoanInvite;
import com.welab.crm.interview.dto.ai.AiCustomerDTO;
import com.welab.crm.interview.dto.ai.AiPushDTO;
import com.welab.crm.interview.dto.callback.AiCallBackDTO;
import com.welab.crm.interview.handler.ElasticJobHandler;
import com.welab.crm.interview.mapper.AiTmkCallbackMapper;
import com.welab.crm.interview.mapper.AiTmkConfigMapper;
import com.welab.crm.interview.mapper.AiTmkUuidCallbackMapper;
import com.welab.crm.interview.mapper.TmkLoanInviteMapper;
import com.welab.crm.interview.service.AiPushService;
import com.welab.crm.interview.util.DateUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.IvrMessageUtil;
import com.welab.crm.interview.vo.callback.AiCallBackVo;
import com.welab.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiPushServiceImpl implements AiPushService {

    @Value("${ai.sale.push.url}")
    private String pushUrl;

    @Resource
    private TmkLoanInviteMapper inviteMapper;

    @Resource
    private AiTmkConfigMapper configMapper;

    @Resource
    private AiTmkCallbackMapper callbackMapper;

    @Resource
    private AiTmkUuidCallbackMapper uuidCallbackMapper;

    @Resource
    private AiPushSaveService pushSaveService;

    @Resource
    private ElasticJobHandler elasticJobHandler;

    private static final int BATCH_SIZE = 400;


    @Override
    public void pushDataToIvr(Long id) {
        // 1.查询待推送产品名称对应的数据
        AiTmkConfig config = configMapper.selectById(id);
        if (config == null || !config.getState() || config.getDeleteFlag()) {
            log.info("任务id对应的配置为空或者状态是未启用, 现做删除无用的job操作, 然后直接返回, id:{}", id);
            // 这里一般不会进入，未防止数据一致性问题，这里重新删除一遍无用的job操作
            deleteInvalidJob(id);
            return;
        }
        List<TmkLoanInvite> invites = getTmkLoanInvites(config);
        if (invites.isEmpty()) {
            log.info("产品名称或者渠道号对应的待推送数据为0直接返回, id:{}, 产品名称:{}, 渠道号:{}",
                    id, config.getProductNames(), config.getLoanChannels());
            return;
        }

        // 2.推送到ivr,由于数量可能偏大所以需要分批次推送
        log.info("pushDataToIvr待推送数据总量为{}, id:{}, 产品名称:{}, 渠道号:{}", invites.size(), id,
                config.getProductNames(), config.getLoanChannels());
        long totalStartTime = System.currentTimeMillis();
        // 记录本次外呼任务一共推送了多少手机量(即用户数量)
        int count = 0;
        // 构建调用ivr服务的参数对象，由于一次任务对应的话术和任务id是相同的，所以即便一次任务的不同批次可以复用同一个对象
        AiPushDTO pushDTO = getAiPushDTO(config);
        for (int i = 0; i < invites.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, invites.size());
            List<TmkLoanInvite> partInvites = invites.subList(i, end);
            // 构建客户信息参数对象列表
            final List<AiCustomerDTO> pushList = new ArrayList<>(partInvites.size());
            partInvites.forEach(t -> {
                AiCustomerDTO customerDTO = constructAiCustomerDTO(t, id);
                pushList.add(customerDTO);
            });
            pushDTO.setCustomers(pushList);

            try {
                long beginTime = System.currentTimeMillis();
                String requestBody = JSON.toJSONString(pushDTO);
                log.info("pushDataToIvr single batch configId:{}, param: {}", id, requestBody);
                String postResult = HttpClientUtil.doPost(pushUrl, requestBody);
                long middleTime = System.currentTimeMillis();
                log.info("pushDataToIvr single batch take time: {}ms", middleTime - beginTime);

                // 3.判断推送返回结果是否正确,不正确则打印日志信息,正确则将此批次的数据状态更新为已推送
                String errMsg = IvrMessageUtil.getMessageResult(postResult);
                if (errMsg != null) {
                    log.warn("pushDataToIvr return error: {}", errMsg);
                    continue;
                }
                pushSaveService.updatePushDataState(partInvites);
                count += partInvites.size();
                log.info("pushDataToIvr updatePushDataState spendTime: {}ms, id:{}", System.currentTimeMillis() - middleTime, id);
            } catch (Exception e) {
                log.warn("pushDataToIvr occur exception:{}", e.getMessage(), e);
            }
        }
        long totalPushTime = System.currentTimeMillis();
        log.info("pushDataToIvr push total time: {}ms, id: {}", totalPushTime - totalStartTime, id);

        // 4.更新推送数量到产品的推送历史表中
        pushSaveService.savePushHistory(id, count);
        log.info("pushDataToIvr total(push and save) time:{}ms, id:{}", System.currentTimeMillis() - totalPushTime, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiCallBackVo saveCallbackData(AiCallBackDTO callBackDTO) {
        try {
            AiTmkCallback cl = JSON.parseObject(callBackDTO.getExtraParam(), AiTmkCallback.class);
            AiTmkCallback callback = getAiTmkCallback(callBackDTO);
            if ("oss".equals(cl.getCreateUser())) {
                // 运营系统的数据回调处理
                AiTmkUuidCallback uuidCallback = new AiTmkUuidCallback();
                BeanUtils.copyProperties(callback, uuidCallback);
                uuidCallbackMapper.insert(uuidCallback);
            } else {
                // 客服电销数据回调处理
                callbackMapper.insert(callback);
            }
            return AiCallBackVo.success();
        } catch (Exception e) {
            log.error("ai saveCallbackData error: {}", e.getMessage(), e);
            return AiCallBackVo.failure(e.getMessage());
        }
    }

    private AiTmkCallback getAiTmkCallback(AiCallBackDTO callBackDTO) throws ParseException {
        AiTmkCallback callback = new AiTmkCallback();
        BeanUtils.copyProperties(callBackDTO, callback);
        SimpleDateFormat timeFormat = new SimpleDateFormat(DateUtils.DATE_TIME_FORMAT);
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isNotBlank(callBackDTO.getCallTime())) {
            callback.setCallTime(timeFormat.parse(callBackDTO.getCallTime()));
        }
        if (StringUtils.isNotBlank(callBackDTO.getAnswerTime())) {
            callback.setAnswerTime(timeFormat.parse(callBackDTO.getAnswerTime()));
        }
        if (StringUtils.isNotBlank(callBackDTO.getEndTime())) {
            callback.setEndTime(timeFormat.parse(callBackDTO.getEndTime()));
        }
        callback.setCallBackDate(dayFormat.parse(callBackDTO.getBusinessDate()));
        callback.setImportTime(timeFormat.parse(callBackDTO.getImportTime()));
        // 从额外的参数中解析taskId和外呼任务id
        String extraParam = callBackDTO.getExtraParam();
        AiTmkCallback parsedCallback = JSONObject.parseObject(extraParam, AiTmkCallback.class);
        callback.setTmkTaskId(parsedCallback.getTmkTaskId());
        callback.setConfigId(parsedCallback.getConfigId());
        return callback;
    }

    private void deleteInvalidJob(Long id) {
        log.info("deleteInvalidJob start, Id: {}", id);
        String jobName = ApplicationConstant.PREFIX_JOB + id;
        elasticJobHandler.removeJob(jobName);
        log.info("deleteInvalidJob end, Id: {}", id);
    }

    private List<TmkLoanInvite> getTmkLoanInvites(AiTmkConfig config) {
        // 需要查询的产品名称列表
        List<String> products = null;
        if (StringUtils.isNotBlank(config.getProductNames())) {
            products = Arrays.stream(config.getProductNames().split(",")).collect(Collectors.toList());
        }
        // 需要查询的进件渠道号列表
        List<String> channels = null;
        if (StringUtils.isNotBlank(config.getLoanChannels())) {
            channels = Arrays.stream(config.getLoanChannels().split(",")).collect(Collectors.toList());
        }
        // 需要过滤的标签编码列表
        List<String> labels = null;
        if (StringUtils.isNotBlank(config.getLabelCode())) {
            labels = Arrays.stream(config.getLabelCode().split(",")).collect(Collectors.toList());
        }

        List<TmkLoanInvite> invites = new ArrayList<>();
        // 分支查询条件是: 除产品、进件渠道外，审批时间是配置的时间段，指定是否分配数据，是否做过ai外呼的数据
        if (StringUtils.isNotBlank(config.getApprovedAt())) {
            String[] approvedAts = config.getApprovedAt().split(",");
            for (String approved : approvedAts) {
                Date approvedAtStar = DateUtil.getDateByDay(-Integer.parseInt(approved));
                Date approvedAtEnd = DateUtil.getDateByDay(-Integer.parseInt(approved) + 1);
                Integer maxAmount = config.getMaxAmount() == null ? null : Integer.valueOf(config.getMaxAmount());
                Integer minAmount = config.getMinAmount() == null ? null : Integer.valueOf(config.getMinAmount());
                List<TmkLoanInvite> itemList = inviteMapper.selectPushData(products, channels, labels,
                        approvedAtStar, approvedAtEnd, minAmount, maxAmount, config.getFlag());
                if (null != itemList && !itemList.isEmpty()) {
                    invites.addAll(itemList);
                }
            }
        }
        if (invites.isEmpty()) {
            return invites;
        } else {
            return processUserInfo(invites);
        }
    }

    private List<TmkLoanInvite> processUserInfo(List<TmkLoanInvite> invites) {
        for (TmkLoanInvite invite : invites) {
            if (StringUtils.isBlank(invite.getUsername())) {
                invite.setUsername("尊敬的我来数科用户");
            }
            if ("1".equals(invite.getGender())) {
                invite.setGender("先生");
            } else if ("2".equals(invite.getGender())) {
                invite.setGender("女士");
            } else {
                invite.setGender("");
            }
        }
        return invites;
    }

    private AiCustomerDTO constructAiCustomerDTO(TmkLoanInvite invite, Long configId) {
        AiCustomerDTO customerDTO = new AiCustomerDTO();
        customerDTO.setName(invite.getUsername());
        customerDTO.setSex(invite.getGender());
        customerDTO.setPhone(invite.getMobile());
        // 设置额外参数便于回调数据核对当前推送属于哪个任务，根据tmkTaskId也可知道推送的客户信息
        AiTmkCallback callback = new AiTmkCallback();
        callback.setConfigId(configId);
        callback.setTmkTaskId(invite.getTmkTaskId());
        customerDTO.setExtraParam(JSON.toJSONString(callback));
        return customerDTO;
    }

    private AiPushDTO getAiPushDTO(AiTmkConfig config) {
        AiPushDTO dto = new AiPushDTO();
        dto.setSpeechId(String.valueOf(config.getSpeechId()));
        dto.setTaskId(String.valueOf(config.getTaskId()));
        dto.setChannelType(config.getCaCompany());
        return dto;
    }
}
