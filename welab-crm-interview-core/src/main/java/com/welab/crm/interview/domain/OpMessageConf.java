package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 短信模板配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_message_conf")
public class OpMessageConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 短信标题
     */
    private String title;

    /**
     * 短信模板代码，来自短信系统
     */
    private String smsCode;

    /**
     * 目录组，字典字段
     */
    private String directory;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 短信内容
     */
    private String context;

    /**
     * 短信类型，字典字段
     */
    private String smsType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 排序字段，
     * 值越大，优先级越高
     */
    private Integer sort;


}
