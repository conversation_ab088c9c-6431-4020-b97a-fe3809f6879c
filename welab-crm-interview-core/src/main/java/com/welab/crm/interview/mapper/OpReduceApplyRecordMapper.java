package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.OpReduceApplyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 减免申请记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface OpReduceApplyRecordMapper extends BaseMapper<OpReduceApplyRecord> {

	/**
	 * 查询该流水号下全部已推送的记录条数
	 * @param requestNo
	 * @return
	 */
	Integer selectAllPushedCount(@Param("requestNo") String requestNo);
	
}
