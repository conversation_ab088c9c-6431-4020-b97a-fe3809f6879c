package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.ai.AiCustomerDTO;
import com.welab.crm.interview.dto.tmk.PushUuidDTO;
import com.welab.crm.interview.dto.tmk.PushUuidQueryDTO;
import com.welab.crm.interview.dto.tmk.TmkUuidConfigDTO;
import com.welab.crm.interview.dto.tmk.UuidDTO;
import com.welab.crm.interview.mapper.CsCallOutBlackListMapper;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.mapper.TmkUuidConfigMapper;
import com.welab.crm.interview.mapper.TmkUuidMapper;
import com.welab.crm.interview.service.TmkUuidService;
import com.welab.crm.interview.util.DateUtil;
import com.welab.crm.interview.vo.oss.SpeechTaskVO;
import com.welab.crm.interview.vo.tmk.TmkUuidCountVO;
import com.welab.exception.FastRuntimeException;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * uuid类型的电销数据保存服务
 *
 * <AUTHOR>
 * @date 2022/2/23 15:38
 */
@Service("tmkUuidService")
@Slf4j
public class TmkUuidServiceImpl implements TmkUuidService {

    @Resource
    private TmkUuidAsyncService tmkUuidAsyncService;

    @Resource
    private UserService userService;

    @Resource
    private OpDictInfoMapper dictInfoMapper;

    @Resource
    private TmkUuidConfigMapper tmkUuidConfigMapper;
    
    @Resource
    private CsCallOutBlackListMapper csCallOutBlackListMapper;
    
    
    @Resource
    private TmkUuidMapper tmkUuidMapper;

    @Override
    public Boolean saveUuidListFromOss(PushUuidDTO pushUuidDTO) {
        try {
            if (Objects.nonNull(pushUuidDTO)) {
                if (StringUtils.isBlank(pushUuidDTO.getCallType()) ||
                        StringUtils.isBlank(pushUuidDTO.getNumPackageId())) {
                    log.error("saveUuidListFromOss callType or numPackageId param null !");
                    return false;
                }
                List<UuidDTO> uuidList = pushUuidDTO.getUuid();
                log.info("saveUuidListFromOss推送uuid数量:{}, currentTime:{}", uuidList != null ? uuidList.size() : 0,
                        DateUtils.formatDate(new Date(), DateUtil.TIME_FORMAT));
                if (CollectionUtils.isEmpty(uuidList)) {
                    return true;
                }
                
                List<String> calloutBlackUuidList = queryValidBlackUuidList();
                uuidList = uuidList.stream().filter(item -> !calloutBlackUuidList.contains(item.getUuid().toString())).collect(Collectors.toList());
                log.info("saveUuidListFromOss 过滤黑名单后,推送uuid数量:{}, currentTime:{}", uuidList != null ? uuidList.size() : 0,
                        DateUtils.formatDate(new Date(), DateUtil.TIME_FORMAT));
                pushUuidDTO.setUuid(uuidList);
                // 保存批量的数据, 如果是ai则保存之外还要推送到创研做ai外呼操作
                tmkUuidAsyncService.insertUuidData(pushUuidDTO);
                log.info("saveUuidListFromOss异步已保存数据");
                return true;
            } else {
                log.warn("saveUuidListFromOss param pushUuidDTO null");
                return false;
            }
        } catch (Exception e) {
            log.warn("saveUuidListFromOss发生未知错误: {}", e.getMessage(), e);
            return false;
        }
    }
    

    /**
     * 查询有效的外呼黑名单uuid
     * @return
     */
    private List<String> queryValidBlackUuidList() {
        List<CsCallOutBlackList> calloutBlackList = csCallOutBlackListMapper.selectList(Wrappers.lambdaQuery(CsCallOutBlackList.class)
                .ge(CsCallOutBlackList::getValidEndTime, com.welab.collection.interview.utils.DateUtils.getStartOfToday())
                .eq(CsCallOutBlackList::getApprovalStatus, 1)
                .eq(CsCallOutBlackList::getIsDeleted, 0).select(CsCallOutBlackList::getUuid));
        if (CollectionUtils.isEmpty(calloutBlackList)){
            return Collections.emptyList();
        }
        return calloutBlackList.stream().map(CsCallOutBlackList::getUuid).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCrmCallConfig(TmkUuidConfigDTO tmkUuidConfigDTO) {
        LambdaQueryWrapper<TmkUuidConfig> wrapper = Wrappers.<TmkUuidConfig>lambdaQuery()
                .eq(TmkUuidConfig::getConfigId, tmkUuidConfigDTO.getConfigId());
        TmkUuidConfig uuidConfig = tmkUuidConfigMapper.selectOne(wrapper);
        if (uuidConfig != null && tmkUuidConfigDTO.getDeleteFlag()) {
            // 做软删除操作
            TmkUuidConfig config = new TmkUuidConfig();
            config.setId(uuidConfig.getId());
            config.setDeleteFlag(tmkUuidConfigDTO.getDeleteFlag());
            tmkUuidConfigMapper.updateById(config);
        } else if (uuidConfig != null && !tmkUuidConfigDTO.getDeleteFlag()) {
            // 做更新操作
            TmkUuidConfig entity = new TmkUuidConfig();
            BeanUtils.copyProperties(tmkUuidConfigDTO, entity);
            entity.setCreateUser(uuidConfig.getCreateUser());
            entity.setGmtCreate(uuidConfig.getGmtCreate());
            entity.setId(uuidConfig.getId());
            tmkUuidConfigMapper.updateById(entity);
        } else {
            // 不存在则创建
            TmkUuidConfig entity = new TmkUuidConfig();
            BeanUtils.copyProperties(tmkUuidConfigDTO, entity);
            entity.setGmtCreate(new Date());
            tmkUuidConfigMapper.insert(entity);
        }
    }

    @Override
    public Response<TmkUuidCountVO> queryPushUuidCount(PushUuidQueryDTO dto) {
        try {
            validQueryDto(dto);

            Date startDate = com.welab.crm.base.utils.DateUtils.getStartOfDate(dto.getPushDate());
            Date endDate = com.welab.common.utils.DateUtil.plusDays(startDate, 1);

            // 查询数据
            List<TmkUuid> tmkUuidList = tmkUuidMapper.selectList(Wrappers.lambdaQuery(TmkUuid.class)
                    .between(TmkUuid::getGmtCreate, startDate, endDate)
                    .eq(TmkUuid::getNumPackageId, dto.getNumPackageId())
                    .groupBy(TmkUuid::getUuid));

            // 创建返回对象
            TmkUuidCountVO countVO = new TmkUuidCountVO();
            BeanUtils.copyProperties(dto, countVO);
            countVO.setPushDate(startDate);
            int count = (tmkUuidList != null) ? tmkUuidList.size() : 0;
            countVO.setCount(count);

            return Response.success(countVO);
        } catch (Exception e) {
            // 异常处理
            log.error("Error in queryPushUuidCount: ", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "查询失败", null);
        }
    }


    private void validQueryDto(PushUuidQueryDTO dto) {
        if (Objects.isNull(dto)){
            throw new FastRuntimeException("请求参数不能为空");
        }
        
        if (StringUtils.isBlank(dto.getNumPackageId())){
            throw new FastRuntimeException("号码包id不能为空");
        }
        
        if (Objects.isNull(dto.getPushDate())){
            throw new FastRuntimeException("推送日期不能为空");
        }
    }

    @Override
    public void validateAiMobiles(String mobiles, Long configId) {
        log.info("validateAiMobiles, param: {}, configId: {}", mobiles, configId);
        if (StringUtils.isNotBlank(mobiles) && configId != null) {
            try {
                String[] mobileList = mobiles.split(",");
                List<AiCustomerDTO> customerDTOList = new ArrayList<>();
                for (String m : mobileList) {
                    try {
                        UserInfo userInfo = userService.getUserInfo(m);
                        AiCustomerDTO aiCustomerDTO;
                        if (userInfo != null) {
                            aiCustomerDTO = constructAiCustomerDTO(m, userInfo.getUserName());
                        } else {
                            aiCustomerDTO = constructAiCustomerDTO(m, null);
                        }
                        customerDTOList.add(aiCustomerDTO);
                    } catch (Exception e) {
                        log.warn("validateAiMobiles userService.getUserInfo error: {}, mobile:{}", e.getMessage(), m);
                    }
                }
                TmkUuidConfig config = tmkUuidAsyncService.getTmkUuidConfigById(configId);
                if (CollectionUtils.isNotEmpty(customerDTOList)) {
                    tmkUuidAsyncService.pushCallToIvr(config, customerDTOList);
                }
            } catch (Exception e) {
                log.warn("validateAiMobiles exception, mobiles:{}, configId:{}", mobiles, configId, e);
            }
        } else {
            log.warn("validateAiMobiles mobiles or configId param is null");
        }
    }




    @Override
    public List<SpeechTaskVO> getSpeechTaskList() {
        LambdaQueryWrapper<OpDictInfo> wrapper = Wrappers.<OpDictInfo>lambdaQuery()
                .eq(OpDictInfo::getCategory, "speechAI").eq(OpDictInfo::getStatus, Boolean.TRUE)
                .eq(OpDictInfo::getDetail, "ZY");
        List<OpDictInfo> dictInfo = dictInfoMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dictInfo)) {
            return Collections.emptyList();
        } else {
            List<SpeechTaskVO> speechList = new ArrayList<>(dictInfo.size());
            for (OpDictInfo info : dictInfo) {
                SpeechTaskVO vo = new SpeechTaskVO();
                vo.setSpeechTaskId(info.getType());
                vo.setSpeechName(info.getContent());
                speechList.add(vo);
            }
            return speechList;
        }
    }

    private AiCustomerDTO constructAiCustomerDTO(String mobile, String userName) {
        AiCustomerDTO customerDTO = new AiCustomerDTO();
        if (StringUtils.isBlank(userName)) {
            customerDTO.setName("尊敬的我来数科用户");
        } else {
            customerDTO.setName(userName);
        }
        customerDTO.setSex("");
        customerDTO.setPhone(mobile);
        AiTmkUuidCallback callback = new AiTmkUuidCallback();
        long configId = System.currentTimeMillis();
        callback.setConfigId(configId);
        callback.setTmkTaskId("validate" + configId);
        callback.setCreateUser("oss");
        customerDTO.setExtraParam(JSON.toJSONString(callback));
        return customerDTO;
    }
}
