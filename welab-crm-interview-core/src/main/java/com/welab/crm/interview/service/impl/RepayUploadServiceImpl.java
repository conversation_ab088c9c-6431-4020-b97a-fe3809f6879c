package com.welab.crm.interview.service.impl;

import com.alibaba.excel.EasyExcel;
import com.welab.collection.interview.service.ProductService;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.constant.DueTypeConstant;
import com.welab.crm.interview.domain.CsRepayFile;
import com.welab.crm.interview.enums.LoanTypeEnum;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.service.*;
import com.welab.crm.interview.vo.repay.RepayVO;
import com.welab.finance.loanprocedure.dto.DueDTO;
import com.welab.finance.loanprocedure.dubbo.DueDubboService;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.vo.DueVO;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.product.interfaces.dto.ProductDTO;
import com.welab.product.interfaces.facade.ProductServiceFacade;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import com.welab.wallet.installment.dto.LoanDueQueryDTO;
import com.welab.wallet.installment.dubbo.WalletLoanDubbboService;
import com.welab.wallet.installment.vo.WalletDueVO;
import com.welab.wallet.installment.vo.WalletLoanRateVO;
import com.welab.wallet.installment.vo.WalletLoanVO;
import com.welab.wallet.repayment.dubbo.RepaymentDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 客户还款明细数据查询并保存文件实现类
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
@Service("repayUploadService")
@Slf4j
public class RepayUploadServiceImpl implements RepayUploadService {

    @Resource
    private AsyncTaskExecutor repayTaskExecutor;

    @Resource
    private IUploadService iUploadService;

    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;

    @Resource
    private DueDubboService dueDubboService;

    @Resource
    private LoanDubboService loanDubboService;

    @Resource
    private ProfileService profileService;

    @Resource
    private ProductServiceFacade productServiceFacade;

    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource
    private CsRepayFileService csRepayFileService;

    @Resource
    private WalletLoanDubbboService walletLoanDubbboService;

    @Resource
    private RepaymentDubboService iRepaymentService;

    @Resource
    private FinanceService financeService;

    @Resource
    private ProductService productService;


    @Override
    public void queryAndSaveFileUrl(Long id, String loanType, List<String> uuidList) {
        log.info("begin to query and calculate repay data, id: {}", id);
        repayTaskExecutor.execute(() -> {
            log.info("start queryAndSaveFileUrl thread data, id: {}", id);
            // 1.先查询各个用户的还款明细
            List<CompletableFuture<List<RepayVO>>> futureResult = new ArrayList<>(30);
            for (String uuid : uuidList) {
                CompletableFuture<List<RepayVO>> future = CompletableFuture.supplyAsync(() -> getRepayVo(loanType, uuid), repayTaskExecutor);
                futureResult.add(future);
            }
            List<List<RepayVO>> results = futureResult.stream().map(CompletableFuture::join)
                    .filter(item -> !item.isEmpty()).collect(Collectors.toList());
            List<RepayVO> queryList = new ArrayList<>(results.size());
            if (!CollectionUtils.isEmpty(results)) {
                for (List<RepayVO> result : results) {
                    queryList.addAll(result);
                }
                // 2.将列表生成文件传到oss，并获取下载url
                String url = saveAndGetFileUrl(id, queryList);

                // 3.更新url到数据记录中
                CsRepayFile file = new CsRepayFile();
                file.setId(id);
                file.setFileUrl(url);
                file.setTaskState(1);
                csRepayFileService.updateRepayDomain(file);
            } else {
                log.info("RepayUploadService file query user none data returned, uuidList: {}", uuidList);
            }
            log.info("end queryAndSaveFileUrl thread data, id: {}", id);
        });

        log.info("end to query and calculate repay data, id: {}", id);
    }

    private List<RepayVO> getRepayVo(String loanType, String uuid) {
        // 1.先查询消金获取用户信息和一些基本的还款信息
        Profile profile = profileService.getProfileByUuid(Long.valueOf(uuid));
        if (profile == null) {
            log.warn("RepayUploadService file contains invalid uuid: {}", uuid);
            return Collections.emptyList();
        }

        if (LoanTypeEnum.LOAN.getValue().equals(loanType)) {
            // 查询现金贷类型的数据
            return getLoanRepayVo(uuid, profile);
        } else {
            // 查询钱夹谷谷类型的数据
            return getWalletRepayVo(uuid, profile);
        }
    }

    private List<RepayVO> getWalletRepayVo(String uuid, Profile profile) {
        try {
            // 2.再查询钱夹谷谷的贷款分期信息，查询从2020-10 至今的还款信息
            List<WalletLoanVO> results = getWalletDueData(profile.getBorrower_id());
            if (results.isEmpty()) {
                log.info("getWalletRepayVo 未获取到钱包数据，uuid-{}", uuid);
                return Collections.emptyList();
            }

            // 3.聚集各个月份数据: 先将全部合同分组，再聚合每一个合同所有月份的数据
            Map<String, WalletLoanVO> walletMap = getWalletApplicationIdMap(results);

            // 4.设置基本信息和应还、未还、已还等数据
            List<RepayVO> repayList = new ArrayList<>(walletMap.size());
            for (Map.Entry<String, WalletLoanVO> entry : walletMap.entrySet()) {
                RepayVO repayVO = new RepayVO();
                // 查询单个钱包贷款全部还款计划
                setTotalDues(entry.getKey(),entry.getValue(),walletMap);
                // 设置钱包数据的基本信息: 客户信息、总应还未还已还金额等
                setWalletBasicInfo(repayVO, profile, uuid, entry.getKey(), entry.getValue());
                // 设置贷款利率字段
                setLoanRate(entry.getKey(), repayVO, uuid);
                // 设置钱包数据的应还未还已还等数据
                setWalletDueInfo(repayVO, entry.getValue().getDues());
                // 设置债转信息和提前还款金额
                setLoanTransferAndEarlyAmount(repayVO.getApplicationId(), repayVO);
                repayList.add(repayVO);
            }

            return repayList;
        } catch (Exception e) {
            log.warn("getWalletRepayVo exception, uuid: {}, errMessage: {}",
                    uuid, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private void setTotalDues(String applicationId, WalletLoanVO loan, Map<String, WalletLoanVO> walletMap) {
        if ("closed".equals(loan.getStatus())){
            return;
        }
        // 查询单个钱包的还款计划
        Response<WalletLoanVO> response =  walletLoanDubbboService.getLoanDueByApplicationId(applicationId);
        if (!ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())){
            log.warn("RepayUploadService setTotalDues return fail, applicationId: {}, {}", applicationId, response);
        } else {
            WalletLoanVO result = response.getResult();
            // 查询钱包贷款全部还款计划
            List<WalletDueVO> dueList = result.getDues();
            if (!CollectionUtils.isEmpty(dueList)) {
                dueList.sort(Comparator.comparing(WalletDueVO::getIndexNo));
                // 应还总金额、已还总金额、未还总金额
                BigDecimal shouldTotalAmount = new BigDecimal("0");
                BigDecimal settledTotalAmount = new BigDecimal("0");
                BigDecimal outstandingTotalAmount = new BigDecimal("0");
                for (WalletDueVO dueVO : dueList) {
                    shouldTotalAmount = shouldTotalAmount.add(dueVO.getAmount());
                    if (dueVO.getSettledAmount() != null) {
                        settledTotalAmount = settledTotalAmount.add(dueVO.getSettledAmount());
                    }
                    if (dueVO.getOutstandingAmount() != null) {
                        outstandingTotalAmount = outstandingTotalAmount.add(dueVO.getOutstandingAmount());
                    }
                }
                loan.setCurrentFeeAmount(shouldTotalAmount);
                loan.setCurrentSettledAmount(settledTotalAmount);
                loan.setCurrentOutstandingAmount(outstandingTotalAmount);
                loan.setDues(dueList);
            }
        }
    }
    

    private void setLoanRate(String applicationId, RepayVO repayVO, String uuid) {
        try {
            // 设置贷款利率
            Response<WalletLoanRateVO> response = walletLoanDubbboService.getLoanRateByApplicationId(applicationId);
            if (response != null && response.getResult() != null) {
                repayVO.setLoanRate(response.getResult().getTotalRate());
            }
        } catch (Exception e) {
            log.warn("getWalletRepayVo 获取利率异常: uuid-{}, errMsg: {}", uuid, e.getMessage(), e);
        }
    }

    private void setWalletBasicInfo(RepayVO repayVO, Profile profile, String uuid, String applicationId, WalletLoanVO loan) {
        repayVO.setCustomerName(profile.getName());
        repayVO.setApplicationId(applicationId);
        repayVO.setUuid(uuid);
        repayVO.setUserId(String.valueOf(profile.getBorrower_id()));
        if (StringUtils.isNotBlank(loan.getPartnerName())) {
            repayVO.setPartnerName(loan.getPartnerName());
        } else {
            repayVO.setPartnerName(loanApplicationService.getPartnerName(loan.getPartnerCode()));
        }
        String pCode = loan.getProductCode();
        repayVO.setProductNo(pCode);
        repayVO.setProductName(LoanTypeEnum.WALLET.getText() + "-" + pCode.substring(pCode.length() - 1));
        repayVO.setLoanAmount(loan.getAmount().toString());
        repayVO.setLoanTerms(String.valueOf(loan.getTenor()));
        if (loan.getRepurchaseStatus() != null) {
            repayVO.setRepurchaseStatus(1 == loan.getRepurchaseStatus() ? "是" : "否");
        } else {
            repayVO.setRepurchaseStatus("否");
        }
        // 放款时间
        repayVO.setMakeLoanTime(getFormatTime(loan.getDisbursedAt()));

        if (loan.getClosedAt() != null) {
            repayVO.setSettledTime(getFormatTime(loan.getClosedAt()));
        }
        if ("closed".equals(loan.getStatus())) {
            repayVO.setWhetherSettled("已结清");
            repayVO.setPaidTerms(repayVO.getLoanTerms());
            repayVO.setUnPayTerms("0");
        } else {
            repayVO.setWhetherSettled("未结清");
        }
        if (isEarlySettledStatus(loan.getStatus())) {
            repayVO.setEarlySettled("是");
            // 提前还款的已还期数和未还期数要特化处理
            setPaidAndUnPaidTermsForEarly(repayVO, loan.getDisbursedAt(), loan.getClosedAt());
        } else {
            repayVO.setEarlySettled("否");
        }
        // 设置总应还未还已还金额
        repayVO.setShouldTotalAmount(loan.getCurrentFeeAmount().toString());
        repayVO.setFactTotalAmount(loan.getCurrentSettledAmount().toString());
        repayVO.setUnpaidTotalAmount(loan.getCurrentOutstandingAmount().toString());
    }

    private void setWalletDueInfo(RepayVO repayVO, List<WalletDueVO> dues) {
        dues.sort(Comparator.comparing(WalletDueVO::getIndexNo));

        for (WalletDueVO due : dues) {
            // 计算最近应还日期
            if (repayVO.getLastShouldTime() == null && due.getDueDate().after(new Date())) {
                repayVO.setLastShouldTime(DateFormatUtils.format(due.getDueDate(), "yyyy-MM-dd"));
            }
            DueVO dueVo = convertToDueVo(due);
            // 当已还金额小于应还金额时即认为本期未还， 由于已经按小到大的分期数排序，所以可计算已还期数和未还期数
            setPaidTerms(repayVO, dueVo, dues.get(dues.size() - 1).getIndexNo());
            // 计算应还未还等信息
            setShouldAndPaidData(repayVO, dueVo);
        }
    }

    private List<WalletLoanVO> getWalletDueData(Integer userId) {
        LoanDueQueryDTO loanDueQueryDTO = new LoanDueQueryDTO();
        loanDueQueryDTO.setUserId(userId);
        List<WalletLoanVO> results = new ArrayList<>(1000);
        // 查询资金的钱包表，最早一笔是20年10月份的,查询距离当前时间的月份数
        long queryMonth = com.welab.crm.interview.util.DateUtil.calculateMonthDifference("2020-10-01", DateUtil.dateToString(new Date(), DateUtil.TimeFormatter.YYYY_MM_DD));
        for (int i = 0; i <= queryMonth; i++) {
            LocalDate date = LocalDate.now().minusMonths(i);
            Date month = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
            loanDueQueryDTO.setMonth(month);
            // 查询指定月的贷款
            long startTime = System.currentTimeMillis();
            Response<List<WalletLoanVO>> walletResponse = walletLoanDubbboService.getLoanDue(loanDueQueryDTO);
            log.info("getWalletDueData one month spend time: {}ms", System.currentTimeMillis() - startTime);
            if (walletResponse != null && walletResponse.getResult() != null) {
                List<WalletLoanVO> voList = walletResponse.getResult();
                results.addAll(voList);
            }
        }
        return results;
    }

    private Map<String, WalletLoanVO> getWalletApplicationIdMap(List<WalletLoanVO> results) {
        Map<String, List<WalletLoanVO>> map = results.stream().collect(Collectors.groupingBy(WalletLoanVO::getApplicationId));
        Map<String, WalletLoanVO> walletMap = new HashMap<>(map.size(), 1);
        for (Map.Entry<String, List<WalletLoanVO>> entry : map.entrySet()) {
            List<WalletLoanVO> monthList = entry.getValue();
            WalletLoanVO totalVo = monthList.get(0);
            for (int i = 1; i < monthList.size(); i++) {
                totalVo.setCurrentFeeAmount(totalVo.getCurrentFeeAmount().add(monthList.get(i).getCurrentFeeAmount()));
                totalVo.setCurrentSettledAmount(totalVo.getCurrentSettledAmount().add(monthList.get(i).getCurrentSettledAmount()));
                totalVo.setCurrentOutstandingAmount(totalVo.getCurrentOutstandingAmount().add(monthList.get(i).getCurrentOutstandingAmount()));
                totalVo.getDues().addAll(monthList.get(i).getDues());
            }
            walletMap.put(entry.getKey(), totalVo);
        }
        return walletMap;
    }

    private DueVO convertToDueVo(WalletDueVO due) {
        DueVO dueVo = new DueVO();
        dueVo.setIndexNo(due.getIndexNo());
        dueVo.setDueType(due.getDueType());
        dueVo.setAmount(due.getAmount());
        dueVo.setOutstandingAmount(due.getOutstandingAmount());
        dueVo.setSettledAmount(due.getSettledAmount());
        return dueVo;
    }

    private List<RepayVO> getLoanRepayVo(String uuid, Profile profile) {
        try {
            // 2.再查询消金获取用户的贷款基本信息
            List<LoanApplicationDTO> applications = loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(
                    Long.valueOf(uuid), Arrays.asList("disbursed", "closed"));
            if (CollectionUtils.isEmpty(applications)) {
                log.info("RepayUploadService file uuid has no LoanApplication, uuid: {}", uuid);
                return Collections.emptyList();
            }

            List<RepayVO> repayList = new ArrayList<>(applications.size());
            // 3.再查询资金接口获取用户的每一笔贷款对应还款计划信息
            for (LoanApplicationDTO application : applications) {
                RepayVO repayVO = new RepayVO();
                // 设置客户姓名,贷款号,uuid等基本信息
                setCustomerBasicLoanInfo(uuid, profile, application, repayVO);
                // 设置是否提前还款、结清与否、资金方名称等信息
                setEarlySettledInfo(uuid, application, repayVO);
                // 设置产品名称信息
                setProductName(uuid, application, repayVO);
                // 设置贷款的应还、未还、已还信息
                setLoanTermsInfo(application, repayVO);
                // 设置渠道名称、当前还款日期信息
                setOriginNameAndDate(application, repayVO);
                repayList.add(repayVO);
            }

            return repayList;
        } catch (Exception e) {
            log.warn("RepayUploadService queryAndSaveFileUrl exception, uuid: {}, errMessage: {}",
                    uuid, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private void setCustomerBasicLoanInfo(String uuid, Profile p, LoanApplicationDTO app, RepayVO repayVO) {
        repayVO.setCustomerName(p.getName());
        repayVO.setUuid(uuid);
        repayVO.setUserId(String.valueOf(p.getBorrower_id()));
        repayVO.setApplicationId(app.getApplicationId());
        repayVO.setLoanAmount(String.valueOf(app.getAmount()));
        repayVO.setLoanRate(String.valueOf(app.getTotalRate()));
    }

    private void setOriginNameAndDate(LoanApplicationDTO app, RepayVO repayVO) {
        Map<String, String> originNameMap = productService.listChannelNameByCodes(app.getOrigin());
        repayVO.setOriginName(originNameMap.get(app.getOrigin()));
    }

    private void setEarlySettledInfo(String uuid, LoanApplicationDTO application, RepayVO repayVO) {
        Response<LoanVO> r1 = loanDubboService.findLoanByAppId(application.getApplicationId());
        if (!ResponsCodeTypeEnum.SUCCESS.getCode().equals(r1.getCode())) {
            log.warn("RepayUploadService queryLoans return fail: {}", r1);
        } else if (r1.getResult() != null) {
            if ("closed".equals(application.getState())) {
                repayVO.setWhetherSettled("已结清");
                repayVO.setSettledTime(getFormatTime(r1.getResult().getClosedAt()));
                // 已结清的直接设置贷款期数和未还的设置为0
                String paidTerms = r1.getResult().getTenor().replace("M", "");
                repayVO.setPaidTerms(paidTerms);
                repayVO.setUnPayTerms("0");
            } else {
                repayVO.setWhetherSettled("未结清");
            }
            if (isEarlySettledStatus(r1.getResult().getStatus())) {
                repayVO.setEarlySettled("是");
                // 提前还款的已还期数和未还期数要特化处理
                setPaidAndUnPaidTermsForEarly(repayVO, r1.getResult().getDisbursedAt(), r1.getResult().getClosedAt());
            } else {
                repayVO.setEarlySettled("否");
            }
            repayVO.setProductNo(r1.getResult().getProductCode());
            repayVO.setMakeLoanTime(getFormatTime(r1.getResult().getDisbursedAt()));
            Long status = r1.getResult().getRepurchaseStatus();
            if (status != null) {
                repayVO.setRepurchaseStatus(1 == r1.getResult().getRepurchaseStatus() ? "是" : "否");
            } else {
                repayVO.setRepurchaseStatus("否");
            }
            try {
                repayVO.setPartnerName(loanApplicationService.getPartnerName(application.getPartnerCode()));
            } catch (Exception e) {
                log.warn("RepayUploadService getPartnerName error, uuid: {}, PartnerCode:{} ", uuid, r1.getResult().getPartnerCode());
            }
            //获取整笔贷款还款计划
            List<DueVO> totalDues = r1.getResult().getDueList();
            if (!CollectionUtils.isEmpty(totalDues)) {
                //期数排序
                totalDues.sort(Comparator.comparing(DueVO::getDueDate));
                for (DueVO due : totalDues) {
                    if (due.getAmount().compareTo(due.getSettledAmount()) != 0) {
                        repayVO.setDeadDate(DateUtil.dateToString(due.getDueDate(), DateUtil.TimeFormatter.YYYY_MM_DD));
                        break;
                    }
                }
            }
            // 设置债转结清信息和提前结清金额
            setLoanTransferAndEarlyAmount(application.getApplicationId(), repayVO);
        }
    }

    /**
     * 提前还款的已还期数和未还期数要特化处理，这里的已还、未还指的是客户提前结清那期为止的客户已还数和未还数，因此需要计算
     */
    private void setPaidAndUnPaidTermsForEarly(RepayVO repayVO, Date makeLoanTime, Date closedTime) {
        String terms = repayVO.getPaidTerms();
        if (StringUtils.isBlank(terms) || makeLoanTime == null || closedTime == null) {
            return;
        }
        int loanTerms = Integer.parseInt(terms);
        LocalDate loanDate = LocalDate.from(makeLoanTime.toInstant().atZone(ZoneId.systemDefault()));
        LocalDate closedDate = LocalDate.from(closedTime.toInstant().atZone(ZoneId.systemDefault()));
        for (int i = 1; i <= loanTerms; i++) {
            LocalDate localDate = loanDate.plusMonths(i);
            if (closedDate.isBefore(localDate)) {
                repayVO.setPaidTerms(String.valueOf(i));
                repayVO.setUnPayTerms(String.valueOf(loanTerms - i));
                break;
            }
        }
    }

    private void setLoanTransferAndEarlyAmount(String applicationId, RepayVO repayVO) {
        // 获取债转结清信息
        WriteoffDTO writeOffDto = financeService.getLoanTransferByApplicationId(applicationId);
        if (Objects.nonNull(writeOffDto)) {
            repayVO.setLoanTransferCompany(writeOffDto.getCompanyName());
            repayVO.setLoanTransferTime(getFormatTime(writeOffDto.getTransactionDate()));
        }

        // 提前结清金额
        repayVO.setEarlySettleAMount(loanApplicationService.getLoanBalance(applicationId).toString());
    }

    /**
     * 是否提前还款状态
     */
    private boolean isEarlySettledStatus(String status) {
        return "early_settled".equals(status) || "refunded".equals(status);
    }

    private void setLoanTermsInfo(LoanApplicationDTO application, RepayVO repayVO) {
        DueDTO dueDTO = new DueDTO();
        dueDTO.setApplicationId(application.getApplicationId());
        Response<List<DueVO>> response = dueDubboService.queryDues(dueDTO);
        if (!ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
            log.warn("RepayUploadService queryDues return fail, applicationId: {}, {}", application.getApplicationId(), response);
        } else {
            List<DueVO> dueList = response.getResult();
            if (!CollectionUtils.isEmpty(dueList)) {
                dueList.sort(Comparator.comparing(DueVO::getIndexNo));
                repayVO.setLoanTerms(String.valueOf(dueList.get(dueList.size() - 1).getIndexNo()));

                // 应还总金额、已还总金额、未还总金额
                BigDecimal shouldTotalAmount = new BigDecimal("0");
                BigDecimal settledTotalAmount = new BigDecimal("0");
                BigDecimal outstandingTotalAmount = new BigDecimal("0");
                for (DueVO dueVO : dueList) {
                    // 当已还金额小于应还金额时即认为本期未还， 由于已经按小到大的分期数排序，所以可计算已还期数和未还期数
                    setPaidTerms(repayVO, dueVO, dueList.get(dueList.size() - 1).getIndexNo());
                    // 第一个大于当下日期的即是最近应还日期
                    if (dueVO.getDueDate().after(new Date()) && repayVO.getLastShouldTime() == null) {
                        repayVO.setLastShouldTime(DateFormatUtils.format(dueVO.getDueDate(), "yyyy-MM-dd"));
                    }

                    // 设置应还未还等信息
                    setShouldAndPaidData(repayVO, dueVO);

                    shouldTotalAmount = shouldTotalAmount.add(dueVO.getAmount());
                    if (dueVO.getSettledAmount() != null) {
                        settledTotalAmount = settledTotalAmount.add(dueVO.getSettledAmount());
                    }
                    if (dueVO.getOutstandingAmount() != null) {
                        outstandingTotalAmount = outstandingTotalAmount.add(dueVO.getOutstandingAmount());
                    }
                }
                repayVO.setShouldTotalAmount(shouldTotalAmount.toString());
                repayVO.setFactTotalAmount(settledTotalAmount.toString());
                repayVO.setUnpaidTotalAmount(outstandingTotalAmount.toString());
            }
        }
    }

    private void setPaidTerms(RepayVO repayVO, DueVO dueVO, Integer maxIndexNo) {
        if (repayVO.getPaidTerms() == null && dueVO.getOutstandingAmount().doubleValue() > 0) {
            repayVO.setPaidTerms(String.valueOf(Math.max(dueVO.getIndexNo() - 1, 0)));
            repayVO.setUnPayTerms(String.valueOf(maxIndexNo - Math.max(dueVO.getIndexNo() - 1, 0)));
        }
    }

    /**
     * 对应各种费用类型设置响应的金额
     */
    private void setShouldAndPaidData(RepayVO repayVO, DueVO dueVO) {
        switch (dueVO.getDueType()) {
            case DueTypeConstant.handlingFee:
                repayVO.setShouldApproveFee(sumOrigin(repayVO.getShouldApproveFee(), dueVO.getAmount()));
                repayVO.setFactApproveFee(sumOrigin(repayVO.getFactApproveFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidApproveFee(sumOrigin(repayVO.getUnpaidApproveFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.interest:
                repayVO.setShouldInterest(sumOrigin(repayVO.getShouldInterest(), dueVO.getAmount()));
                repayVO.setFactInterest(sumOrigin(repayVO.getFactInterest(), dueVO.getSettledAmount()));
                repayVO.setUnpaidInterest(sumOrigin(repayVO.getUnpaidInterest(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.managementFee:
                repayVO.setShouldMaintenance(sumOrigin(repayVO.getShouldMaintenance(), dueVO.getAmount()));
                repayVO.setFactMaintenanceFee(sumOrigin(repayVO.getFactMaintenanceFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidMaintenanceFee(sumOrigin(repayVO.getUnpaidMaintenanceFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.principal:
                repayVO.setShouldCapital(sumOrigin(repayVO.getShouldCapital(), dueVO.getAmount()));
                repayVO.setFactCapital(sumOrigin(repayVO.getFactCapital(), dueVO.getSettledAmount()));
                repayVO.setUnpaidCapital(sumOrigin(repayVO.getUnpaidCapital(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.overdueInterest:
                repayVO.setShouldOverdueInterest(sumOrigin(repayVO.getShouldOverdueInterest(), dueVO.getAmount()));
                repayVO.setFactOverdueInterest(sumOrigin(repayVO.getFactOverdueInterest(), dueVO.getSettledAmount()));
                repayVO.setUnpaidOverdueInterest(sumOrigin(repayVO.getUnpaidOverdueInterest(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.withdrawalFee:
                repayVO.setShouldWithdrawFee(sumOrigin(repayVO.getShouldWithdrawFee(), dueVO.getAmount()));
                repayVO.setFactWithdrawFee(sumOrigin(repayVO.getFactWithdrawFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidWithdrawFee(sumOrigin(repayVO.getUnpaidWithdrawFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.depositFee:
            case DueTypeConstant.collectionFee:
                repayVO.setShouldWithholdFee(sumOrigin(repayVO.getShouldWithholdFee(), dueVO.getAmount()));
                repayVO.setFactWithholdFee(sumOrigin(repayVO.getFactWithholdFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidWithholdFee(sumOrigin(repayVO.getUnpaidWithholdFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.overdueManagementFee:
                repayVO.setShouldOverdueMaintenanceFee(sumOrigin(repayVO.getShouldOverdueMaintenanceFee(), dueVO.getAmount()));
                repayVO.setFactOverdueMaintenanceFee(sumOrigin(repayVO.getFactOverdueMaintenanceFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidOverdueMaintenanceFee(sumOrigin(repayVO.getUnpaidOverdueMaintenanceFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.overduePenalty:
            case DueTypeConstant.overdueFee:
                repayVO.setShouldOverduePenalty(sumOrigin(repayVO.getShouldOverduePenalty(), dueVO.getAmount()));
                repayVO.setFactOverduePenalty(sumOrigin(repayVO.getFactOverduePenalty(), dueVO.getSettledAmount()));
                repayVO.setUnpaidOverduePenalty(sumOrigin(repayVO.getUnpaidOverduePenalty(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.earlySettleFee:
                repayVO.setShouldEarlyPenalty(sumOrigin(repayVO.getShouldEarlyPenalty(), dueVO.getAmount()));
                repayVO.setFactEarlyPenalty(sumOrigin(repayVO.getFactEarlyPenalty(), dueVO.getSettledAmount()));
                repayVO.setUnpaidEarlyPenalty(sumOrigin(repayVO.getUnpaidEarlyPenalty(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.outsourcingFee:
                repayVO.setShouldOaFee(sumOrigin(repayVO.getShouldOaFee(), dueVO.getAmount()));
                repayVO.setFactOaFee(sumOrigin(repayVO.getFactOaFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidOaFee(sumOrigin(repayVO.getUnpaidOaFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.insuranceFee:
                repayVO.setShouldInsureFee(sumOrigin(repayVO.getShouldInsureFee(), dueVO.getAmount()));
                repayVO.setFactInsureFee(sumOrigin(repayVO.getFactInsureFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidOaFee(sumOrigin(repayVO.getUnpaidOaFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.premium:
                repayVO.setShouldMobileGuaranteeFee(sumOrigin(repayVO.getShouldMobileGuaranteeFee(), dueVO.getAmount()));
                repayVO.setFactMobileGuaranteeFee(sumOrigin(repayVO.getFactMobileGuaranteeFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidMobileGuaranteeFee(sumOrigin(repayVO.getUnpaidMobileGuaranteeFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.buyoutFee:
                repayVO.setShouldMobileBuyOutFee(sumOrigin(repayVO.getShouldMobileBuyOutFee(), dueVO.getAmount()));
                repayVO.setFactMobileBuyOutFee(sumOrigin(repayVO.getFactMobileBuyOutFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidMobileBuyOutFee(sumOrigin(repayVO.getUnpaidMobileBuyOutFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.damageFee:
                repayVO.setShouldMobileDamageFee(sumOrigin(repayVO.getShouldMobileDamageFee(), dueVO.getAmount()));
                repayVO.setFactMobileDamageFee(sumOrigin(repayVO.getFactMobileDamageFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidMobileDamageFee(sumOrigin(repayVO.getUnpaidMobileDamageFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.coupon:
                repayVO.setShouldRedPack(sumOrigin(repayVO.getShouldRedPack(), dueVO.getAmount()));
                repayVO.setFactRedPack(sumOrigin(repayVO.getFactRedPack(), dueVO.getSettledAmount()));
                repayVO.setUnpaidRedPack(sumOrigin(repayVO.getUnpaidRedPack(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.guaranteeFee:
                repayVO.setShouldGuaranteeFee(sumOrigin(repayVO.getShouldGuaranteeFee(), dueVO.getAmount()));
                repayVO.setFactGuaranteeFee(sumOrigin(repayVO.getFactGuaranteeFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidGuaranteeFee(sumOrigin(repayVO.getUnpaidGuaranteeFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.riskServiceFee:
                repayVO.setShouldGuaranteeServiceFee(sumOrigin(repayVO.getShouldGuaranteeServiceFee(), dueVO.getAmount()));
                repayVO.setFactGuaranteeServiceFee(sumOrigin(repayVO.getFactGuaranteeServiceFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidGuaranteeServiceFee(sumOrigin(repayVO.getUnpaidGuaranteeServiceFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.riskMargin:
                repayVO.setShouldGuaranteeAmount(sumOrigin(repayVO.getShouldGuaranteeAmount(), dueVO.getAmount()));
                repayVO.setFactGuaranteeAmount(sumOrigin(repayVO.getFactGuaranteeAmount(), dueVO.getSettledAmount()));
                repayVO.setUnpaidGuaranteeAmount(sumOrigin(repayVO.getUnpaidGuaranteeAmount(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.assessServiceFee:
                repayVO.setShouldReviewFee(sumOrigin(repayVO.getShouldReviewFee(), dueVO.getAmount()));
                repayVO.setFactReviewFee(sumOrigin(repayVO.getFactReviewFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidReviewFee(sumOrigin(repayVO.getUnpaidReviewFee(), dueVO.getOutstandingAmount()));
                break;
            case DueTypeConstant.guaranteeConsultingFee:
                repayVO.setShouldGuaranteeAskFee(sumOrigin(repayVO.getShouldGuaranteeAskFee(), dueVO.getAmount()));
                repayVO.setFactGuaranteeAskFee(sumOrigin(repayVO.getFactGuaranteeAskFee(), dueVO.getSettledAmount()));
                repayVO.setUnpaidGuaranteeAskFee(sumOrigin(repayVO.getUnpaidGuaranteeAskFee(), dueVO.getOutstandingAmount()));
                break;
            default:
                log.info("RepayUploadService invalid dueType: {}", dueVO.getDueType());
        }
    }

    private String sumOrigin(String sum, BigDecimal origin) {
        BigDecimal result = new BigDecimal(sum).add(new BigDecimal(getStringAmount(origin)));
        return result.toString();
    }

    private String getStringAmount(BigDecimal decimal) {
        if (decimal == null) {
            return "0";
        } else {
            return decimal.toString();
        }
    }

    private void setProductName(String uuid, LoanApplicationDTO application, RepayVO repayVO) {
        try {
            ProductDTO product = productServiceFacade.getProductById(application.getWelabProductId());
            if (Objects.nonNull(product)) {
                repayVO.setProductName(product.getName());
            }
        } catch (Exception e) {
            log.warn("RepayUploadService getProductById error, uuid: {}, productId:{} ", uuid, application.getWelabProductId());
        }
    }

    private String getFormatTime(Date date) {
        if (date != null) {
            return DateFormatUtils.format(date, "yyyy-MM-dd HH:mm");
        } else {
            return null;
        }
    }

    private String saveAndGetFileUrl(Long id, List<RepayVO> repayList) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            Set<String> names = getExcludeFieldNames(repayList);
            EasyExcel.write(out, RepayVO.class).sheet("还款明细").excludeColumnFiledNames(names).doWrite(repayList);
        } catch (Exception e) {
            log.error("RepayUploadService EasyExcel.write error: {}", e.getMessage(), e);
            throw new CrmInterviewException("RepayUploadService EasyExcel.write error");
        }

        Response<String> uploadResponse = iUploadService.uploadFile(out.toByteArray(), "repay_" + System.currentTimeMillis() + ".xlsx");
        String url = "urlError";
        if (Response.isSuccess(uploadResponse)) {
            log.info("RepayUploadService upload success, id:{}", id);
            String fileName = uploadResponse.getResult();
            Map<String, Object> filePathMap = iUploadService.getUploadFile(Collections.singletonList(fileName));
            // 返回的文件oss下载路径
            url = (String) filePathMap.get(fileName);
        }
        return url;
    }

    /**
     * 计算空值的列，以便导出时排除
     */
    private Set<String> getExcludeFieldNames(List<RepayVO> repayList) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(repayList)) {
            return Collections.emptySet();
        }
        Set<String> allNames = getAllNames(); // 所有需要处理的字段名称
        Set<String> collectNames = new HashSet<>(); // 所有需要展示的字段名称
        for (RepayVO vo : repayList) {
            Field[] fields = vo.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (needCount(field)) {
                    field.setAccessible(true);
                    Object object = field.get(vo);
                    if (object != null) {
                        String value = (String) object;
                        if (StringUtils.isNotBlank(value) && Double.parseDouble(value) != 0) {
                            collectNames.add(field.getName());
                        }
                    }

                }
            }
        }
        // 取差集即可求出所有需要过滤的excel显示字段
        allNames.removeAll(collectNames);
        return allNames;
    }

    private Set<String> getAllNames() {
        Field[] fields = RepayVO.class.getDeclaredFields();
        Set<String> collectNames = new HashSet<>();
        for (Field field : fields) {
            if (needCount(field)) {
                collectNames.add(field.getName());
            }
        }
        return collectNames;
    }

    private boolean needCount(Field field) {
        return field.getName().startsWith("should") || field.getName().startsWith("fact")
                || field.getName().startsWith("unpaid");
    }

}
