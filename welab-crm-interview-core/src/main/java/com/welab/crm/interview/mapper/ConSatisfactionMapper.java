package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.ConSatisfaction;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 满意度调查表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-18
 */
public interface ConSatisfactionMapper extends BaseMapper<ConSatisfaction> {

    /**
     * 查询坐席满意度报表
     *
     * @param dto
     * @return
     */
    List<ReportSatisfactionVO> selectSatisfactionReport(@Param("cond") RepeatCallDTO dto,
        @Param("list") List<String> hotlines);

    /**
     * 查询坐席满意度报表数据量
     *
     * @param dto
     * @return
     */
    int selectSatisfactionCount(@Param("cond") RepeatCallDTO dto, @Param("list") List<String> hotlines);
}
