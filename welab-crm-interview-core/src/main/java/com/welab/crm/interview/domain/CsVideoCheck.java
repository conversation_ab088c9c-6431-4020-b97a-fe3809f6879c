package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客服视频核验记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_video_check")
public class CsVideoCheck implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联data_customer表的ID
     */
    private Long customerId;

    /**
     * 发送手机号
     */
    private String mobile;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 操作人
     */
    private Long staffId;

    /**
     * 是否本人。0-非本人，1-本人
     */
    private Boolean imgCode;

    /**
     * 录音是否符合逾期。1-符合，0-不符合
     */
    private Boolean voiceCode;

    /**
     * 对应每条发送记录的token
     */
    private String token;

    /**
     * oss文件名称，用于下载
     */
    private String videoName;

    /**
     * 是否收藏；0-未收藏，1-收藏
     */
    private Boolean isCollect;

    /**
     * 对应短信发送记录ID
     */
    private Long smsLogId;

    /**
     * 结果信息
     */
    private String resultMsg;

    /**
     * 是否为同一人的分值
     */
    private String imgScore;

    /**
     * 所读文本相似度
     */
    private String voiceScore;

    /**
     * 开始录制时间
     */
    private Date startRecordTime;

    /**
     * 结束录制时间
     */
    private Date endRecordTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 客户需要念的话
     */
    private String readMsg;


    /**
     * 用户ID
     */
    private Integer userId;


    /**
     * uuid
     */
    private String uuid;

    /**
     * 源文件名称
     */
    private String sourceVideoName;


}
