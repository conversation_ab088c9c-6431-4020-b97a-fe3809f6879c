package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.config.AliyunQualityCheckConfig;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.welab.crm.interview.dto.QualityInspectionCallbackDTO;
import com.welab.crm.interview.dto.QualityInspectionRequestDTO;
import com.welab.crm.interview.dto.QualityInspectionResultDTO;
import com.welab.crm.interview.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.interview.service.QualityInspectionService;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.util.AliyunQualityCheckClient;
import com.welab.crm.interview.util.AliyunSignatureUtil;
import com.welab.crm.interview.util.QualityInspectionThreadPoolMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云质检服务实现类（优化版）
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Service
@Slf4j
public class QualityInspectionServiceImpl implements QualityInspectionService {

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Resource
    private TrTokenService trTokenService;

    @Resource
    private QualityInspectionThreadPoolMonitor threadPoolMonitor;

    @Autowired
    private AliyunQualityCheckConfig config;

    @Autowired
    private AliyunQualityCheckClient aliyunClient;

    /**
     * 回调签名验证的最大时间差（5分钟）
     */
    private static final long MAX_CALLBACK_TIME_DIFF = 5 * 60 * 1000L;

    /**
     * 质检专用线程池，参考项目中现有的异步处理模式
     * 使用 CallerRunsPolicy 拒绝策略，确保任务不会丢失
     */
    private ThreadPoolExecutor qualityInspectionExecutor;

    @PostConstruct
    public void initExecutor() {
        // 初始化可配置的线程池
        int coreSize = 10;
        int maxSize = 20;
        long keepAlive = 300L;
        int queueSize = 500;

        qualityInspectionExecutor = new ThreadPoolExecutor(
                coreSize, maxSize, keepAlive, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(queueSize),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        // 允许核心线程超时，防止不必要的空耗线程
        qualityInspectionExecutor.allowCoreThreadTimeOut(true);

        log.info("阿里云质检线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}，保活时间：{}秒",
                coreSize, maxSize, queueSize, keepAlive);

        // 检查配置
        if (!aliyunClient.isConfigValid()) {
            log.warn("阿里云质检配置不完整，质检功能可能无法正常工作");
        }
    }

    @Override
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public String submitQualityInspection(QualityInspectionRequestDTO requestDTO) {
        try {
            if (!config.getEnabled()) {
                log.info("阿里云质检功能已禁用");
                return null;
            }

            log.info("提交阿里云质检任务，参数：{}", JSON.toJSONString(requestDTO));
            String taskId = aliyunClient.uploadAudioData(requestDTO);

            if (StringUtils.isNotBlank(taskId)) {
                log.info("阿里云质检任务提交成功，taskId：{}", taskId);
                return taskId;
            } else {
                log.error("阿里云质检任务提交失败");
                return null;
            }
        } catch (Exception e) {
            log.error("提交阿里云质检任务异常", e);
            throw e; // 重新抛出异常以触发重试
        }
    }

    @Override
    public QualityInspectionResultDTO getQualityInspectionResult(String taskId) {
        try {
            if (!config.getEnabled()) {
                log.info("阿里云质检功能已禁用");
                return null;
            }

            log.info("获取阿里云质检结果，任务ID：{}", taskId);
            QualityInspectionResultDTO result = aliyunClient.getResult(taskId, config.getBaseMeAgentId());

            if (result != null) {
                log.info("阿里云质检结果获取成功，taskId：{}，status：{}，score：{}",
                        taskId, result.getStatus(), result.getScore());
            } else {
                log.warn("阿里云质检结果获取失败，taskId：{}", taskId);
            }

            return result;
        } catch (Exception e) {
            log.error("获取阿里云质检结果异常，taskId：{}", taskId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleQualityInspectionCallback(QualityInspectionCallbackDTO callbackDTO) {
        log.info("处理阿里云质检回调，参数：{}", JSON.toJSONString(callbackDTO));

        try {
            // 验证回调签名
            if (!verifyCallbackSignature(callbackDTO)) {
                log.error("阿里云质检回调签名验证失败，拒绝处理：{}", callbackDTO.getTaskId());
                return;
            }

            // 验证事件类型
            if (!"TaskComplete".equals(callbackDTO.getEvent())) {
                log.warn("阿里云质检回调事件类型不匹配，忽略：{}，事件：{}",
                        callbackDTO.getTaskId(), callbackDTO.getEvent());
                return;
            }

            // 根据任务ID查找对应的通话记录
            LambdaQueryWrapper<ConPhoneCallInfo> queryWrapper = Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getQualityInspectionTaskId, callbackDTO.getTaskId());

            ConPhoneCallInfo phoneCallInfo = conPhoneCallInfoMapper.selectOne(queryWrapper);
            if (Objects.isNull(phoneCallInfo)) {
                log.warn("未找到对应的通话记录，任务ID：{}", callbackDTO.getTaskId());
                return;
            }

            // 获取详细的质检结果
            QualityInspectionResultDTO resultDTO = getQualityInspectionResult(callbackDTO.getTaskId());

            // 更新通话记录的质检信息
            updatePhoneCallQualityInfo(phoneCallInfo, callbackDTO, resultDTO);

            log.info("阿里云质检回调处理完成，通话ID：{}，状态：{}",
                    phoneCallInfo.getCdrMainUniqueId(), phoneCallInfo.getQualityInspectionStatus());
            
        } catch (Exception e) {
            log.error("处理阿里云质检回调异常", e);
            throw e;
        }
    }

    /**
     * 验证回调签名
     * @param callbackDTO 回调数据
     * @return 验证结果
     */
    private boolean verifyCallbackSignature(QualityInspectionCallbackDTO callbackDTO) {
        try {
            // 验证时间戳
            if (!AliyunSignatureUtil.isTimestampValid(callbackDTO.getTimestamp(), MAX_CALLBACK_TIME_DIFF)) {
                return false;
            }

            // 验证签名
            boolean isValid = AliyunSignatureUtil.verifySignature(
                    callbackDTO.getTaskId(),
                    callbackDTO.getTimestamp(),
                    callbackDTO.getAliUid(),
                    callbackDTO.getSignature()
            );

            callbackDTO.setSignatureValid(isValid);
            return isValid;
        } catch (Exception e) {
            log.error("验证回调签名异常", e);
            return false;
        }
    }

    /**
     * 更新通话记录的质检信息
     * @param phoneCallInfo 通话记录
     * @param callbackDTO 回调数据
     * @param resultDTO 质检结果
     */
    private void updatePhoneCallQualityInfo(ConPhoneCallInfo phoneCallInfo,
                                          QualityInspectionCallbackDTO callbackDTO,
                                          QualityInspectionResultDTO resultDTO) {
        if (callbackDTO.getStatus() == 1 && resultDTO != null) {
            // 质检成功
            phoneCallInfo.setQualityInspectionStatus(2); // 已完成
            phoneCallInfo.setQualityInspectionScore(resultDTO.getScore());
            phoneCallInfo.setQualityInspectionTime(new Date());

            // 可以存储更多质检详情
            if (StringUtils.isNotBlank(resultDTO.getComments())) {
                // 如果有复核意见，可以存储到备注字段
                // phoneCallInfo.setQualityInspectionComments(resultDTO.getComments());
            }
        } else {
            // 质检失败
            phoneCallInfo.setQualityInspectionStatus(3); // 检查失败
            phoneCallInfo.setQualityInspectionTime(new Date());
            log.error("阿里云质检失败，任务ID：{}，错误信息：{}",
                    callbackDTO.getTaskId(), callbackDTO.getErrorMessage());
        }

        phoneCallInfo.setGmtModify(new Date());
        conPhoneCallInfoMapper.updateById(phoneCallInfo);
    }

    /**
     * 构建阿里云质检请求
     * @param phoneCallInfo 通话记录
     * @param recordingUrl 录音文件URL
     * @param callType 通话类型
     * @param customerNumber 客户号码
     * @param agentNumber 座席号码
     * @return 质检请求DTO
     */
    private QualityInspectionRequestDTO buildAliyunQualityRequest(ConPhoneCallInfo phoneCallInfo,
                                                                String recordingUrl, String callType,
                                                                String customerNumber, String agentNumber) {
        QualityInspectionRequestDTO requestDTO = new QualityInspectionRequestDTO();

        // 设置基本参数
        requestDTO.setBaseMeAgentId(config.getBaseMeAgentId());
        requestDTO.setAutoSplit(config.getAutoSplit());
        requestDTO.setServiceChannel(config.getServiceChannel());
        requestDTO.setClientChannel(config.getClientChannel());
        requestDTO.setSampleRate(config.getSampleRate());
        requestDTO.setCallbackUrl(config.getCallbackUrl());

        // 设置客服关键词（用于单轨录音角色分离）
        if (config.getServiceChannelKeywords() != null) {
            requestDTO.setServiceChannelKeywords(Arrays.asList(config.getServiceChannelKeywords()));
        }

        // 构建录音文件信息
        QualityInspectionRequestDTO.CallInfo callInfo = new QualityInspectionRequestDTO.CallInfo();
        callInfo.setVoiceFileUrl(recordingUrl);
        callInfo.setFileName(phoneCallInfo.getCdrRecordFile());
        callInfo.setCallStartTime(phoneCallInfo.getCdrStartTime() != null ?
                phoneCallInfo.getCdrStartTime().getTime() : System.currentTimeMillis());
        callInfo.setCallId(phoneCallInfo.getCdrMainUniqueId());
        callInfo.setCallType("1".equals(callType) ? 3 : 1); // 阿里云：1呼出，3呼入
        callInfo.setCaller("1".equals(callType) ? customerNumber : agentNumber);
        callInfo.setCallee("1".equals(callType) ? agentNumber : customerNumber);
        callInfo.setBusiness("客服业务线");
        callInfo.setCallUuid(phoneCallInfo.getCdrMainUniqueId()); // 幂等标识

        // 设置客服信息
        if (StringUtils.isNotBlank(agentNumber)) {
            callInfo.setCustomerServiceName(agentNumber);
        }

        // 设置自定义数据
        callInfo.setRemark1(phoneCallInfo.getCdrQueue()); // 队列信息
        callInfo.setRemark2(phoneCallInfo.getCdrEndReason()); // 结束原因
        callInfo.setRemark3(callType); // 通话类型

        requestDTO.setCallList(Collections.singletonList(callInfo));

        return requestDTO;
    }

    @Override
    public void submitPhoneRecordQualityInspection(String cdrMainUniqueId, String recordFile,
                                                 String callType, String customerNumber, String agentNumber) {
        // 监控线程池状态
        threadPoolMonitor.monitorThreadPool(qualityInspectionExecutor, "QualityInspectionPool");

        // 使用 CompletableFuture 异步执行，参考项目中现有的异步处理模式
        CompletableFuture.runAsync(() -> {
            doSubmitPhoneRecordQualityInspection(cdrMainUniqueId, recordFile, callType, customerNumber, agentNumber);
        }, qualityInspectionExecutor).exceptionally(throwable -> {
            log.error("异步提交电话录音质检任务执行异常，通话ID：{}", cdrMainUniqueId, throwable);
            // 异常时也要处理状态
            handleQualityInspectionException(cdrMainUniqueId, (Exception) throwable);
            return null;
        });
    }

    /**
     * 实际执行质检任务提交的方法
     */
    private void doSubmitPhoneRecordQualityInspection(String cdrMainUniqueId, String recordFile,
                                                    String callType, String customerNumber, String agentNumber) {
        log.info("异步提交电话录音质检，通话ID：{}，录音文件：{}", cdrMainUniqueId, recordFile);

        try {
            // 检查是否已经提交过质检
            LambdaQueryWrapper<ConPhoneCallInfo> queryWrapper = Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getCdrMainUniqueId, cdrMainUniqueId);

            ConPhoneCallInfo phoneCallInfo = conPhoneCallInfoMapper.selectOne(queryWrapper);
            if (Objects.isNull(phoneCallInfo)) {
                log.warn("未找到对应的通话记录：{}", cdrMainUniqueId);
                return;
            }

            // 如果已经在质检中或已完成，则跳过
            if (phoneCallInfo.getQualityInspectionStatus() != null && phoneCallInfo.getQualityInspectionStatus() > 0) {
                log.info("通话记录已经提交质检或已完成，跳过：{}", cdrMainUniqueId);
                return;
            }

            // 获取录音文件URL
            String enterpriseId = recordFile.substring(0, 7);
            String recordingUrl = trTokenService.getWaveSoundUrl(enterpriseId, recordFile);

            if (StringUtils.isBlank(recordingUrl)) {
                log.warn("无法获取录音文件URL：{}", recordFile);
                return;
            }

            // 构建阿里云质检请求
            QualityInspectionRequestDTO requestDTO = buildAliyunQualityRequest(
                    phoneCallInfo, recordingUrl, callType, customerNumber, agentNumber);

            // 提交质检任务
            String taskId = submitQualityInspection(requestDTO);

            if (StringUtils.isNotBlank(taskId)) {
                // 更新质检状态为检查中
                phoneCallInfo.setQualityInspectionStatus(1); // 检查中
                phoneCallInfo.setQualityInspectionTaskId(taskId);
                phoneCallInfo.setGmtModify(new Date());
                conPhoneCallInfoMapper.updateById(phoneCallInfo);

                log.info("质检任务提交成功，通话ID：{}，任务ID：{}", cdrMainUniqueId, taskId);
            } else {
                log.error("质检任务提交失败，通话ID：{}", cdrMainUniqueId);
                // 可以考虑设置失败状态或重试机制
                handleQualityInspectionSubmitFailure(phoneCallInfo, cdrMainUniqueId);
            }

        } catch (Exception e) {
            log.error("异步提交电话录音质检异常，通话ID：{}", cdrMainUniqueId, e);
            // 异常处理：可以考虑设置失败状态
            handleQualityInspectionException(cdrMainUniqueId, e);
        }
    }

    /**
     * 处理质检任务提交失败的情况
     */
    private void handleQualityInspectionSubmitFailure(ConPhoneCallInfo phoneCallInfo, String cdrMainUniqueId) {
        try {
            // 可以根据业务需求决定是否设置失败状态，这里暂时不设置状态，保持为0（未检查）
            log.warn("质检任务提交失败，通话ID：{}，保持未检查状态", cdrMainUniqueId);
            // 如果需要标记为失败状态，可以取消下面的注释
            // phoneCallInfo.setQualityInspectionStatus(3); // 检查失败
            // phoneCallInfo.setGmtModify(new Date());
            // conPhoneCallInfoMapper.updateById(phoneCallInfo);
        } catch (Exception e) {
            log.error("处理质检任务提交失败异常，通话ID：{}", cdrMainUniqueId, e);
        }
    }

    /**
     * 处理质检过程中的异常
     */
    private void handleQualityInspectionException(String cdrMainUniqueId, Exception originalException) {
        try {
            // 查询通话记录
            LambdaQueryWrapper<ConPhoneCallInfo> queryWrapper = Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getCdrMainUniqueId, cdrMainUniqueId);

            ConPhoneCallInfo phoneCallInfo = conPhoneCallInfoMapper.selectOne(queryWrapper);
            if (Objects.nonNull(phoneCallInfo)) {
                // 根据异常类型决定处理策略
                if (originalException instanceof java.net.SocketTimeoutException
                    || originalException instanceof java.net.ConnectException) {
                    // 网络异常，保持未检查状态，可能稍后重试
                    log.warn("质检任务网络异常，通话ID：{}，保持未检查状态以便重试", cdrMainUniqueId);
                } else {
                    // 其他异常，可以考虑标记为失败
                    log.error("质检任务异常，通话ID：{}，标记为检查失败", cdrMainUniqueId);
                    phoneCallInfo.setQualityInspectionStatus(3); // 检查失败
                    phoneCallInfo.setGmtModify(new Date());
                    conPhoneCallInfoMapper.updateById(phoneCallInfo);
                }
            }
        } catch (Exception e) {
            log.error("处理质检异常时发生错误，通话ID：{}", cdrMainUniqueId, e);
        }
    }
}
