package com.welab.crm.interview.service.impl;

import com.alibaba.druid.util.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.constant.SmsKeyConstant;
import com.welab.crm.interview.domain.CsVideoCheck;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.CsVideoCheckMapper;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.service.VideoCheckService;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.DocumentVo;
import com.welab.crm.interview.vo.VideoCheckResultVO;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.document.interfaces.dto.DocumentDTO;
import com.welab.exception.FastRuntimeException;
import com.welab.security.util.MD5Util;
import com.welab.usercenter.service.ProfileService;
import com.welab.web.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 视频验证服务类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoCheckServiceImpl implements VideoCheckService {

    @Resource
    private JedisCommands jedisCommands;

    @Resource
    private CsVideoCheckMapper csVideoCheckMapper;

    @Resource
    private IUploadService uploadService;

    @Autowired
    private LoansApplicationServiceImpl loansApplicationService;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;
    
    @Resource
    private ProfileService profileService;

    @Value("${video.check.app.key}")
    private String appKey;

    @Value("${video.check.token}")
    private String userToken;

    @Value("${video.check.url}")
    private String checkUrl;

    @Override
    public String getReadMsg(String token) {
        log.info("getReadMsg start,token:{}", token);
        if (StringUtils.isBlank(token)) {
            log.warn("getReadMsg token校验失败");
            throw new CrmInterviewException("链接已过期");
        }
        String tokenTime = jedisCommands.get(SmsKeyConstant.REDIS_PRE_TOKEN_KEY + token);
        if (StringUtils.isBlank(tokenTime) || Integer.parseInt(tokenTime) <= 0) {
            throw new CrmInterviewException("链接已过期");
        }
        if (jedisCommands.exists(SmsKeyConstant.REDIS_PRE_TOKEN_KEY + token)) {
            jedisCommands.decr(SmsKeyConstant.REDIS_PRE_TOKEN_KEY + token);
        }

        CsVideoCheck check = csVideoCheckMapper.selectOne(Wrappers.lambdaQuery(CsVideoCheck.class)
            .eq(CsVideoCheck::getToken, token).select(CsVideoCheck::getReadMsg));
        if (Objects.isNull(check)) {
            log.warn("getReadMsg,查不到token信息");
            throw new CrmInterviewException("链接已过期");
        }
        return check.getReadMsg();
    }

    @Override
    public void uploadVideoAndPushToAi(byte[] bytes, byte[] sourceBytes, String token, Date startTime,
        Date endTime) {

        if (jedisCommands.exists(SmsKeyConstant.REDIS_PRE_UPLOAD_KEY + token)) {
            jedisCommands.decr(SmsKeyConstant.REDIS_PRE_UPLOAD_KEY + token);
        }

        CsVideoCheck check =
            csVideoCheckMapper.selectOne(Wrappers.lambdaQuery(CsVideoCheck.class).eq(CsVideoCheck::getToken, token));

        // 上传转码后的文件
        Response<String> res = uploadService.uploadFile(bytes, "fix_" + token + ".mp4");
        if (Response.isSuccess(res)) {
            String ossFileName = res.getResult();
            // 获取下载链接，有效时间一天
            Map<String, Object> map =
                uploadService.getUploadFileSurvivalTime(Arrays.asList(ossFileName), 60 * 60 * 24L);
            String videoUrl = (String)map.get(ossFileName);

            log.info("uploadVideoAndPushToAi 视频下载链接:{}", videoUrl);

            // 获取用户底照
            String userPhotoUrl = getUserPhotoUrl(check.getUserId());
            log.info("用户底照链接:{}", userPhotoUrl);

            
            // 请求ai接口，获取比对结果
            VideoCheckResultVO resultVO = checkVideoAndPhone(videoUrl, userPhotoUrl, check.getReadMsg(),getMustList(check));
            check.setStartRecordTime(startTime);
            check.setEndRecordTime(endTime);
            check.setVideoName(ossFileName);
            check.setGmtModify(new Date());
            check.setImgCode(resultVO.getImg_code());
            check.setVoiceCode(resultVO.getVoice_code());
            check.setImgScore(resultVO.getImg_score());
            check.setVoiceScore(resultVO.getVoice_score());
            check.setResultMsg(resultVO.getMsg());
            //上传源文件
            Response<String> sourceRes = uploadService.uploadFile(sourceBytes, token + ".mp4");
            if (Response.isSuccess(sourceRes)){
                check.setSourceVideoName(sourceRes.getResult());
            }
            csVideoCheckMapper.updateById(check);
        } else {
            log.warn("uploadVideoAndPushToAi 上传文件到oss失败,token:{}", token);
            throw new FastRuntimeException("上传视频失败");
        }

    }

    private List<String> getMustList(CsVideoCheck check) {
        List<OpDictInfo> opDictInfoList = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class)
                .eq(OpDictInfo::getCategory, "video_read_msg").eq(OpDictInfo::getStatus, 1));
        List<String> mustList = opDictInfoList.stream().filter(item -> item.getType().contains("must"))
                .map(OpDictInfo::getDetail).collect(Collectors.toList());
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("name",check.getCustomerName());
        paramsMap.put("mobile",check.getMobile());
        paramsMap.put("cnid", profileService.getProfileByUserId(check.getUserId()).getCnid());

        List<String> mustValueList = new ArrayList<>();
        for (String mustStr : mustList) {
            if (mustStr.contains("$")) {
                mustValueList.add(StringUtil.replacePlaceholders(mustStr, paramsMap));
            } else {
                mustValueList.add(mustStr);
            }
        }
        
        return mustValueList;
    }

    /**
     * 调用创研接口校验视频
     * @param videoUrl 视频下载链接
     * @param userPhotoUrl 用户底照链接
     * @param readMsg 用户需要读的信息
     * @param mustList 必须读对的信息片段
     * @return
     */
    public VideoCheckResultVO checkVideoAndPhone(String videoUrl, String userPhotoUrl, String readMsg, List<String> mustList) {
        String responseStr;
        try {

            Map<String, String> headers = getHeaders();
            String requestBody = getJsonParams(videoUrl, userPhotoUrl, readMsg,mustList);
            HttpResponse<String> response = Unirest.post(checkUrl).headers(headers).body(requestBody).asString();
            responseStr = response.getBody();
            log.info("checkVideoAndPhone 视频校验返回数据:{}", responseStr);
        } catch (Exception e) {
            log.error("checkVideoAndPhone error", e);
            throw new CrmInterviewException("校验视频异常");
        }

        ResponseVO<JSONObject> response = JSON.parseObject(responseStr, ResponseVO.class);
        if (response == null || response.getRet() != 0) {
            log.warn("checkVideoAndPhone 失败: {}", response == null ? "response空" : response);
            throw new CrmInterviewException("视频校验结果:" + (response == null ? "response null" : response.getMsg()));
        } else if (Objects.isNull(response.getData())) {
            throw new CrmInterviewException("视频校验结果为空");
        } else {
            return JSON.toJavaObject(response.getData(),VideoCheckResultVO.class);
        }
    }


    private String getJsonParams(String videoUrl, String userPhotoUrl, String readMsg, List<String> mustList) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("photo_url", userPhotoUrl);
        jsonObject.put("content", readMsg);
        jsonObject.put("video_url", videoUrl);
        jsonObject.put("must_match", mustList);
        return jsonObject.toJSONString();
    }

    public String getUserPhotoUrl(Integer userId) {
        // 用户上传文档列表，按照时间降序排列
        List<DocumentDTO> documents = loansApplicationService.getDocumentsByUuid(userId);
        if (CollectionUtils.isEmpty(documents)) {
            throw new CrmInterviewException("获取用户底照失败");
        }
        for (DocumentDTO document : documents) {
            if (document.getDocType().equals(DocumentVo.DocType.FACE_RECOGNITION.getValue())) {
                return document.getOriginalUrl();
            }
        }
        throw new CrmInterviewException("获取用户底照失败");
    }

    private Map<String, String> getHeaders() {
        Map<String, String> paramMap = new HashMap<>();
        // 授权认证参数封装
        String flowCode = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis();
        paramMap.put("flow_code", flowCode);
        paramMap.put("timestamp", timestamp + "");
        paramMap.put("sign", MD5Util.md5(appKey + timestamp + flowCode));
        paramMap.put("token", userToken);
        paramMap.put("Content-Type", "application/json");
        return paramMap;
    }

}
