package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 运营系统uuid数据关联外呼任务配置表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_uuid_config")
public class TmkUuidConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 外呼任务id
     */
    private Long configId;

    /**
     * 号码包id
     */
    private String numPackageId;

    /**
     * 号码包定义
     */
    private String numPackageDef;

    /**
     * 外呼说明
     */
    private String callInstruction;

    /**
     * ai话术
     */
    private Integer speechId;

    /**
     * 话术所属的任务id
     */
    private Integer taskId;

    /**
     * 外呼方式: ai-ai外呼, man-人工外呼
     */
    private String callType;

    /**
     * 创建人userId
     */
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 软删除标识: 0-未删除 1-已删除
     */
    private Boolean deleteFlag;
}
