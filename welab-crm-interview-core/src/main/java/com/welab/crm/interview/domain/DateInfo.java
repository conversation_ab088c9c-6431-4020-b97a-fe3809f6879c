package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 日期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("date_info")
public class DateInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 公历年份
     */
    private Integer year;

    /**
     * 公历月份
     */
    private Integer month;

    /**
     * 公历日期
     */
    private Integer date;

    /**
     * 公历一年中的第几周
     */
    private Integer yearWeek;

    /**
     * 公历一年中的第几天
     */
    private Integer yearDay;

    /**
     * 农历年份
     */
    private Integer lunarYear;

    /**
     * 农历月份
     */
    private Integer lunarMonth;

    /**
     * 农历日期
     */
    private Integer lunarDate;

    /**
     * 农历一年中的第几天
     */
    private Integer lunarYearday;

    /**
     * 星期几
     */
    private Integer week;

    /**
     * 是否为周末（1:是，2:否）
     */
    private Integer weekend;

    /**
     * 是否为工作日（1:是，2:否）
     */
    private Integer workday;

    /**
     * 节假日（10表示非节假日）
     */
    private Integer holiday;

    /**
     * 其他节假日
     */
    private Integer holidayOr;

    /**
     * 节假日调休（00：非，其他为是）
     */
    private Integer holidayOvertime;

    /**
     * 是否为节日当天（1:是，2:否）
     */
    private Integer holidayToday;

    /**
     * 是否为法定节假日（1:是，2:否）
     */
    private Integer holidayLegal;

    /**
     * 是否为假期节假日（1:是，2:否）
     */
    private Integer holidayRecess;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;


}
