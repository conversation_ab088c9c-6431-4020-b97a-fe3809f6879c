package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.OpPartnerInfo;
import com.welab.crm.interview.mapper.OpPartnerInfoMapper;
import com.welab.crm.interview.service.KfPartnerInfoService;
import com.welab.crm.interview.vo.partnerInfo.PartnerInfoVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 资金方服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class KfPartnerInfoServiceImpl implements KfPartnerInfoService {
	
	@Resource
	private OpPartnerInfoMapper opPartnerInfoMapper;
	@Override
	public PartnerInfoVO queryPartnerInfo(String partnerName) {
		OpPartnerInfo partnerInfo = opPartnerInfoMapper.selectOne(Wrappers.lambdaQuery(OpPartnerInfo.class)
				.eq(OpPartnerInfo::getPartnerName, partnerName));
		if (Objects.isNull(partnerInfo)){
			log.warn("没查到资金方信息,partnerName:{}", partnerName);
			throw new FastRuntimeException("没查到资金方信息");
		}
		return convertDomainToDto(partnerInfo);
	}

	private PartnerInfoVO convertDomainToDto(OpPartnerInfo partnerInfo) {
		PartnerInfoVO dto = new PartnerInfoVO();
		BeanUtils.copyProperties(partnerInfo, dto);
		return dto;
	}
}
