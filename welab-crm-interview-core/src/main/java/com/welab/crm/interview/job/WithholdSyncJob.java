package com.welab.crm.interview.job;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.impl.DictInfoServiceImpl;
import com.welab.crm.interview.service.impl.FinanceServiceImpl;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Description: 完善代扣信息定时任务
 * @date 2022/4/21 16:51
 */
@Slf4j
public class WithholdSyncJob implements SimpleJob {

    /**
     * 定时任务计数器
     */
    private static AtomicInteger count = new AtomicInteger(0);

    @Resource
    private FinanceServiceImpl financeService;

    @Resource
    private DictInfoServiceImpl  dictInfoService;


    @Override
    public void execute(ShardingContext shardingContext) {
        Boolean isJobStart = dictInfoService.queryJobSwitch("withholdSync");
        if (!isJobStart){
            log.info("WithholdSyncJob 未开启");
            return;
        }
        Date date = new Date();

        log.info("当前计数器计数:{},时间:{}",count, DateUtil.dateToString(date));
        financeService.syncWithholdRecord(count.get(),date);

        count.getAndIncrement();


    }


}
