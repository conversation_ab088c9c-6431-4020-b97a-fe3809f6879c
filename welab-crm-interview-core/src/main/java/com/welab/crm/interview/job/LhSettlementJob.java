package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.LhSettlementService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 定时获取蓝海银行结清证明
 *
 * <AUTHOR>
 */
@Slf4j
public class LhSettlementJob implements SimpleJob {

    @Resource
    private LhSettlementService lhSettlementService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("开始定时获取蓝海结清文件");
        lhSettlementService.getLhSettlement();
        log.info("定时获取蓝海结清文件结束");
    }
}
