package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.agreement.service.ILoanAgreementService;
import com.welab.capital.dto.CapitalBasicVo;
import com.welab.capital.dto.CapitalVo;
import com.welab.capital.provider.CapitalBasicProvider;
import com.welab.capital.provider.CapitalRuleProvider;
import com.welab.collection.interview.service.ComplainService;
import com.welab.collection.interview.service.ProductService;
import com.welab.collection.interview.vo.CollectionStaffVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.DateUtil.TimeFormatter;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.interview.bo.OpDictInfoBO;
import com.welab.crm.interview.constant.DueTypeEnum;
import com.welab.crm.interview.constant.LoanConstant;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.enums.*;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.mapper.OpImportInfoMapper;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.util.AppUtil;
import com.welab.crm.interview.util.NumberUtil;
import com.welab.crm.interview.util.RandomUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.loan.*;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.finance.account.dto.AccountTransactionsQueryDTO;
import com.welab.finance.account.dubbo.FinanceTransDubboService;
import com.welab.finance.account.vo.AccountTransactionsVo;
import com.welab.finance.accounting.dubbo.AccountTransDubboService;
import com.welab.finance.accounting.vo.AccountTransactionVO;
import com.welab.finance.bankcard.dubbo.BankCardDubboService;
import com.welab.finance.bankcard.vo.BankCardVO;
import com.welab.finance.loanprocedure.dto.DueDTO;
import com.welab.finance.loanprocedure.dto.UserLoanDTO;
import com.welab.finance.loanprocedure.dubbo.DueDubboService;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.dubbo.LoanRateDubboService;
import com.welab.finance.loanprocedure.enums.OwnerTypeEnum;
import com.welab.finance.loanprocedure.vo.DueVO;
import com.welab.finance.loanprocedure.vo.LoanRateVO;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.finance.repayment.dto.RepaymentCalculateDTO;
import com.welab.finance.repayment.dto.RepaymentDTO;
import com.welab.finance.repayment.dto.UserRepaysDTO;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService;
import com.welab.finance.repayment.dubbo.RepaymentDubboService;
import com.welab.finance.repayment.dubbo.RepaymentRecordDubboService;
import com.welab.finance.repayment.enums.RepayOriginEnum;
import com.welab.finance.repayment.enums.UserRepaysStatusEnum;
import com.welab.finance.repayment.vo.RepaymentFeeItemVo;
import com.welab.finance.repayment.vo.RepaymentVo;
import com.welab.finance.repayment.vo.UserRepaysVO;
import com.welab.loanapplication.interfaces.dto.*;
import com.welab.loanapplication.interfaces.facade.CreditApplicationServiceFacade;
import com.welab.loanapplication.interfaces.facade.DeviceInfoServiceFacade;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.loancenter.interfaces.dto.AssetBenefitOrderResponseDTO;
import com.welab.loancenter.interfaces.dto.VipPrivilegeCardDTO;
import com.welab.loancenter.interfaces.facade.AssetBenefitServiceFacade;
import com.welab.loancenter.interfaces.facade.NewMemberServiceFacade;
import com.welab.product.interfaces.dto.ProductDTO;
import com.welab.product.interfaces.facade.ProductServiceFacade;
import com.welab.user.interfaces.dto.NotesDTO;
import com.welab.user.interfaces.facade.NotesServiceFacade;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.model.base.Admin;
import com.welab.usercenter.model.base.Company;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.model.base.User;
import com.welab.usercenter.service.AdminService;
import com.welab.usercenter.service.CompanyService;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import com.welab.util.DateUtils;
import com.welab.xdao.context.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/29
 */
@Slf4j
@Service
public class LoanApplicationServiceImpl implements LoanApplicationService {

    @Resource
    private UserService userService;
    @Resource
    private ProfileService profileService;
    @Resource
    private CompanyService companyService;
    @Resource
    private AdminService adminService;
    @Resource
    private DeviceInfoServiceFacade deviceInfoServiceFacade;
    @Resource
    private CapitalBasicProvider capitalBasicProvider;
    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;
    @Resource
    private CreditApplicationServiceFacade creditApplicationServiceFacade;
    @Resource
    private ProductServiceFacade productServiceFacade;
    @Resource
    private LoanDubboService loanDubboService;
    @Resource
    private RepaymentCalculationDubboService calculationDubboService;
    @Resource
    private BankCardDubboService bankCardDubboService;
    @Resource
    private DueDubboService dueDubboService;
    @Resource
    private RepaymentDubboService repaymentDubboService;
    @Resource
    private NotesServiceFacade notesServiceFacade;
    @Resource
    private FinanceTransDubboService financeTransDubboService;
    @Resource
    private AccountTransDubboService accountTransDubboService;
    @Resource
    private RepaymentRecordDubboService repaymentRecordDubboService;
    @Resource
    private ILoanAgreementService loanAgreementService;
    @Resource
    private LoanRateDubboService loanRateDubboService;
    @Resource
    private ComplainService complainService;
    @Resource
    private CapitalRuleProvider capitalRuleProviderImpl;
    @Resource
    private ProductService productService;
    @Resource
    private NewMemberServiceFacade memberServiceFacade;
    @Resource
    private IUploadService uploadService;
    @Resource
    private UserServiceFacade userServiceFacade;
    
    @Resource
    private OpDictInfoMapper opDictInfoMapper;
    
    @Resource
    private FinanceService financeService;

    @Resource
    private OpImportInfoMapper importInfoMapper;
    
    @Resource
    private OpDictInfoBO opDictInfoBO;

    @Resource
    private AssetBenefitServiceFacade assetBenefitServiceFacade;
 

    @Value("${usercenter.operate.id}")
    private Integer adminId;

    @Value("${lender.http.pre}")
    private String lenderPreUrl;


    /**
     * 债转公司对应号码字典
     */
    private static final String LOAN_TRANSFER_DEPT_DICT = "loan_transfer_dept_mobile";

    /**
     * 模式类型字典
     */
    private static final String LOAN_MODEL_TYPE = "loan_model_type";
    private static final String DEFAULT_MODEL_TYPE = "助贷模式";

    /**
     * @see LoanApplicationService#getLoanApplicationList(Long)
     */
    @Override
    public List<LoanApplicationVO> getLoanApplicationList(Long uuid) {
        List<LoanApplicationVO> appList = new ArrayList<>();
        if (Objects.isNull(uuid)) {
            return appList;
        }
        // 查询贷款申请列表
        List<LoanInfoVO> loanList = getLoanAndCreditByUuid(uuid);
        if (CollectionUtils.isNotEmpty(loanList)) {
            // 先查询进件渠道中文名称
            List<String> originList = loanList.stream().map(LoanInfoVO::getOrigin).collect(Collectors.toList());
            String origins = StringUtils.join(originList, ",");
            Map<String, String> originMap = productService.listChannelNameByCodes(origins);
            Map<String, String> modelTypeMap = opDictInfoBO.queryValidDictMap(LOAN_MODEL_TYPE, "");
            for (LoanInfoVO vo : loanList) {
                LoanApplicationVO applicationVO = new LoanApplicationVO();
                BeanUtils.copyProperties(vo, applicationVO);
                applicationVO.setAppliedAmount(vo.getApplyAmount());
                applicationVO.setAppliedTenor(vo.getApplyTenor());
                applicationVO.setApprovedTenor(vo.getApprovalTenor());
                applicationVO.setAppliedAt(vo.getApplyTime());
                applicationVO.setApprovedAt(vo.getApprovedAt());
                applicationVO.setState(vo.getStatus());
                applicationVO.setDisbursedTime(vo.getDisbursedTime());
                applicationVO.setApprovedAmount(vo.getApprovalAmount());
                LoanApplicationStateEnum stateEnum = LoanApplicationStateEnum.getStateEnum(vo.getStatus());
                if (Objects.nonNull(stateEnum)) {
                    applicationVO.setState(stateEnum.getText());
                }
                String originName = originMap.get(vo.getOrigin());
                if (StringUtils.isNotBlank(originName)) {
                    applicationVO.setOriginName(originName);
                } else {
                    applicationVO.setOriginName(vo.getOrigin());
                }
                // 添加合作资金方
                applicationVO.setLoanModelType(modelTypeMap.getOrDefault(vo.getPartnerCode(), DEFAULT_MODEL_TYPE));
                applicationVO.setPartnerCodeNew(vo.getPartnerCode());
                applicationVO.setPartnerName(getPartnerName(vo.getPartnerCode()));
                appList.add(applicationVO);
            }
        }
        return appList;
    }

    /**
     * @see LoanApplicationService#getLoanDetails(String)
     */
    @Override
    public LoanDetailsVO getLoanDetails(String applicationId) {
        LoanDetailsVO loanDetailsVO = null;
        UserInfo userInfo = null;
        if (StringUtils.isNotBlank(applicationId)) {
            if (applicationId.contains("CA")) {
                // 如果正常贷款没有,获取贷款额度申请信息
                CreditApplicationDTO creditApplication = creditApplicationServiceFacade
                        .getCreditApplicationByApplicationId(applicationId);
                if (null != creditApplication) {
                    userInfo = userService.getUserInfoByUuid(creditApplication.getUuid());
                    loanDetailsVO = buildLoanDetailsVO(creditApplication);
                }
            } else {
                LoanApplicationDTO loanApplication = loanApplicationServiceFacade
                        .getLoanApplicationByApplicationId(applicationId);
                if (null != loanApplication) {
                    //查询用户信息
                    Integer userId = loanApplication.getBorrowerId();
                    userInfo = userService.getUserInfo(userId);
                    loanDetailsVO = buildLoanDetailsVO(loanApplication);
                }
            }
        }
        if (Objects.nonNull(userInfo) && Objects.nonNull(loanDetailsVO)) {
            loanDetailsVO.setUuid(userInfo.getUuid());
            loanDetailsVO.setUserId(Objects.isNull(userInfo.getUserId()) ? null : userInfo.getUserId().longValue());
            loanDetailsVO.setMobile(StringUtil.hideMobile(userInfo.getMobile()));
            try {
                Response<LoanRateVO> response = loanRateDubboService.findLoanByAppId(applicationId);
                if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                    // 小数形式
                    String rate = response.getResult().getTotalRate();
                    // 转换为百分比
                    rate = Double.valueOf(rate) * 100 + "%";
                    loanDetailsVO.setTotalRate(rate);
                } else {
                    throw new CrmInterviewException(response.getMessage());
                }
            } catch (Exception e) {
                log.warn("getLoanDetails 利率查询失败，applicationId={}", applicationId, e);
            }
        }

        // 查询此笔贷款对应的当前催收人员，便于客服们回答客户的咨询(主要是催收人员的信息核实)
        if (StringUtils.isNotBlank(applicationId) && Objects.nonNull(loanDetailsVO)) {
            try {
                CollectionStaffVO staffInfo = complainService.getCollectionStaffInfo(applicationId);
                if (staffInfo != null) {
                    loanDetailsVO.setCollectionStaffId(staffInfo.getStaffId());
                    loanDetailsVO.setCollectionStaffName(staffInfo.getStaffName());
                }
            } catch (Exception e) {
                log.warn("获取催收员id和催收员姓名发生异常: {}", e.getMessage(), e);
            }
        }
        return loanDetailsVO;
    }

    @Override
    public LoanDetailsVO getExternalOrderNoByApplicationId(String applicationId) {
        LoanDetailsVO detailsVO = new LoanDetailsVO();
        LoanVO loan = findLoanByAppId(applicationId);
        if (Objects.nonNull(loan)) {
            detailsVO.setExternalOrderNo(loan.getExternalOrderNo());
            detailsVO.setChannelOrderNo(queryChannelOrderNo(applicationId));
        }
        return detailsVO;
    }

    @Override
    public List<LoanRecordVO> getOrderRecord(String applicationId) {
        List<LoanRecordVO> list = new ArrayList<>();
        if (StringUtils.isEmpty(applicationId)) {
            return list;
        }
        UserRepaysDTO userRepaysDTO = new UserRepaysDTO();
        userRepaysDTO.setApplicationId(applicationId);
        Response<List<UserRepaysVO>> response = repaymentRecordDubboService.queryUserRepaysByCondition(userRepaysDTO);
        log.info("getLoanRecordList get lender response, applicationId:{}, {}", applicationId, response.toString());
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            List<UserRepaysVO> repaysVOList = response.getResult();
            list = repaysVOList.stream().map(vo -> {
                LoanRecordVO loanRecordVo = new LoanRecordVO();
                //批次号
                loanRecordVo.setTransId(vo.getServiceNo());
                //交易类型
                loanRecordVo.setTransType(vo.getAction());
                //扣款金额
                loanRecordVo.setTransAmount(vo.getDebitAmount());
                loanRecordVo.setBeginTime(vo.getCreatedAt());
                loanRecordVo.setEndTime(vo.getSettledAt());
                loanRecordVo.setStatus(UserRepaysStatusEnum.getCommentByStatus(vo.getStatus()));
                //支付渠道
                loanRecordVo.setPaymentChannel(ChannelCodeEnum.getChannelNameByChannelCode(vo.getDebitChannelCode()));
                //代扣失败分类、原因匹配
                if (StringUtils.isNotEmpty(vo.getRemark())) {
                    loanRecordVo.setRemark(vo.getRemark());
                }
                loanRecordVo.setRepayOrigin(getRepayOriginCh(vo.getRepayOrigin()));
                try {
                    Response<BankCardVO> bankCardVOResponse = bankCardDubboService.queryBankCardById(vo.getBankCardId());
                    if (Objects.nonNull(bankCardVOResponse) && Objects.nonNull(bankCardVOResponse.getResult())) {
                        loanRecordVo.setAccountNo(StringUtil.hideBankCard(bankCardVOResponse.getResult().getAccountNo()));
                        loanRecordVo.setPayBank(bankCardVOResponse.getResult().getBankName());
                    }
                } catch (Exception e) {
                    log.info("queryBankCardById id:{}, error:{}", vo.getBankCardId(), e);
                }
                loanRecordVo.setDebitChannelCode(PayCodeEnum.getNameByChannel(vo.getDebitChannelCode()));
                return loanRecordVo;
            }).collect(Collectors.toList());
        }
        return list;
    }

    private String getRepayOriginCh(String repayOrigin) {

        for (RepayOriginEnum repayOriginEnum : RepayOriginEnum.values()) {
            if (repayOriginEnum.getCode().equals(repayOrigin)) {
                return repayOriginEnum.getComment();
            }
        }
        return repayOrigin;
    }

    @Override
    public List<RepaymentPlanVO> getRepaymentPlan(String applicationId) {
        List<RepaymentPlanVO> result = new ArrayList<>();
        if (Objects.isNull(applicationId)) {
            return result;
        }
        Response<LoanVO> response = loanDubboService.findLoanByAppId(applicationId);
        if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
            return result;
        }
        LoanVO loan = response.getResult();
        //获取还款列表
        List<DueVO> dueList = loan.getDueList();
        if (CollectionUtils.isEmpty(dueList)) {
            return result;
        }
        //排序
        dueList.sort(Comparator.comparing(DueVO::getIndex));
        //按照期数index进行分组
        Map<Integer, List<DueVO>> dueMap = dueList.stream().collect(Collectors.groupingBy(DueVO::getIndex));
        //每期对应的还款列表
        List<DueVO> dueVOList = null;
        String state = loan.getStatus();
        for (Map.Entry entry : dueMap.entrySet()) {
            dueVOList = (List<DueVO>) entry.getValue();
            //还款返回对象
            RepaymentPlanVO repaymentPlanVO = new RepaymentPlanVO();
            //还款计划
            repaymentPlanVO.setIndex(Integer.valueOf(entry.getKey().toString()));
            //到期时间
            Date dueDate = null;
            BigDecimal big = BigDecimal.ZERO;
            BigDecimal due = BigDecimal.ZERO;
            List<RepaymentPlanSettlementVO> settlementVOList = new ArrayList<>();
            for (DueVO dueVO : dueVOList) {
                RepaymentPlanSettlementVO settlementVO = new RepaymentPlanSettlementVO();
                if (RepaymentPlanType.PRINCIPAL.getValue().equals(dueVO.getDueType())) {
                    dueDate = dueVO.getDueDate();
                }
                BigDecimal settledAmount = dueVO.getSettledAmount();
                BigDecimal dueAmount = dueVO.getAmount();
                if (null != settledAmount) {
                    big = big.add(settledAmount);
                }
                if (null != dueAmount) {
                    due = due.add(dueAmount);
                }
                settlementVO.setDueType(RepaymentPlanType.getText(dueVO.getDueType()));
                settlementVO.setIndex(dueVO.getIndex());
                settlementVO.setSettledAmount(settledAmount);
                settlementVO.setDueAmount(dueAmount);
                settlementVO.setSettledTime(dueVO.getSettledDate());
                settlementVO.setState(getRepaymentPlanState(dueVO, state));
                settlementVOList.add(settlementVO);
            }
            repaymentPlanVO.setDetail(settlementVOList);
            repaymentPlanVO.setDueDate(dueDate);
            repaymentPlanVO.setSumStillAmount(big);
            repaymentPlanVO.setSumDueAmount(due);
            result.add(repaymentPlanVO);
        }
        return result;
    }

    /**
     * @see LoanApplicationService#getOutstandingLoanList(Integer)
     */
    @Override
    public List<LoanOutstandingVO> getOutstandingLoanList(Integer userId) {
        if (Objects.isNull(userId)) {
            return new ArrayList<>();
        }
        List<LoanVO> loans = getLoanVOList(userId);
        Map<String, String> modelTypeMap = opDictInfoBO.queryValidDictMap(LOAN_MODEL_TYPE, "");
        return loans.stream().map(item -> buildLoanOutstandingVO(item, modelTypeMap)).collect(Collectors.toList());
    }

    /**
     * @see LoanApplicationService#allowEarlySettle(String)
     */
    @Override
    public void allowEarlySettle(String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            log.error("allowEarlySettle applicationId is null");
            throw new CrmInterviewException("贷款号不能为空");
        }
        Response<Integer> response = repaymentDubboService.updateAllowEarlySettleByApplicationId(applicationId, true);
        if (Objects.isNull(response) || !ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
            log.error("修改支持提前结清失败, applicationId={}, {}", applicationId, JSONObject.toJSONString(response));
            throw new CrmInterviewException("修改支持提前结清失败");
        }
    }

    @Override
    public List<String> allowEarlySettleBatch(List<String> applicationIdList) {
        List<String> failedList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(applicationIdList)) {
            for (String applicationId : applicationIdList) {
                Response<Integer> response = repaymentDubboService
                        .updateAllowEarlySettleByApplicationId(applicationId, true);
                if (Objects.isNull(response) || !ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                    log.error("allowEarlySettleBatch error, applicationId={}, {}", applicationId,
                            JSONObject.toJSONString(response));
                    failedList.add(applicationId);
                }
            }
        }
        return failedList;
    }

    /**
     * @see LoanApplicationService#earlySettle(String, String)
     */
    @Override
    public void earlySettle(String staffMobile, String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            throw new CrmInterviewException("贷款号不能为空");
        }
//        List<Admin> admins = adminService.queryAdminByMobile(Arrays.asList(staffMobile));
//        if (CollectionUtils.isEmpty(admins)) {
//            log.error("earlySettle 该员工没有Admin权限, mobile={}", staffMobile);
//            throw new CrmInterviewException("该员工没有Admin权限");
//        }
//        Integer adminId = admins.get(0).getId();
        LoanVO loan = findLoanByAppId(applicationId);
        if (Objects.isNull(loan) || Objects.isNull(loan.getUserId())) {
            log.error("earlySettle 找不到用户信息，applicationId={}", applicationId);
            throw new CrmInterviewException("找不到用户信息");
        }
        Integer userId = loan.getUserId().intValue();
        RepaymentDTO repaymentDTO = new RepaymentDTO();
        repaymentDTO.setUserId(userId);
        repaymentDTO.setApplicationId(applicationId);
        repaymentDTO.setRepaymentMode("YB");
        repaymentDTO.setServiceNo(RandomUtil.getRandomFileName("KF", 11));
        repaymentDTO.setRepayOrigin(LoanConstant.REPAY_ORIGIN);
        Response<RepaymentVo> response = repaymentDubboService.repayment(repaymentDTO);
        if (Objects.nonNull(response) && ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
            //提前结清成功后保存日志
            createNote(userId, adminId, String.format("提前结清贷款单号:" + applicationId + "的剩余贷款"));
        } else {
            log.warn("earlySettle error, userId={}, applicationId={}, {}", userId, applicationId,
                    JSONObject.toJSONString(response));
            if (Objects.isNull(response)) {
                throw new CrmInterviewException("提前结清失败");
            } else {
                throw new CrmInterviewException("提前结清失败。" + response.getMessage());
            }
        }
    }

    @Override
    public List<String> earlySettleBatch(Integer userId, String staffMobile, List<String> applicationIdList) {
        List<String> failedList = new ArrayList<>();
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(applicationIdList)) {
            return failedList;
        }
//        List<Admin> admins = adminService.queryAdminByMobile(Arrays.asList(staffMobile));
//        if (CollectionUtils.isEmpty(admins)) {
//            log.error("earlySettleBatch 该员工没有Admin权限, mobile={}", staffMobile);
//            throw new CrmInterviewException("该员工没有Admin权限");
//        }
//        Integer adminId = admins.get(0).getId();
        RepaymentDTO repaymentDTO = new RepaymentDTO();
        repaymentDTO.setUserId(userId);
        repaymentDTO.setRepaymentMode("YB");
        repaymentDTO.setServiceNo(RandomUtil.getRandomFileName("KF", 11));
        repaymentDTO.setRepayOrigin(LoanConstant.REPAY_ORIGIN);
        for (String applicationId : applicationIdList) {
            repaymentDTO.setApplicationId(applicationId);
            Response<RepaymentVo> response = repaymentDubboService.repayment(repaymentDTO);
            if (Objects.nonNull(response) && ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                //提前结清成功后保存日志
                createNote(userId, adminId, String.format("提前结清贷款单号:" + applicationId + "的剩余贷款"));
            } else {
                log.error("earlySettleBatch error, userId={}, applicationId={},  {}",
                        userId, applicationId, JSONObject.toJSONString(response));
                failedList.add(applicationId);
            }
        }
        return failedList;
    }

    /**
     * @see LoanApplicationService#getPaymentsDetails(Integer)
     */
    @Override
    public List<PaymentsDetailsVO> getPaymentsDetails(Integer userId) {
        List<PaymentsDetailsVO> result = new ArrayList<>();
        if (Objects.isNull(userId)) {
            return result;
        }
        addAccountTransactions(userId, result);
        addAccountingTransactions(userId, result);
        addRepaymentType(userId, result);
        //根据交易时间降序
        if (CollectionUtils.isNotEmpty(result)) {
            result.sort(Comparator.comparing(PaymentsDetailsVO::getTransTime).reversed());
        }
        return result;
    }

    /**
     * 获得合作资金方名称
     *
     * @param partnerCode
     * @return
     */
    @Override
    public String getPartnerName(String partnerCode) {
        try {
            if (StringUtils.isNotBlank(partnerCode)) {
                Response<List<CapitalBasicVo>> response = capitalBasicProvider.getCapitalBasicByCode(partnerCode, 0L);
                if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResult())) {
                    return response.getResult().get(0).getCompanyName();
                }
            }
            // 如果为空则默认为我来贷资金方
            return "我来贷";
        } catch (Exception e) {
            log.error("查询资金方接口异常, partnerCode:{}", partnerCode, e);
        }
        return "";
    }

    private void addRepaymentType(Integer userId, List<PaymentsDetailsVO> result) {
        UserRepaysDTO userRepaysDTO = new UserRepaysDTO();
        userRepaysDTO.setUserId(userId);
        Response<List<UserRepaysVO>> response = repaymentRecordDubboService.queryUserRepaysByCondition(userRepaysDTO);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            Map<String, String> map = response.getResult().stream()
                    .filter(userRepaysVO -> StringUtils.isNotBlank(userRepaysVO.getRepayOrigin()))
                    .collect(Collectors.toMap(UserRepaysVO::getServiceNo, UserRepaysVO::getRepayOrigin));
            result.forEach(vo -> {
                vo.setRepaymentType(RepaymentTypeEnum.getType(map.get(vo.getServiceNo())));
            });
        }
    }

    /**
     * 获取某笔还款计划的状态
     */
    private String getRepaymentPlanState(DueVO dueVO, String state) {
        // 未还款
        boolean notRepaid = NumberUtil.neqZero(dueVO.getAmount())
                && (Objects.isNull(dueVO.getSettledAmount()) || Objects.isNull(dueVO.getSettledDate()));
        if (notRepaid) {
            return RepaymentPlanState.OPEN.getText();
        }
        // 是否还清
        boolean isSettled = NumberUtil.eqZero(dueVO.getAmount())
                || NumberUtil.eqZero(dueVO.getAmount().subtract(dueVO.getSettledAmount()))
                || RepaymentPlanState.CLOSED.getValue().equals(state)
                || RepaymentPlanState.ERALY_SETTLED.getValue().equals(state)
                || RepaymentPlanState.CANCELLED.getValue().equals(state);
        if (isSettled) {
            return RepaymentPlanState.SETTLED.getText();
        } else if (Math.max(0, (int) DateUtils.daysUntilCurrentDate(dueVO.getDueDate())) > 0) {
            //逾期
            return RepaymentPlanState.OVERDUE.getText();
        }
        return RepaymentPlanState.OPEN.getText();
    }

    private void addAccountTransactions(Integer userId, List<PaymentsDetailsVO> result) {
        AccountTransactionsQueryDTO queryDTO = new AccountTransactionsQueryDTO();
        queryDTO.setPageSize(1000);
        queryDTO.setStartIndex(1);
        queryDTO.setClientType("User");
        queryDTO.setClientCode(userId);
        //查询账户明细接口
        Response<Page<AccountTransactionsVo>> response = financeTransDubboService.queryAccountTransactions(queryDTO);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            List<AccountTransactionsVo> accountTransactionsVos = response.getResult().getList();
            if (CollectionUtils.isNotEmpty(accountTransactionsVos)) {
                PaymentsDetailsVO paymentsDetailsVO = null;
                for (AccountTransactionsVo vo : accountTransactionsVos) {
                    paymentsDetailsVO = new PaymentsDetailsVO();
                    paymentsDetailsVO.setAccountFlag(Boolean.FALSE);
                    paymentsDetailsVO.setServiceNo(vo.getTransactionSeqNo());
                    //交易金额
                    paymentsDetailsVO.setTransAmount(vo.getAmount());
                    //交易时间
                    paymentsDetailsVO.setTransTime(vo.getCreatedAt());
                    // 交易类型
                    if (Objects.nonNull(vo.getAction())) {
                        paymentsDetailsVO.setTransType(ActionTypeEnum.getType(vo.getAction()));
                    }
                    // 支付渠道
                    if (Objects.nonNull(vo.getPayCode())) {
                        paymentsDetailsVO.setChannel(ChannelCodeEnum.getChannelNameByChannelCode(vo.getPayCode()));
                    }
                    //根据loanId查询贷款号
                    if (null != vo.getLoanId()) {
                        Response<LoanVO> loanResponse = loanDubboService.findLoanById(vo.getLoanId().longValue());
                        if (Objects.nonNull(loanResponse) && Objects.nonNull(loanResponse.getResult())) {
                            LoanVO loanVO = loanResponse.getResult();
                            paymentsDetailsVO.setApplicationId(loanVO.getApplicationId());
                        }
                    }
                    result.add(paymentsDetailsVO);
                }
            }
        }
    }

    private void addAccountingTransactions(Integer userId, List<PaymentsDetailsVO> result) {
        com.welab.finance.accounting.dto.AccountTransactionsQueryDTO dto = new com.welab.finance.accounting.dto.AccountTransactionsQueryDTO();
        dto.setClientCode(userId);
        dto.setClientType("User");
        dto.setPageSize(1000);
        dto.setStartIndex(1);
        Response<Page<AccountTransactionVO>> res = accountTransDubboService.queryAccountTransactions(dto);
        if (Objects.nonNull(res) && Objects.nonNull(res.getResult())) {
            List<AccountTransactionVO> list = res.getResult().getList();
            if (CollectionUtils.isNotEmpty(list)) {
                PaymentsDetailsVO paymentsDetailsVO = null;
                for (AccountTransactionVO vo : list) {
                    paymentsDetailsVO = new PaymentsDetailsVO();
                    paymentsDetailsVO.setAccountFlag(Boolean.TRUE);
                    paymentsDetailsVO.setServiceNo(vo.getServiceNo());
                    //交易金额
                    paymentsDetailsVO.setTransAmount(vo.getAmount());
                    //交易时间
                    paymentsDetailsVO.setTransTime(vo.getTransactionAt());
                    // 交易类型
                    if (Objects.nonNull(vo.getAction())) {
                        paymentsDetailsVO.setTransType(ActionTypeEnum.getType(vo.getAction()));
                    }
                    // 支付渠道
                    if (Objects.nonNull(vo.getChannelCode())) {
                        paymentsDetailsVO.setChannel(ChannelCodeEnum.getChannelNameByChannelCode(vo.getChannelCode()));
                    }
                    paymentsDetailsVO.setApplicationId(vo.getApplicationId());
                    result.add(paymentsDetailsVO);
                }
            }
        }
    }

    /**
     * 提前结清成功后保存日志
     *
     * @param userId
     * @param operatorId
     * @param content
     */
    private void createNote(Integer userId, Integer operatorId, String content) {
        NotesDTO note = new NotesDTO();
        // 操作员Id
        note.setAdminId(operatorId);
        note.setUpdatedAt(new Date());
        note.setCreatedAt(new Date());
        note.setSubjectId(userId);
        note.setSubjectType(OwnerTypeEnum.USER.getKey());
        note.setContent(content);
        notesServiceFacade.saveNotes(note);
    }

    /**
     * 根据用户id查询(已放款)贷款列表
     */
    @Override
    public List<LoanVO> getLoanVOList(Integer userId) {
        UserLoanDTO dto = new UserLoanDTO();
        dto.setUserId(userId.longValue());
        dto.setStatusList(Arrays.asList(LoanConstant.DISBURSED));
        Response<List<LoanVO>> response = loanDubboService.findLoanListByUser(dto);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            return response.getResult();
        }
        return new ArrayList<>();
    }

    private LoanOutstandingVO buildLoanOutstandingVO(LoanVO loan, Map<String, String> modelTypeMap) {
        LoanOutstandingVO loanVO = new LoanOutstandingVO();
        BeanUtils.copyProperties(loan, loanVO);
        if (StringUtils.isNotBlank(loan.getChannelCode())) {
            loanVO.setPaymentChannel(ChannelCodeEnum.getChannelNameByChannelCode(loan.getChannelCode()));
        }
        loanVO.setProductName(getProductName(loan.getProductCode()));
        loanVO.setState(getState(loan.getStatus()));
        loanVO.setPartnerCode(loanVO.getPartnerCode());
        loanVO.setPartnerCodeNew(loanVO.getPartnerCode());
        loanVO.setLoanModelType(modelTypeMap.getOrDefault(loanVO.getPartnerCode(), DEFAULT_MODEL_TYPE));
        loanVO.setPartnerName(getPartnerName(loan.getPartnerCode()));
        //全额结清金额
        covertLoanBalance(loanVO);
        //loanVO.setSettleAllAmount(getLoanBalance(loan.getApplicationId()));
        // 获取进件渠道号
        LoanApplicationDTO loanApplicationDTO =
            loanApplicationServiceFacade.getLoanApplicationByApplicationId(loan.getApplicationId());
        if (Objects.nonNull(loanApplicationDTO)) {
            loanVO.setOrigin(loanApplicationDTO.getOrigin());
            //状态从此接口获取
            loanVO.setState(LoanApplicationStateEnum.getStateEnum(loanApplicationDTO.getState()).getText());
        }
        loanVO.setDisbursedTime(loan.getDisbursedAt());
        Date deadDate = null;
        Integer currTenor = 0;
        //获取整笔贷款还款计划
        List<DueVO> totalDues = loan.getDueList();
        if (CollectionUtils.isNotEmpty(totalDues)) {
            //期数排序
            totalDues.sort(Comparator.comparing(DueVO::getDueDate));
            for (DueVO due : totalDues) {
                if (due.getAmount().compareTo(due.getSettledAmount()) != 0) {
                    deadDate = due.getDueDate();
                    //贷款当前期数
                    currTenor = due.getIndex();
                    break;
                }
            }
        }
        //贷款总期数
        Integer totalTenor = getTotalTenor(loan.getTenor());
        //贷款的剩余期数
        Integer leftTenor = totalTenor - currTenor + 1;
        loanVO.setTenor(leftTenor + "/" + totalTenor);
        //获取该笔贷款当前期的due list
        BigDecimal currBalance = BigDecimal.ZERO;
        DueDTO dueDTO = new DueDTO();
        dueDTO.setApplicationId(loan.getApplicationId());
        dueDTO.setDueIndex(currTenor);
        try {
            Response<List<DueVO>> response = dueDubboService.queryDues(dueDTO);
            if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResult())) {
                List<DueVO> dues = response.getResult();
                for (DueVO due : dues) {
                    currBalance = currBalance.add(due.getAmount());
                }
                //到期日期
                if (deadDate == null) {
                    DueVO lastDue = dues.get(dues.size() - 1);
                    deadDate = lastDue.getDueDate();
                }
            }
        } catch (Exception e) {
            log.warn("buildLoanOutstandingVO 查询当前还款计划异常, dueDTO:{}", dueDTO.toString(), e);
        }
        //当期应还
        loanVO.setCurrBalance(currBalance);
        //到期日期
        if (Objects.nonNull(deadDate)) {
            loanVO.setDeadDate(DateUtil.dateToDate(deadDate, TimeFormatter.YYYY_MM_DD));
        }
        try {
            Response<Boolean> isAllow = repaymentDubboService.checkAllowEarlySettle(loan.getApplicationId());
            if (Boolean.TRUE.equals(isAllow.getResult())) {
                loanVO.setIsAllowEarlySettle("是");
            } else {
                loanVO.setIsAllowEarlySettle("否");
            }
        } catch (Exception e) {
            log.warn("buildLoanOutstandingVO 确认是否允许提前结清异常, applicationId:{}", loan.getApplicationId(), e);
        }

        //返回还款时间
        Response<List<CapitalVo>> capitalResponse = capitalRuleProviderImpl.getCapitalRuleByCode(loan.getPartnerCode(), 0L);
        if (Objects.nonNull(capitalResponse) && CollectionUtils.isNotEmpty(capitalResponse.getResult())) {
            List<CapitalVo> capitalList = capitalResponse.getResult();
            if (capitalList != null && capitalList.size() > 0) {
                loanVO.setRepayTime(capitalList.get(0).getRepaymentStartTime() + "-" + capitalList.get(0).getRepaymentEndTime());
            }
        }
        return loanVO;
    }

    @Override
    public String getState(String status) {
        for (com.welab.finance.loanprocedure.enums.LoanStateEnum stateEnum : com.welab.finance.loanprocedure.enums.LoanStateEnum
                .values()) {
            if (stateEnum.getKey().equals(status)) {
                return stateEnum.getValue();
            }
        }

        return "";
    }

    @Override
    public String getProductName(String productCode) {

        ProductDTO product = productServiceFacade.getProductByCode(productCode);
        if (Objects.isNull(product)) {
            return "";
        }

        return product.getName();

    }

    /**
     * 获取贷款的总共期数
     */
    private Integer getTotalTenor(String tenor) {
        if (tenor.contains("D")){
            return 1;
        }
        return Integer.parseInt(tenor.split("M")[0]);
    }

    private LoanDetailsVO buildLoanDetailsVO(CreditApplicationDTO creditApplication) {
        LoanDetailsVO loanDetailsVO = new LoanDetailsVO();
        BeanUtils.copyProperties(creditApplication, loanDetailsVO);
        loanDetailsVO.setApplyTime(creditApplication.getAppliedAt());
        loanDetailsVO.setApprovalTime(creditApplication.getApprovedAt());
        loanDetailsVO.setApprovalAmount(creditApplication.getAmount());
        if (StringUtils.isNotBlank(creditApplication.getState())) {
            loanDetailsVO.setState(CreditStateEnum.getText(loanDetailsVO.getState()));
        }
        try {
            //查询产品信息
            ProductDTO productDTO = productServiceFacade.getProductByCode(creditApplication.getProductCode());
            if (null != productDTO) {
                loanDetailsVO.setProductCode(productDTO.getCode());
                loanDetailsVO.setProductName(productDTO.getName());
            }

            EcOrderDTO ecOrderDTO = loanApplicationServiceFacade.getOrderByApplicationIdAndSource(creditApplication.getApplicationId(), null);
            if (null != ecOrderDTO) {
                loanDetailsVO.setOrderNo(ecOrderDTO.getOrderNo());
            }
        } catch (Exception e) {
            log.error("buildLoanDetailsVO CreditApplicationDTO getProductByCode error. productCod={}",
                    creditApplication.getProductCode(), e);
        }
        return loanDetailsVO;
    }

    private LoanDetailsVO buildLoanDetailsVO(LoanApplicationDTO loanApplication) {
        LoanDetailsVO loanDetailsVO = new LoanDetailsVO();
        String applicationId = loanApplication.getApplicationId();
        loanDetailsVO.setApplicationId(applicationId);
        loanDetailsVO.setOrigin(loanApplication.getOrigin());
        loanDetailsVO.setApplyTime(loanApplication.getAppliedAt());
        loanDetailsVO.setApprovalTime(loanApplication.getApprovedAt());
        loanDetailsVO.setApprovalAmount(loanApplication.getAmount());
        loanDetailsVO.setApplyTenor(loanApplication.getAppliedTenor());
        loanDetailsVO.setPartnerName(getPartnerName(loanApplication.getPartnerCode()));
        loanDetailsVO.setTenor(loanApplication.getTenor());
        loanDetailsVO.setConfirmedAt(loanApplication.getConfirmedAt());
        loanDetailsVO.setAmount(loanApplication.getAmount());
        loanDetailsVO.setApplyAmount(loanApplication.getAppliedAmount());
        if (StringUtils.isNotBlank(loanApplication.getState())) {
            loanDetailsVO.setState(LoanApplicationStateEnum.getStateEnum(loanApplication.getState()).getText());
        }
        try {
            // 调用lender项目接口查询loan贷款信息
            convertLoanVO(loanDetailsVO, applicationId);
            // 查询订单信息
            EcOrderDTO ecOrderDTO = loanApplicationServiceFacade
                    .getOrderByApplicationIdAndSource(applicationId, null);
            if (null != ecOrderDTO) {
                loanDetailsVO.setOrderNo(ecOrderDTO.getOrderNo());
            } else {
                EcLoanOrdersDTO ecLoanOrdersDTO = loanApplicationServiceFacade
                        .getPartnerLoanApplicationsDTOByApplicationId(applicationId);
                if (Objects.nonNull(ecLoanOrdersDTO)) {
                    loanDetailsVO.setOrderNo(ecLoanOrdersDTO.getLoanOrderId());
                }
            }
            //查询产品信息
            ProductDTO productDTO = productServiceFacade.getProductById(loanApplication.getWelabProductId());
            if (null != productDTO) {
                loanDetailsVO.setProductCode(productDTO.getCode());
                loanDetailsVO.setProductName(productDTO.getName());
            }
            // 用户到账银行卡信息
            Response<BankCardVO> bankcard = bankCardDubboService.getDisburesBankCardByApplicationId(applicationId);
            if (null != bankcard && Objects.nonNull(bankcard.getResult())) {
                loanDetailsVO.setBankName(bankcard.getResult().getBankName());
                if (StringUtils.isNotBlank(bankcard.getResult().getAccountNo())) {
                    loanDetailsVO.setAccountNo(StringUtil.hideBankCard(bankcard.getResult().getAccountNo()));
                }
            }
            
            // 查询渠道订单号
            String channelOrderNo = queryChannelOrderNo(applicationId);
            loanDetailsVO.setChannelOrderNo(channelOrderNo);
            //贷款合同单独分离接口
            /*String url = getApplicationAgreementUrl(applicationId);
            loanDetailsVO.setLoanAgreementUrl(url);*/
        } catch (Exception e) {
            log.error("buildLoanDetailsVO LoanApplicationDTO error. loanApplication={}", loanApplication.toString(), e);
        }
        return loanDetailsVO;
    }

    private String queryChannelOrderNo(String applicationId) {
        try {
            String res = HttpClientUtil.get(lenderPreUrl + "/withdrawal-service/v1/loan/getLoanApplicationByApplicationId" + "?applicationId=" + applicationId);
            log.info("queryChannelOrderNo res:{}", res);
            Response response = JSONObject.parseObject(res, Response.class);
            if (Response.isSuccess(response)) {
                JSONObject result = (JSONObject) response.getResult();
                return result.getString("externalOrderNo");
            } else {
                log.warn("queryChannelOrderNo fail: {}", res);
            }
        } catch (Exception e) {
            log.error("queryChannelOrderNo error: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to query channel order no", e);
        }
        return "";

    }

    /**
     * 根据贷款号查询贷款详情
     */
    private void convertLoanVO(LoanDetailsVO loanDetailsVO, String applicationId) {
        // 调用lender项目接口查询loan贷款信息
        LoanVO loan = findLoanByAppId(applicationId);
        if (Objects.isNull(loan)) {
            return;
        }
        loanDetailsVO.setRepurchaseStatus(RepurchaseStatusEnum.getDescByCode(loan.getRepurchaseStatus()));
        // 查询用户手机号
        UserInfo userInfo = userService.getUserInfo(loan.getUserId().intValue());
        loanDetailsVO.setMobile(userInfo != null ? userInfo.getMobile() : null);
        // 放款时间
        loanDetailsVO.setDisbursedTime(loan.getDisbursedAt());
        loanDetailsVO.setExternalOrderNo(loan.getExternalOrderNo());
        if (CollectionUtils.isNotEmpty(loan.getLenderList())) {
            loanDetailsVO.setLenderId(loan.getLenderList().get(0).getLenderId());
        }
    }

    public LoanVO findLoanByAppId(String applicationId) {
        try {
            Response<LoanVO> loan = loanDubboService.findLoanByAppId(applicationId);
            if (Objects.nonNull(loan) && Objects.nonNull(loan.getResult())) {
                return loan.getResult();
            }
        } catch (Exception e) {
            log.error("findLoanByAppId error, applicationId:{}", applicationId, e);
        }
        return null;
    }

    private void covertLoanBalance(LoanOutstandingVO loan){
        RepaymentCalculateDTO dto = new RepaymentCalculateDTO();
        dto.setRepaymentMode("YB");
        dto.setApplicationId(loan.getApplicationId());
        dto.setRepaymentDate(new Date());
        dto.setOrgId("0");
        try {
            Response<RepaymentFeeItemVo> itemVo = calculationDubboService.getRepaymentFeeItems(dto);
            if (Objects.nonNull(itemVo) && Objects.nonNull(itemVo.getResult())) {
                Map<String, List<com.welab.finance.repayment.vo.DueVO>> group = itemVo.getResult().getDues().stream().collect(Collectors.groupingBy(com.welab.finance.repayment.vo.DueVO::getDueType));
                StringBuilder component = new StringBuilder();
                //控制最后一个拼接
                int size = 0;
                for (Map.Entry<String, List<com.welab.finance.repayment.vo.DueVO>> entry : group.entrySet()) {
                    size++;
                    double dueTypeSumAmount = entry.getValue().stream().map(com.welab.finance.repayment.vo.DueVO::getOutstandingAmount).mapToDouble(BigDecimal::doubleValue).sum();
                    component.append(DueTypeEnum.getDueName(entry.getKey())).append(dueTypeSumAmount).append("元");
                    if (size < group.size()) {
                        component.append(", ");
                    }
                }
                loan.setSettleAllAmount(itemVo.getResult().getAmount());
                loan.setSettleAllAmountDetail(component.toString());
            }
        } catch (Exception e) {
            log.warn("查询全额结清异常, dto:{}", dto, e);
        }
    }

    /**
     * 获得贷款余额
     *
     * @return
     */
    @Override
    public BigDecimal getLoanBalance(String applicationId) {
        RepaymentCalculateDTO dto = new RepaymentCalculateDTO();
        dto.setRepaymentMode("YB");
        dto.setApplicationId(applicationId);
        dto.setRepaymentDate(new Date());
        dto.setOrgId("0");
        try {
            Response<RepaymentFeeItemVo> itemVo = calculationDubboService.getRepaymentFeeItems(dto);
            if (Objects.nonNull(itemVo) && Objects.nonNull(itemVo.getResult())) {
                return itemVo.getResult().getAmount();
            }
        } catch (Exception e) {
            log.warn("查询全额结清异常, dto:{}", dto, e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据uuid查询普通进件和额度进件表的贷款数据
     *
     * @param uuid
     * @return
     */
    private List<LoanInfoVO> getLoanAndCreditByUuid(Long uuid) {
        List<LoanInfoVO> loanInfoVOList = new ArrayList<>();
        try {
            User userInfo = userService.getUserByUuid(uuid);
            if (Objects.isNull(userInfo)) {
                return loanInfoVOList;
            }
            // 添加进件产品信息
            addLoanInfo(userInfo, loanInfoVOList);
            // 给进件贷款数据添加关联的会员订单号
            addVipOrderNo(userInfo, loanInfoVOList);
            // 添加额度产品信息
            addCreditInfo(userInfo, loanInfoVOList);
        } catch (Exception e) {
            log.warn("getLoanAndCreditByUuid error. uuid={}", uuid, e);
            throw new CrmInterviewException("消金接口查询异常");
        }
        // 按申请时间降序排序
        if (CollectionUtils.isNotEmpty(loanInfoVOList)) {
            loanInfoVOList.sort(Comparator.comparing(LoanInfoVO::getApplyTime).reversed());
        }
        return loanInfoVOList;
    }

    /**
     * 添加会员订单号
     *
     * @param userInfo
     * @param loanInfoVOList
     */
    private void addVipOrderNo(User userInfo, List<LoanInfoVO> loanInfoVOList) {
        for (LoanInfoVO loanInfoVO : loanInfoVOList) {
	        try {
		        VipPrivilegeCardDTO dto =
		                memberServiceFacade.getPrivilegeCardByApplicationId(userInfo.getId(), loanInfoVO.getApplicationId());
		        if (Objects.nonNull(dto)) {
		            loanInfoVO.setVipOrderNo(dto.getOrderNo());
		        }
	        } catch (Exception e) {
		        log.warn("addVipOrderNo error, applicationId:{}", loanInfoVO.getApplicationId(), e);
	        }
        }

        // 查询渠道方会员订单
        log.info("查询渠道方会员权益,uuid:{}", userInfo.getUuid());
        List<AssetBenefitOrderResponseDTO> assetBenefitOrderByUUid = assetBenefitServiceFacade.getAssetBenefitOrderByUUid(userInfo.getUuid());
        log.info("查询渠道方会员权益, result: {}", JSON.toJSONString(assetBenefitOrderByUUid));

        if (CollectionUtils.isEmpty(assetBenefitOrderByUUid)){
            return;
        }
        assetBenefitOrderByUUid.forEach(item -> {
            loanInfoVOList.stream().filter(loanInfoVO -> loanInfoVO.getApplicationId().equals(item.getApplicationId())).findFirst()
                    .ifPresent(loanInfoVO -> {
                        loanInfoVO.setVipOrderNo(item.getSupplierOrderNo());
                        loanInfoVO.setVipAssetCode(item.getSource());
                    });
        });

    }

    /**
     * 获得进件产品信息
     */
    private void addLoanInfo(User userInfo, List<LoanInfoVO> loanInfoVOList) {
        // 根据用户UUID获取相应的贷款申请信息列表
        List<LoanApplicationDTO> loanApplications = loanApplicationServiceFacade
                .getLoanApplicationsByUserUuid(userInfo.getUuid());
        if (CollectionUtils.isNotEmpty(loanApplications)) {
            for (LoanApplicationDTO dto : loanApplications) {
                LoanInfoVO loanInfoVO = initLoanInfoVO(dto, userInfo);
                buildLoanInfoVO(loanInfoVO, userInfo, dto);
                setLoanApprover(loanInfoVO, dto);
                loanInfoVOList.add(loanInfoVO);
            }
        }
    }

    private LoanInfoVO initLoanInfoVO(LoanApplicationDTO dto, User userInfo) {
        LoanInfoVO loanInfoVO = new LoanInfoVO();
        loanInfoVO.setMobile(userInfo.getMobile());
        loanInfoVO.setUuid(userInfo.getUuid());
        loanInfoVO.setBlocked(userInfo.getBlocked());
        loanInfoVO.setApprovalAmount(dto.getAmount());
        loanInfoVO.setApprovalTenor(dto.getTenor());
        loanInfoVO.setOrigin(dto.getOrigin());
        loanInfoVO.setBorrowerId(dto.getBorrowerId());
        loanInfoVO.setApplicationId(dto.getApplicationId());
        loanInfoVO.setApplyAmount(dto.getAppliedAmount());
        loanInfoVO.setApplyTime(dto.getAppliedAt());
        loanInfoVO.setApplyTenor(dto.getAppliedTenor());
        loanInfoVO.setStatus(dto.getState());
        loanInfoVO.setUrgent(dto.getUrgent());
        loanInfoVO.setAmount(dto.getAmount());
        loanInfoVO.setApprovedAt(dto.getApprovedAt());
        loanInfoVO.setPartnerCode(dto.getPartnerCode());
        loanInfoVO.setTotalRate(dto.getTotalRate());
        loanInfoVO.setConfirmedTime(dto.getConfirmedAt());
        return loanInfoVO;
    }

    /**
     * @param loanInfoVO
     * @param userInfo
     * @param dto
     */
    private void buildLoanInfoVO(LoanInfoVO loanInfoVO, User userInfo, LoanApplicationDTO dto) {
        try {
            // 申请人名称
            Profile profile = profileService.getProfileByUserId(userInfo.getId());
            if (Objects.nonNull(profile)) {
                loanInfoVO.setName(profile.getName());
            }
        } catch (Exception e) {
            log.error("getProfileByUserId error. userId:{}", userInfo.getId(), e);
        }
        try {
            // 公司名称
            Company company = companyService.getCompanyByUuid(userInfo.getUuid());
            if (Objects.nonNull(company)) {
                loanInfoVO.setCompany(company.getName());
            }
        } catch (Exception e) {
            log.error("getCompanyByUuid error. uuid:{}", userInfo.getUuid(), e);
        }
        try {
            // 根据贷款号查询设备信息
            DeviceInfoDTO deviceInfo = deviceInfoServiceFacade.selectByApplicationId(dto.getApplicationId());
            if (Objects.nonNull(deviceInfo) && Objects.nonNull(deviceInfo.getSourceId())) {
                // 过滤SourceId为null的情况
                loanInfoVO.setApplyPlatform(AppUtil.getPlatform(deviceInfo.getSourceId()));
            }
        } catch (Exception e) {
            log.error("getDeviceInfoDTO error. applicationId:{}", dto.getApplicationId(), e);
        }
        try {
            // 获得产品信息
            ProductDTO product = productServiceFacade.getProductById(dto.getWelabProductId());
            if (Objects.nonNull(product)) {
                loanInfoVO.setProductCode(product.getCode());
                loanInfoVO.setProductName(product.getName());
            }
        } catch (Exception e) {
            log.error("getProductById error. productId:{}", dto.getWelabProductId(), e);
        }
        // 获得放款时间
        LoanVO loanVO = findLoanByAppId(dto.getApplicationId());
        if (Objects.nonNull(loanVO)) {
            loanInfoVO.setDisbursedTime(loanVO.getDisbursedAt());
            loanInfoVO.setConfirmedTime(loanVO.getConfirmedAt());
            loanInfoVO.setExternalOrderNo(loanVO.getExternalOrderNo());
        }
    }

    /**
     * 为LoanInfoVO设置审批员
     *
     * @param loanInfoVO
     * @param dto
     */
    private void setLoanApprover(LoanInfoVO loanInfoVO, LoanApplicationDTO dto) {
        LoanStateEnum loanStateEnum = LoanStateEnum.getByValue(dto.getState());
        if (Objects.isNull(loanStateEnum)) {
            setLoanApprover(dto.getAipById(), loanInfoVO);
        } else {
            setLoanApprover(dto.getPrePickedUpById(), loanInfoVO);
        }
    }

    private void setLoanApprover(Integer adminId, LoanInfoVO loanInfoVO) {
        if (Objects.nonNull(adminId)) {
            Admin admin = adminService.getAdminById(adminId);
            if (Objects.nonNull(admin)) {
                loanInfoVO.setApprover(getAdminNameByEmail(admin.getEmail()));
            }
        }
    }

    /**
     * 邮箱转换成管理员姓名
     *
     * @param email
     * @return
     */
    private String getAdminNameByEmail(String email) {
        if (Objects.isNull(email)) {
            return "";
        }
        return email.substring(0, email.indexOf("@"));
    }

    /**
     * 获得额度产品信息
     */
    private void addCreditInfo(User userInfo, List<LoanInfoVO> loanInfoVOList) {
        List<CreditApplicationDTO> creditApplications = creditApplicationServiceFacade
                .getCreditApplicationsByUuid(userInfo.getUuid());
        if (CollectionUtils.isNotEmpty(creditApplications)) {
            for (CreditApplicationDTO dto : creditApplications) {
                LoanInfoVO loanInfoVO = initLoanInfoVO(dto, userInfo);
                buildLoanInfoVO(loanInfoVO, userInfo, dto);
                loanInfoVOList.add(loanInfoVO);
            }
        }
    }

    private LoanInfoVO initLoanInfoVO(CreditApplicationDTO dto, User userInfo) {
        LoanInfoVO loanInfoVO = new LoanInfoVO();
        loanInfoVO.setMobile(userInfo.getMobile());
        loanInfoVO.setUuid(userInfo.getUuid());
        loanInfoVO.setBlocked(userInfo.getBlocked());
        loanInfoVO.setApplicationId(dto.getApplicationId());
        loanInfoVO.setApprovalAmount(dto.getAmount());
        loanInfoVO.setAmount(dto.getAmount());
        loanInfoVO.setStatus(dto.getState());
        loanInfoVO.setApprovedAt(dto.getApprovedAt());
        loanInfoVO.setApplyTime(dto.getAppliedAt());
        loanInfoVO.setUrgent(dto.getUrgent());
        loanInfoVO.setOrigin(dto.getOrigin());
        return loanInfoVO;
    }

    /**
     * @param loanInfoVO
     * @param userInfo
     * @param dto
     */
    private void buildLoanInfoVO(LoanInfoVO loanInfoVO, User userInfo, CreditApplicationDTO dto) {
        try {
            // 申请人名称
            Profile profile = profileService.getProfileByUserId(userInfo.getId());
            if (Objects.nonNull(profile)) {
                loanInfoVO.setName(profile.getName());
            }
        } catch (Exception e) {
            log.error("getProfileByUserId error. userId:{}", userInfo.getId(), e);
        }
        try {
            // 公司名称
            Company company = companyService.getCompanyByUuid(userInfo.getUuid());
            if (Objects.nonNull(company)) {
                loanInfoVO.setCompany(company.getName());
            }
        } catch (Exception e) {
            log.error("getCompanyByUuid error. uuid:{}", userInfo.getUuid(), e);
        }
        // 根据贷款号调用dubbo接口查询设备信息
        try {
            DeviceInfoDTO deviceInfo = deviceInfoServiceFacade
                    .selectByApplicationId(dto.getApplicationId());
            if (deviceInfo != null && null != deviceInfo.getSourceId()) {
                //过滤SourceId为null的情况
                loanInfoVO.setApplyPlatform(AppUtil.getPlatform(deviceInfo.getSourceId()));
            }
        } catch (Exception e) {
            log.error("查询设备接口异常,applicationId:{}", dto.getApplicationId(), e);
        }
        // 产品信息
        try {
            ProductDTO product = productServiceFacade.getProductByCode(dto.getProductCode());
            if (Objects.nonNull(product)) {
                loanInfoVO.setProductCode(product.getCode());
                loanInfoVO.setProductName(product.getName());
            }
        } catch (Exception e) {
            log.error("获得产品信息异常,productCode:{}", dto.getProductCode(), e);
        }
    }


    @Override
    public List<LoanApplicationDTO> getApplicationListByApplications(List<String> applications) {
        return loanApplicationServiceFacade.getLoanApplicationByApplicationIds(applications);
    }

    @Override
    public Response<byte[]> getApplicationAgreementUrl(String appNo) {
        //CA属于申请号,无合同pdf
        if (StringUtils.isNotBlank(appNo) && !appNo.contains("CA")) {
            LoanApplicationDTO loanApplication = loanApplicationServiceFacade.getLoanApplicationByApplicationId(appNo);
            if (null != loanApplication) {
                //查询用户信息
                Integer userId = loanApplication.getBorrowerId();
                //获取name
                Profile ucProfile = profileService.getProfileByUserId(userId);
                //获取mobile
                UserInfo userInfo = userService.getUserInfo(userId);
                if (null != userInfo) {
                    //获取贷款合同url
                    String agreementUrl = loanAgreementService.downloadAgreement(appNo);
                    log.info("getApplicationAgreementUrl appNo:{}, agreementUrl:{}", appNo, agreementUrl);
                    if (StringUtils.isNotBlank(agreementUrl)) {
                        agreementUrl = switchDomainName(agreementUrl);
                        //拼接水印内容
                        String userName = ucProfile.getName();
                        String mobile = userInfo.getMobile().substring(userInfo.getMobile().length() - 4, userInfo.getMobile().length());
                        String timeFormat = DateUtil.dateToString(new Date(), TimeFormatter.YYYY_MM_DD);
                        return uploadService.getWatermarkFileByAppNo(agreementUrl, "借款合同.pdf", userName + mobile + timeFormat);
                    }
                }
            }
        }
        return null;
    }


    @Override
    public LoanImportLabelVO getImportLabelInfo(String applicationId) {
        // 查询案件是否委外
        LoanImportLabelVO importLabelInfo = importInfoMapper.getImportLabelInfo(applicationId);
        // 如果不是委外则再查是否债转
        if (Objects.isNull(importLabelInfo)){
            WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(applicationId);
            if (Objects.nonNull(writeoffDTO)){
                LoanImportLabelVO vo = new LoanImportLabelVO();
                vo.setType("1");
                vo.setCompanyName(writeoffDTO.getCompanyName());
                vo.setDeptDate(writeoffDTO.getTransactionDate());

                // 查询字典获取公司号码
                if (StringUtils.isNotBlank(writeoffDTO.getCompanyName())) {
                    List<OpDictInfo> dicts =
                        opDictInfoMapper.getAllByCategoryAndType(LOAN_TRANSFER_DEPT_DICT, writeoffDTO.getCompanyName())
                            .stream().filter(OpDictInfo::getStatus).collect(Collectors.toList());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dicts) && Objects.nonNull(dicts.get(0))) {
                        vo.setCompanyTel(dicts.get(0).getContent());
                    }
                }
                return vo;

            }
        }

        return importLabelInfo;
    }

    /**
     * 将外部域名改为内部域名
     * @param agreementUrl 文件下载链接
     */
    private String switchDomainName(String agreementUrl) {
        
        // fat 环境不管
        if (!agreementUrl.contains("-fat")){
            return agreementUrl.replace("https://bkv2.wld.net", "http://japi-openresty.slb.cluster.coltd:8000");
        }
        
        return agreementUrl;
    }
}
