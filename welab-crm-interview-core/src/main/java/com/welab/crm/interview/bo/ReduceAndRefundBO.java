package com.welab.crm.interview.bo;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.dto.reduce.ReduceAndRefundCallbackDTO;
import com.welab.collection.interview.service.ILenderService;
import com.welab.collection.interview.utils.DateUtils;
import com.welab.common.response.Response;
import com.welab.crm.interview.domain.InAuthCrmStaff;
import com.welab.crm.interview.domain.OpReduceApplyRecord;
import com.welab.crm.interview.domain.OpReduceRefundAttachment;
import com.welab.crm.interview.domain.OpRefundApplyRecord;
import com.welab.crm.interview.enums.ReduceAndRefundApprovalStatusEnum;
import com.welab.crm.interview.enums.ReduceAndRefundResultEnum;
import com.welab.crm.interview.mapper.OpReduceApplyRecordMapper;
import com.welab.crm.interview.mapper.OpReduceRefundAttachmentMapper;
import com.welab.crm.interview.mapper.OpRefundApplyRecordMapper;
import com.welab.frs.account.dto.RefundSaveDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ReduceAndRefundBO {

	@Resource
	private OpReduceApplyRecordMapper opReduceApplyRecordMapper;


	@Resource
	private OpRefundApplyRecordMapper opRefundApplyRecordMapper;
	
	@Resource
	private OpReduceRefundAttachmentMapper opReduceRefundAttachmentMapper;
	
	@Resource
	private ILenderService lenderService;
	
	@Resource
	private StaffBO staffBO;


	@Value("${oss.prefix}")
	private String aliyunPath;

	/**
	 * 分隔符
	 */
	private static final String SERVICE_NO_SEPARATOR = "_";


	public void pushRefundDataToLender() {
		List<OpRefundApplyRecord> refundList = queryUnPushedAndApprovedAndTimeUpList();
		if (CollectionUtils.isEmpty(refundList)){
			log.info("今日无需要发送的退款记录");
			return;
		}

		Map<Long, InAuthCrmStaff> staffMap = staffBO.getStaffIdMap();

		List<String> requestNoList = refundList.stream().map(OpRefundApplyRecord::getRequestNo).collect(Collectors.toList());
        List<OpReduceRefundAttachment> fileList = opReduceRefundAttachmentMapper.selectList(Wrappers
            .lambdaQuery(OpReduceRefundAttachment.class).in(OpReduceRefundAttachment::getRequestNo, requestNoList));
        Map<String, List<OpReduceRefundAttachment>> fileMap =
            fileList.stream().collect(Collectors.groupingBy(OpReduceRefundAttachment::getRequestNo));

		for (OpRefundApplyRecord refundRecord : refundList) {
			int index = findIndex(refundRecord.getRequestNo());
			RefundSaveDTO refundSaveDTO = convertRefundRecordToRefundSaveDTO(refundRecord, staffMap, index);
			List<OpReduceRefundAttachment> refundFileList = fileMap.get(refundRecord.getRequestNo());
			if (CollectionUtils.isNotEmpty(refundFileList)){
                refundSaveDTO.setImgPath(refundFileList.stream()
                    .map(item -> aliyunPath + item.getUniqueFileName()).collect(Collectors.joining(",")));
			}
			
			log.info("客服推送退款记录,requestNo:{},参数:{}", refundSaveDTO.getServiceNo(), JSON.toJSONString(refundSaveDTO));
			Response response = lenderService.pushRefundDataToLender(refundSaveDTO);
			log.info("客服推送退款记录,requestNo:{},返回结果:{}", refundSaveDTO.getServiceNo(), JSON.toJSONString(response));
			if (Response.isSuccess(response)){
				updateRefundPushFlag(refundRecord);
			} else {
				refundRecord.setResult(ReduceAndRefundResultEnum.PUSH_FAILED.getStatus());
				refundRecord.setFailReason(response.getMessage());
				updateRefundPushFlag(refundRecord);
				log.warn("客服推送退款记录失败,requestNo:{}", refundSaveDTO.getServiceNo());
				updateTaskStatus(refundRecord.getRequestNo());
			}
		}
	}

	/**
	 * 查询该流水号号有多少条已推送的记录，在他基础上+1就是该条记录的索引
	 * @param requestNo
	 * @return
	 */
	private int findIndex(String requestNo) {
		Integer count = opReduceApplyRecordMapper.selectAllPushedCount(requestNo);
		return count + 1;
	}

	public void saveReduceAndRefundCallback(ReduceAndRefundCallbackDTO callbackDTO){
		handlerServiceNo(callbackDTO);
		if ("reduction".equals(callbackDTO.getBizType())){
			saveReduceCallback(callbackDTO);
		} else if ("refund".equals(callbackDTO.getBizType())){
			saveRefundCallBack(callbackDTO);
		}
	}

	private void handlerServiceNo(ReduceAndRefundCallbackDTO callbackDTO) {
		callbackDTO.setServiceNo(callbackDTO.getServiceNo().split(SERVICE_NO_SEPARATOR)[0]);
	}

	private void saveRefundCallBack(ReduceAndRefundCallbackDTO callbackDTO) {
		OpRefundApplyRecord refundApplyRecord = new OpRefundApplyRecord();
		refundApplyRecord.setResult(callbackDTO.getResult());
		refundApplyRecord.setGmtModify(new Date());
		refundApplyRecord.setFailReason(callbackDTO.getFailReason());
		opRefundApplyRecordMapper.update(refundApplyRecord, Wrappers.lambdaUpdate(OpRefundApplyRecord.class)
				.eq(OpRefundApplyRecord::getRequestNo, callbackDTO.getServiceNo())
				.eq(OpRefundApplyRecord::getApplicationId, callbackDTO.getAppNo()));

		// 查询任务是否完成
		updateTaskStatus(callbackDTO.getServiceNo());
	}

	private void saveReduceCallback(ReduceAndRefundCallbackDTO callbackDTO) {
		OpReduceApplyRecord reduceApplyRecord = new OpReduceApplyRecord();
		reduceApplyRecord.setResult(callbackDTO.getResult());
		reduceApplyRecord.setGmtModify(new Date());
		reduceApplyRecord.setFailReason(callbackDTO.getFailReason());
		opReduceApplyRecordMapper.update(reduceApplyRecord, Wrappers.lambdaUpdate(OpReduceApplyRecord.class)
				.eq(OpReduceApplyRecord::getRequestNo, callbackDTO.getServiceNo())
				.eq(OpReduceApplyRecord::getApplicationId, callbackDTO.getAppNo()));
		
		// 查询任务是否完成
		updateTaskStatus(callbackDTO.getServiceNo());
	}

	/**
	 * 更新任务状态,相同请求号下面的全表减免、退款都完成了，任务状态才是完成
	 * @param requestNo 请求号
	 */
	private void updateTaskStatus(String requestNo) {
		List<OpReduceApplyRecord> reduceList = opReduceApplyRecordMapper.selectList(Wrappers.lambdaQuery(OpReduceApplyRecord.class).eq(OpReduceApplyRecord::getRequestNo, requestNo));
		String taskStatus = "1";

		for (OpReduceApplyRecord reduceApplyRecord : reduceList) {
			if (StringUtils.isBlank(reduceApplyRecord.getResult())){
				taskStatus = "0";
				break;
			}
		}
		
		if ("1".equals(taskStatus)){
			List<OpRefundApplyRecord> refundList = opRefundApplyRecordMapper.selectList(Wrappers.lambdaQuery(OpRefundApplyRecord.class).eq(OpRefundApplyRecord::getRequestNo, requestNo));
			for (OpRefundApplyRecord refundApplyRecord : refundList) {
				if (StringUtils.isBlank(refundApplyRecord.getResult())){
					taskStatus = "0";
					break;
				}
			}
		}

		OpReduceApplyRecord updateRecord = new OpReduceApplyRecord();
		updateRecord.setTaskStatus(taskStatus);
		opReduceApplyRecordMapper.update(updateRecord, Wrappers.lambdaUpdate(OpReduceApplyRecord.class).in(OpReduceApplyRecord::getRequestNo, requestNo));

		OpRefundApplyRecord updateRecord2 = new OpRefundApplyRecord();
		updateRecord2.setTaskStatus(taskStatus);
		opRefundApplyRecordMapper.update(updateRecord2, Wrappers.lambdaUpdate(OpRefundApplyRecord.class).in(OpRefundApplyRecord::getRequestNo, requestNo));

	}

	private void updateRefundPushFlag(OpRefundApplyRecord refundRecord) {
		refundRecord.setPushFlag("Y");
		refundRecord.setGmtModify(new Date());
		opRefundApplyRecordMapper.updateById(refundRecord);
	}

	private RefundSaveDTO convertRefundRecordToRefundSaveDTO(OpRefundApplyRecord refundRecord, Map<Long, InAuthCrmStaff> staffMap, int index) {
		RefundSaveDTO refundSaveDTO = new RefundSaveDTO();
		refundSaveDTO.setApplicationId(refundRecord.getApplicationId());
		refundSaveDTO.setUserId(Long.valueOf(refundRecord.getUserId()));
		refundSaveDTO.setAmount(refundRecord.getAmount());
		refundSaveDTO.setApplySource("kefu");
		refundSaveDTO.setApplicants(staffMap.get(refundRecord.getStaffId()).getLoginName());
		refundSaveDTO.setApplyAt(refundRecord.getGmtCreate());
		refundSaveDTO.setServiceNo(refundRecord.getRequestNo() + SERVICE_NO_SEPARATOR + index);
		refundSaveDTO.setRefundReason(refundRecord.getRefundReason());
		refundSaveDTO.setReductionType(refundRecord.getRefundType());
		return refundSaveDTO;
	}

	private List<OpRefundApplyRecord> queryUnPushedAndApprovedAndTimeUpList() {
		return opRefundApplyRecordMapper.selectList(Wrappers.lambdaQuery(OpRefundApplyRecord.class)
				.eq(OpRefundApplyRecord::getPushFlag,"N")
				.ge(OpRefundApplyRecord::getRefundStartTime, DateUtils.getStartOfToday())
				.le(OpRefundApplyRecord::getRefundStartTime, DateUtils.getEndOfDate(new Date()))
				.eq(OpRefundApplyRecord::getApprovalStatus, ReduceAndRefundApprovalStatusEnum.SECOND_APPROVAL.getCode()));
	}
}
