package com.welab.crm.interview.handler;

import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.dangdang.ddframe.job.config.JobCoreConfiguration;
import com.dangdang.ddframe.job.config.simple.SimpleJobConfiguration;
import com.dangdang.ddframe.job.lite.config.LiteJobConfiguration;
import com.dangdang.ddframe.job.lite.lifecycle.api.JobOperateAPI;
import com.dangdang.ddframe.job.lite.lifecycle.api.JobSettingsAPI;
import com.dangdang.ddframe.job.lite.lifecycle.domain.JobSettings;
import com.dangdang.ddframe.job.lite.lifecycle.internal.operate.JobOperateAPIImpl;
import com.dangdang.ddframe.job.lite.lifecycle.internal.settings.JobSettingsAPIImpl;
import com.dangdang.ddframe.job.lite.spring.api.SpringJobScheduler;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import com.google.common.base.Optional;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;


/**
 * 动态处理定时任务的类
 */
@Component
public class ElasticJobHandler {

    @Resource
    private ZookeeperRegistryCenter registryCenter;

    private JobOperateAPI jobOperateAPI;

    private JobSettingsAPI jobSettingsAPI;

    @PostConstruct
    public void initService() {
        jobOperateAPI = new JobOperateAPIImpl(registryCenter);
        jobSettingsAPI = new JobSettingsAPIImpl(registryCenter);
    }

    private static LiteJobConfiguration.Builder simpleJobConfigBuilder(String jobName,
                                                                       Class<? extends SimpleJob> jobClass,
                                                                       int shardTotalCount,
                                                                       String cron,
                                                                       String id) {
        LiteJobConfiguration.Builder builder = LiteJobConfiguration.newBuilder(new SimpleJobConfiguration(
                JobCoreConfiguration.newBuilder(jobName, cron, shardTotalCount)
                        .jobParameter(id).build(), jobClass.getCanonicalName()
        ));
        builder.overwrite(true);
        return builder;
    }

    /**
     * 添加一个定时任务
     */
    public void addJob(String jobName, SimpleJob jobInstance, String cron, Integer shardTotalCount, String id) {
        LiteJobConfiguration jobConfig = simpleJobConfigBuilder(jobName, jobInstance.getClass(), shardTotalCount,
                cron, id).build();
        new SpringJobScheduler(jobInstance, registryCenter, jobConfig).init();
    }

    /**
     * 更新定时任务
     *
     * @param jobName job实例名称
     * @param cron    cron表达式
     */
    public void updateJob(String jobName, String cron) {
        JobSettings settings = jobSettingsAPI.getJobSettings(jobName);
        settings.setCron(cron);
        jobSettingsAPI.updateJobSettings(settings);
    }

    /**
     * 删除定时任务
     *
     * @param jobName job实例名称
     */
    public void removeJob(String jobName) {
        // 删除zk上的运行节点
        jobOperateAPI.remove(Optional.of(jobName), Optional.absent());
    }
}
