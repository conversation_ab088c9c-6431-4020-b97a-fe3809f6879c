package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.bo.MessageBO;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 短信发送定时任务
 * @date 2021/12/10
 */
@Slf4j
public class SmsSendJob implements SimpleJob {

    @Resource
    private MessageBO messageBO;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("SmsSendJob start.");
            doJob();
            log.info("SmsSendJob end, takes {} ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("SmsJob error. ", e);
        }
    }

    private void doJob() {
        messageBO.sendMessage();
    }
}
