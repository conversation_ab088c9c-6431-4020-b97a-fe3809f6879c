package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.InAuthCrmStaff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客服人员记录表（同步认证平台） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface InAuthCrmStaffMapper extends BaseMapper<InAuthCrmStaff> {

	
	List<InAuthCrmStaff> queryStaffByCno(@Param("cno") String cno);
}
