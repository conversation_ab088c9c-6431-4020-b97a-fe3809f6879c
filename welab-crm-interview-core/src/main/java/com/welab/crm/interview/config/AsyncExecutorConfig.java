package com.welab.crm.interview.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Description: 线程池配置
 * @date 2022/2/25 10:39
 */
@Configuration
public class AsyncExecutorConfig {

    @Bean(name = "repayTaskExecutor")
    public AsyncTaskExecutor tmkSyncExecutor() {
        ThreadPoolTaskExecutor executor;
        executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("repay-uuid-executor-");
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(20);
        // 设置拒绝策略rejection-policy：当pool已经达到max size的时候，直接抛出异常，丢弃任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 等允许核心线程超时，防止没必要的空耗线程
        executor.setAllowCoreThreadTimeOut(true);
        // 设置非核心线程超时回收时间,单位：秒
        executor.setKeepAliveSeconds(120);
        executor.setQueueCapacity(10000);
        executor.initialize();
        return executor;
    }
}
