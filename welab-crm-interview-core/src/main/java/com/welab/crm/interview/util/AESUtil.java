package com.welab.crm.interview.util;


import com.welab.crm.interview.dto.EncryptedDTO;
import com.welab.crm.interview.vo.EncryptedVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public final class AESUtil {

    /**
     * SHA1加密密钥（用于增加加密的复杂度） 生成签名
     */
    public static final String SHA1_SECRET_KEY = "QIVH9VddlreVoYZePVqYSg==";

    /**
     * 对数据用AES加密再用Base64编码
     *
     * @param data 待加密数据
     */
    public static String aesEncrypt(String data, String aesKey) {
        try {
            // 加密算法/工作模式/填充方式
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            byte[] dataBytes = data.getBytes();
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(Base64Utils.decodeFromString(aesKey), "AES"));
            byte[] result = cipher.doFinal(dataBytes);
            return Base64Utils.encodeToString(result);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesEncrypt失败：data={}，异常：{}", data, e);
        }
        return null;
    }

    /**
     * 对数据用AES解密
     *
     * @param encryptedDataBase64 已被base64编码的加密字节数据
     */
    public static String aesDecrypt(String encryptedDataBase64, String aesKey) {
        try {
            // 加密算法/工作模式/填充方式
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            byte[] dataBytes = Base64Utils.decodeFromString(encryptedDataBase64);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(Base64Utils.decodeFromString(aesKey), "AES"));
            byte[] result = cipher.doFinal(dataBytes);
            return new String(result);
        } catch (Exception e) {
            log.error("执行CodecUtil.aesDecrypt失败：data={}，异常：{}", encryptedDataBase64, e);
        }
        return null;
    }

    /**
     * 生成sign签名 data=加密数据+时间戳
     * 对数据用SHA1加密再转换为16进制
     */
    public static String generateSign(String data) {
        return DigestUtils.sha1Hex(data + SHA1_SECRET_KEY);
    }

    /**
     * 先验证签名然后解密数据
     *
     * @param vo 包含签名和加密的字符串
     * @return 解密出来的真实数据json
     */
    public static String decryptData(EncryptedVO vo, String aesKey) {
        String sign = generateSign(vo.getResponse() + vo.getTimestamp());
        if (sign.equals(vo.getSign())) {
            return aesDecrypt(vo.getResponse(), aesKey);
        } else {
            throw new FastRuntimeException("验签失败");
        }
    }
}
