package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.service.AiPushService;

import com.welab.crm.interview.service.TrTokenService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 定时拉取天润的中控呼入报表数据
 * <AUTHOR>
 */

@Slf4j
public class PullTrTrunkReportIbJob implements SimpleJob {

    @Resource
    private TrTokenService trTokenService;

    @Override
    public void execute(ShardingContext shardingContext) {
        trTokenService.saveTrunkReportIbToDb(null);
    }
}
