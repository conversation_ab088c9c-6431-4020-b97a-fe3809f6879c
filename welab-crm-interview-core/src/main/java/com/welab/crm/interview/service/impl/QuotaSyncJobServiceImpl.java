package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.TmkLoanInvite;
import com.welab.crm.interview.mapper.TmkLoanInviteMapper;
import com.welab.crm.interview.service.QuotaSyncJobService;
import com.welab.crm.interview.util.AppUtil;
import com.welab.support.credit.dto.GetAvalQuotaReq;
import com.welab.support.credit.dto.GetAvalQuotaResp;
import com.welab.support.credit.service.QuotaService;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description: 同步额度信息
 * @date 2022/4/27 10:44
 */
@Service
public class QuotaSyncJobServiceImpl implements QuotaSyncJobService {

    @Resource
    private TmkLoanInviteMapper tmkLoanInviteMapper;
    @Resource
    private QuotaService quotaService;

    @Override
    public void syncQuota() {
        // 查询审批时间是最近15天内的额度模式单进行额度状态的更新
        List<TmkLoanInvite> creditList = tmkLoanInviteMapper
                .selectList(Wrappers.lambdaQuery(TmkLoanInvite.class).eq(TmkLoanInvite::getType, "credit")
                        .eq(TmkLoanInvite::getState,"aip")
                        .ge(TmkLoanInvite::getApprovedAt,
                                DateUtil.plusHours(new Date(), -1)));

        for (TmkLoanInvite tmkLoanInvite : creditList) {
            String productCodeWithLevel = tmkLoanInvite.getProductCode();
            String productCode = AppUtil.removeProductCodeLevel(productCodeWithLevel);
            GetAvalQuotaReq dto = new GetAvalQuotaReq();
            dto.setUserId(Long.valueOf(tmkLoanInvite.getUuid()));
            dto.setProductCode(productCode);
            Response<GetAvalQuotaResp> quota = quotaService.getAvalQuota(dto);
            TmkLoanInvite temp = new TmkLoanInvite();
            if (Response.isSuccess(quota) && Objects.nonNull(quota.getResult())){
                GetAvalQuotaResp result = quota.getResult();
                temp.setAvlCredit(result.getAvlCreditLine());
                temp.setCreditLine(result.getCreditLine());
                temp.setCreditState(result.getState());
                temp.setGmtModify(new Date());
            }
            tmkLoanInviteMapper.update(temp,
                    Wrappers.lambdaUpdate(TmkLoanInvite.class).eq(TmkLoanInvite::getId, tmkLoanInvite.getId()));
        }
    }
}
