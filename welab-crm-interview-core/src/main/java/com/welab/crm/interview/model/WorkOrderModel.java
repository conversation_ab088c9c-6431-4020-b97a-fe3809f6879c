package com.welab.crm.interview.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单查询请求参数
 * @date 2021/09/29
 */
@Data
public class WorkOrderModel implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	@ApiModelProperty(value = "工单类型", name = "orderType")
    private String orderType;
	
	@ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private Long orderOneClass;

    @ApiModelProperty(value = "工单二类", name = "orderTwoClass")
    private Long orderTwoClass;

    @ApiModelProperty(value = "工单三类", name = "orderThreeClass")
    private Long orderThreeClass;
	
    @ApiModelProperty(value = "电话号码", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;
    
    @ApiModelProperty(value = "身份证号码", name = "cnid")
    private String cnid;
    
    @ApiModelProperty(value="分单开始时间", name="distributeStartDate")
	private Date distributeStartDate;
    
    @ApiModelProperty(value="分单结束时间", name="distributeEndDate")
	private Date distributeEndDate;
    
    @ApiModelProperty(value="提单开始时间", name="submitStartDate")
	private Date submitStartDate;
    
    @ApiModelProperty(value="提单结束时间", name="submitEndDate")
	private Date submitEndDate;

    @ApiModelProperty(value = "客户类型", name = "custType")
    private String custType;

    @ApiModelProperty(value = "是否催单", name = "reminderFlag")
    private String reminderFlag;

    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "申请开始时间", name = "applyStartDate")
    private Date applyStartDate;
    
    @ApiModelProperty(value = "申请结束时间", name = "applyEndDate")
    private Date applyEndDate;
    
    @ApiModelProperty(value = "审批开始时间", name = "approvalStartDate")
    private Date approvalStartDate;
    
    @ApiModelProperty(value = "审批结束时间", name = "approvalEndDate")
    private Date approvalEndDate;
    
    @ApiModelProperty(value = "确认开始时间", name = "confirmStartDate")
    private Date confirmStartDate;
    
    @ApiModelProperty(value = "确认结束时间", name = "confirmEndDate")
    private Date confirmEndDate;
    
    @ApiModelProperty(value = "放款开始时间", name = "loanStartDate")
    private Date loanStartDate;
    
    @ApiModelProperty(value = "放款结束时间", name = "loanEndDate")
    private Date loanEndDate;

    @ApiModelProperty(value = "渠道号", name = "channelCode")
    private String channelCode;

    @ApiModelProperty(value = "资金方", name = "partnerCode")
    private String partnerCode;

    @ApiModelProperty(value = "分单状态", name = "assignStatus")
    private String assignStatus;
    
    @ApiModelProperty(value = "是否标记", name = "sign")
    private String sign;
    
    @ApiModelProperty(value = "是否vip", name = "vip")
    private String vip;
    
    @ApiModelProperty(value = "工单状态", name = "status")
    private String status;
    
    @ApiModelProperty(value = "处理组", name = "groupCode")
    private String groupCode;
    
    @ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    @ApiModelProperty(value = "投诉处理状态", name = "complainStatus")
    private Integer complainStatus;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private String userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    /**
     * 客户Id
     */
    private Long custId;

    /**
     *  排除的状态
     */
    private List<String> notInStatusList;


    private List<String> groupCodeList;



    @ApiModelProperty(value = "加急标识,1-加急,0-未加急", name = "urgentFlag")
    private String urgentFlag;
    
    @ApiModelProperty(value = "未联系时间开始")
    private Integer noContactTimeStart;

    @ApiModelProperty(value = "未联系时间结束")
    private Integer noContactTimeEnd;
}
