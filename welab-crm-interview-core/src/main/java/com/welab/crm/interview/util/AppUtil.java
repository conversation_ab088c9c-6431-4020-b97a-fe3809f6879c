package com.welab.crm.interview.util;

import com.welab.crm.interview.exception.CrmInterviewException;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>

 * @date 2021/9/27 16:50
 */
public class AppUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(AppUtil.class);
    /**
     * 更具sourceId查询平台
     *
     * @param sourceId sourceId
     * @return
     */
    public static String getPlatform(Integer sourceId) {
        switch (sourceId) {
            case 1:
                return "android";
            case 2:
                return "H5";
            case 3:
                return "iOS";
            default:
                return null;
        }
    }

    public static Integer getAge(String cnid) {
        Date birthday = getBirthday(cnid);
        if (birthday != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());//XXX DateUtils.getCurrentDate()
            int yearCur = cal.get(Calendar.YEAR);
            int monthCur = cal.get(Calendar.MONTH);
            int dayCur = cal.get(Calendar.DAY_OF_MONTH);

            cal.setTime(birthday);
            int yearBir = cal.get(Calendar.YEAR);
            int monthBir = cal.get(Calendar.MONTH);
            int dayBir = cal.get(Calendar.DAY_OF_MONTH);

            return (yearCur - yearBir - ((monthCur > monthBir) || (monthCur == monthBir && dayCur >= dayBir) ? 0 : 1));
        }

        return null;
    }

    /**
     * 获取生肖
     *
     * @param cnid 身份证
     */
    public static String getZodiacSign(String cnid) {
        LocalDate birthDay = LocalDate.parse(cnid.substring(6, 14), DateTimeFormatter.ofPattern("yyyyMMdd"));
        int year = birthDay.getYear();
        if (year < 1900) {
            return "未知";
        }
        int start = 1900;
        String[] years = new String[]{"鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"};
        return years[(year - start) % years.length];
    }

    /**
     * 获取星座
     *
     * @param cnid 身份证
     */
    public static String getStarSign(String cnid) {
        String[] constellationArr = {"水瓶座", "双鱼座", "牡羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座",
                "天蝎座", "射手座", "魔羯座"};
        int[] constellationEdgeDay = {20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22};
        LocalDate birthDay = LocalDate.parse(cnid.substring(6, 14), DateTimeFormatter.ofPattern("yyyyMMdd"));
        int month = birthDay.getMonthValue();
        int day = birthDay.getDayOfMonth();
        int constellationArrIndex;
        if (day < constellationEdgeDay[month - 1]) {
            constellationArrIndex = month - 2;
        } else {
            constellationArrIndex = month - 1;
        }
        if (constellationArrIndex < 0) {
            constellationArrIndex = 11;
        }
        return constellationArr[constellationArrIndex];
    }

    private static Date getBirthday(String cnid) {
        cnid = cnid.trim();
        String birthdayFmt = "yyyyMMdd";
        String birthdayStr = null;

        switch (cnid.length()) {
            case 18:
                birthdayStr = cnid.substring(6, 14);
                break;

            case 15:
                birthdayStr = "19" + cnid.substring(6, 12);
                break;
        }

        if (StringUtils.isNotBlank(birthdayStr)) {
            try {
                return DateUtils.parseDate(birthdayStr, birthdayFmt);
            } catch (ParseException e) {
                LOGGER.error("[ProfileBO] parseDate error in getBirthday", e);
            }
        }

        return null;
    }


    public static String removeProductCodeLevel(String productCodeWithLevel) {
        if (StringUtils.isBlank(productCodeWithLevel)) {
            throw new CrmInterviewException("productCodeWithLevel is null");
        }
        if (productCodeWithLevel.endsWith("-A") || productCodeWithLevel.endsWith("-B") || productCodeWithLevel
                .endsWith("-C") || productCodeWithLevel.endsWith("-D")) {
            return productCodeWithLevel.substring(0, productCodeWithLevel.lastIndexOf("-"));
        }
        return productCodeWithLevel;
    }

    public static void main(String[] args) {
        System.out.println("removeProductCodeLevel(\"h5-gsd-A\") = " + removeProductCodeLevel("h5-gsd-A"));
    }
}
