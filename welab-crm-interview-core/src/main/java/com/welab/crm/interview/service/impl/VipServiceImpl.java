package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.vip.SVipQueryDTO;
import com.welab.crm.interview.dto.vip.VipLockDTO;
import com.welab.crm.interview.dto.vip.VipLockTipsDTO;
import com.welab.crm.interview.enums.*;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.vip.*;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.payment.dto.PaymentQueryDTO;
import com.welab.finance.payment.dubbo.PaymentDubboService;
import com.welab.finance.payment.vo.PaymentOrderVO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.loancenter.interfaces.dto.*;
import com.welab.loancenter.interfaces.enums.MemberCategoryEnum;
import com.welab.loancenter.interfaces.enums.MemberSupplierEnum;
import com.welab.loancenter.interfaces.facade.AssetBenefitServiceFacade;
import com.welab.loancenter.interfaces.facade.NewMemberServiceFacade;
import com.welab.marketing.dto.OperationLogsDTO;
import com.welab.marketing.dto.SupVipLockDTO;
import com.welab.marketing.dto.SupVipRequestDto;
import com.welab.marketing.dto.SupVipResponseDto;
import com.welab.marketing.service.NewMarketingService;
import com.welab.marketing.service.SupVipDubboService;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/22
 */
@Slf4j
@Service
public class VipServiceImpl implements VipService {

    @Resource
    private UserServiceFacade userServiceFacade;
    @Resource
    private PaymentDubboService paymentDubboService;
    @Resource
    private NewMemberServiceFacade memberServiceFacade;
    @Resource
    private NewMarketingService newMarketingService;
    @Resource
    private SupVipDubboService supVipDubboService;
    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;

    @Resource
    private AssetBenefitServiceFacade assetBenefitServiceFacade;

    @Override
    public List<VipOrderVO> getVipOrderList(Integer userId) {
        List<VipOrderVO> result = new ArrayList<>();
        if (Objects.isNull(userId)) {
            return result;
        }
        Response<List<PaymentOrderVO>> response = getPaymentResponse(userId);
        if (Objects.nonNull(response) && null != response.getResult()) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<PaymentOrderVO> orderList = response.getResult();
            result = orderList.stream().map(vo -> {
                VipOrderVO dto = new VipOrderVO();
                BeanUtils.copyProperties(vo, dto);
                if (null != vo.getTransactionTime()) {
                    dto.setTransactionTime(df.format(vo.getTransactionTime()));
                }
                if (null != vo.getChannelCode()) {
                    dto.setChannelName(ChannelCodeEnum.getChannelNameByChannelCode(vo.getChannelCode()));
                }
                //查询供应商订单号
                try {
                    VipOrderResponseDTO responseDTO = memberServiceFacade.getVipOrderByOrderNo(vo.getSeqNo());
                    if (Objects.nonNull(responseDTO)) {
                        dto.setSupplierOrderNo(responseDTO.getSupplierOrderNo());
                        dto.setSource(responseDTO.getSource());
                    }
                } catch (Exception e) {
                    log.error("会员订单号查询订单信息异常,seqNo:{}", vo.getSeqNo(), e);
                }
                //卡号脱敏
                if (StringUtils.isNotEmpty(dto.getBankCardAccount())) {
                    dto.setBankCardAccount(StringUtil.hideBankCard(dto.getBankCardAccount()));
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<VipInfoVO> getVipInfoList(SVipQueryDTO queryDTO) {
        log.info("查询会员权益: {}", queryDTO);
        List<VipInfoVO> result = new ArrayList<>();
        if (Objects.isNull(queryDTO.getUuid()) || Objects.isNull(queryDTO.getUserId())) {
            return result;
        }

        SupVipRequestDto requestDto = new SupVipRequestDto();
        BeanUtils.copyProperties(queryDTO, requestDto);
        requestDto.setPageSize(10000);
        com.welab.remoting.dubbox.api.Response<List<SupVipResponseDto>> response = newMarketingService
                .getSupVipList(requestDto);
        if (Objects.nonNull(response)) {
            List<SupVipResponseDto> list = response.getData();
            if (CollectionUtils.isNotEmpty(list)) {
                // 收集退款失败的订单号，以便查询订单退款失败原因
                result = list.stream().map(dto -> {
                    VipInfoVO vipInfoVO = new VipInfoVO();
                    BeanUtils.copyProperties(dto, vipInfoVO);
                    vipInfoVO.setPaymentStatus(VipStatus.PaymentStatusEnum.getText(vipInfoVO.getPaymentStatus()));
                    if (MemberSupplierEnum.OAK.name().equals(dto.getSupplierCode())){
                        vipInfoVO.setSupplierCode("橡树");
                    } else if (MemberSupplierEnum.WAN_HENG.name().equals(dto.getSupplierCode())){
                        vipInfoVO.setSupplierCode("万恒");
                    } else if (MemberSupplierEnum.QI_WEI.name().equals(dto.getSupplierCode())){
                        vipInfoVO.setSupplierCode("齐为");
                    }

                    if (StringUtils.isNotBlank(vipInfoVO.getMobile())) {
                        vipInfoVO.setMobile(StringUtil.hideMobile(vipInfoVO.getMobile()));
                    }
                    setZero(vipInfoVO);
                    if (Objects.isNull(vipInfoVO.getRefundAmount())) {
                        vipInfoVO.setRefundAmount(vipInfoVO.getPaymentAmount().subtract(vipInfoVO.getWylCouponAmount())
                                .subtract(vipInfoVO.getHqCouopnAmount()).subtract(vipInfoVO.getBenefitTotalCost())
                                .subtract(vipInfoVO.getJjpCouponAmount()));
                    }

                    try {
                        // 查询退款失败原因拼接到返回的vo列表上
                        VipOrderResponseDTO order = memberServiceFacade.getVipOrderByOrderNo(dto.getOrderNo());
                        vipInfoVO.setRefundStatus(VipStatus.RefundStatusEnum.getText(order.getRefundStatus()));
                        vipInfoVO.setRefundFailedCause(order.getRefundFailedCause());
                    } catch (Exception e) {
                        log.warn("getVipInfoList.getVipOrderByOrderNo exception: {}", e.getMessage(), e);
                    }

                    // 转换支付方式为中文
                    vipInfoVO.setPaymentMode(PaymentModeEnum.getDesc(vipInfoVO.getPaymentMode()));
                    vipInfoVO.setCategory(MemberCategoryEnum.valueOf(vipInfoVO.getCategory()).getCategoryName());
                    return vipInfoVO;
                }).collect(Collectors.toList());
            }
        }


        // 查询渠道方会员权益
        log.info("查询渠道方会员权益,uuid:{}", queryDTO.getUuid());
        List<AssetBenefitOrderResponseDTO> assetBenefitOrderList = assetBenefitServiceFacade.getAssetBenefitOrderByUUid(queryDTO.getUuid());
        log.info("查询渠道方会员权益, result: {}", JSON.toJSONString(assetBenefitOrderList));
        if (CollectionUtils.isNotEmpty(assetBenefitOrderList)) {
            result.addAll(assetBenefitOrderList.stream()
                    .filter(dto -> !dto.getPaymentStatus().equals(VipStatus.PaymentStatusEnum.CANCEL.getValue()))
                    .map(dto -> {
                VipInfoVO vipInfoVO = new VipInfoVO();
                BeanUtils.copyProperties(dto, vipInfoVO);
                if (dto.getPaymentStatus().equals("refund")){
                    dto.setPaymentStatus(VipStatus.PaymentStatusEnum.SUCCEED.getValue());
                    dto.setRefundStatus(VipStatus.RefundStatusEnum.SUCCEED.getValue());
                }
                vipInfoVO.setPaymentStatus(VipStatus.PaymentStatusEnum.getText(dto.getPaymentStatus()));
                vipInfoVO.setRefundStatus(VipStatus.RefundStatusEnum.getText(dto.getRefundStatus()));
                vipInfoVO.setAssetCode(dto.getSource());
                vipInfoVO.setPaymentAmount(dto.getPayAmount());
                return vipInfoVO;
            }).collect(Collectors.toList()));
        }

        return result;
    }

    private Response<List<PaymentOrderVO>> getPaymentResponse(Integer userId) {
        PaymentQueryDTO queryDTO = new PaymentQueryDTO();
        queryDTO.setOwnerId(Long.valueOf(userId));
        queryDTO.setOwnerType("User");
        return paymentDubboService.queryPaymentOrder(queryDTO);
    }

    private void setZero(VipInfoVO vipInfoVO) {
        if (Objects.isNull(vipInfoVO.getWylCouponAmount())) {
            vipInfoVO.setWylCouponAmount(BigDecimal.ZERO);
        }
        if (Objects.isNull(vipInfoVO.getHqCouopnAmount())) {
            vipInfoVO.setHqCouopnAmount(BigDecimal.ZERO);
        }

        if (Objects.isNull(vipInfoVO.getBenefitTotalCost())) {
            vipInfoVO.setBenefitTotalCost(BigDecimal.ZERO);
        }
        if (Objects.isNull(vipInfoVO.getJjpCouponAmount())) {
            vipInfoVO.setJjpCouponAmount(BigDecimal.ZERO);
        }
    }

    @Override
    public PrivilegeCardVO getUserPrivilegeCardList(Long uuid) {
        log.info("getUserPrivilegeCardList start，uuid:{}", uuid);
        PrivilegeCardVO cardVO = new PrivilegeCardVO();
        try {

            List<VipMemberResponseDTO> cards = memberServiceFacade.getActivePrivilegeCards(uuid);
            if (!Objects.isNull(cards) && !cards.isEmpty()) {
                // 用户可以拥有多张特权卡, 取用户最近一次购买的特权卡信息返回
                cards.sort(Comparator.comparing(VipMemberResponseDTO::getCardCreatedAt));
                VipMemberResponseDTO startMemberDTO = cards.get(0);
                cardVO.setCardUser(true);
                cardVO.setCardCreatedAt(
                    DateUtils.parseDate(startMemberDTO.getCardCreatedAt(), DateUtils.DATE_TIME_FORMAT));
                cards.sort(Comparator.comparing(VipMemberResponseDTO::getCardExpiryAt).reversed());
                VipMemberResponseDTO endMemberDTO = cards.get(0);
                cardVO.setCardExpiryAt(DateUtils.parseDate(endMemberDTO.getCardExpiryAt(), DateUtils.DATE_TIME_FORMAT));
                if (MemberCategoryEnum.SVIP.name().equals(endMemberDTO.getCategory())) {
                    cardVO.setCardType(MemberCategoryEnum.SVIP.getCategoryName());
                } else if (MemberCategoryEnum.VVIP.name().equals(endMemberDTO.getCategory())) {
                    cardVO.setCardType(MemberCategoryEnum.VVIP.getCategoryName());
                } else {
                    cardVO.setCardType("未知");
                }
            }

        } catch (Exception e) {
            log.error("getUserPrivilegeCardList error", e);
        }
        return cardVO;
    }

    @Override
    public PrivilegeCardVO getUserPrivilegeCardListPlus(Long uuid, Integer userId) {
        PrivilegeCardVO cardVO = getUserPrivilegeCardList(uuid);

        List<MemberLabelDTO> labels = memberServiceFacade.getMemberMarketingLabels(uuid);

        if (CollectionUtils.isNotEmpty(labels)) {
            cardVO.setLabelName(labels.stream().map(MemberLabelDTO::getLabelDesc).collect(Collectors.toList()));
        }
        return cardVO;
    }

    @Override
    public List<VipBenefitOrderResVO> getVipBenefitOrders(String orderNo) {
        log.info("getVipBenefitOrders 查询用户权益:{}", orderNo);
        List<VipBenefitOrderResponseDTO> vipBenefitOrders = memberServiceFacade.getVipBenefitOrders(orderNo);

        if (Objects.isNull(vipBenefitOrders)) {
            return Collections.emptyList();
        }
        return vipBenefitOrders.stream().map(item -> {
            VipBenefitOrderResVO vo = new VipBenefitOrderResVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());

    }

    @Override
    public List<VipOperateLogVO> getVipOperateLog(String orderNo) {
        try {
            log.info("getVipOperateLog 查询会员订单操作日志,orderNo:{}", orderNo);
            Response<List<OperationLogsDTO>> resList = supVipDubboService.getOperationLogByServiceNo(orderNo);
            if (Response.isSuccess(resList)) {
                return resList.getResult().stream().map(item -> {
                    VipOperateLogVO logVO = new VipOperateLogVO();
                    BeanUtils.copyProperties(item, logVO);
                    return logVO;
                }).collect(Collectors.toList());
            } else {
                log.warn("getVipOperateLog 查询会员操作记录失败,res:{}", JSON.toJSONString(resList));
            }
        } catch (Exception e) {
            log.warn("getVipOperateLog 查询会员操作记录异常", e);
        }
        throw new FastRuntimeException("查询失败");

    }


    @Override
    public Response<String> unlock(VipLockDTO dto) {
        log.info("unlock 解冻会员订单,请求参数:{}", JSON.toJSONString(dto));
        try {
            SupVipLockDTO lockDTO = new SupVipLockDTO();
            BeanUtils.copyProperties(dto, lockDTO);
            lockDTO.setOperationMan(dto.getOperatorEmail());
            lockDTO.setUuid(Long.valueOf(dto.getUuid()));
            Response<String> res = supVipDubboService.unLockOrder(lockDTO);
            if (Response.isSuccess(res)) {
                return Response.success("解冻成功");
            } else {
                log.warn("unlock 解冻会员订单失败,res:{}", JSON.toJSONString(res));
            }
        } catch (Exception e) {
            log.error("unlock 解冻会员订单异常");
        }
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "解冻失败", null);

    }


    @Override
    public Response<String> lock(VipLockDTO dto) {
        log.info("lock 冻结会员订单,请求参数:{}", JSON.toJSONString(dto));
        try {
            SupVipLockDTO lockDTO = new SupVipLockDTO();
            BeanUtils.copyProperties(dto, lockDTO);
            lockDTO.setOperationMan(dto.getOperatorEmail());
            lockDTO.setUuid(Long.valueOf(dto.getUuid()));
            Response<String> res = supVipDubboService.lockOrder(lockDTO);
            if (Response.isSuccess(res)) {
                return Response.success("冻结成功");
            } else {
                log.warn("lock 冻结会员订单,res:{}", JSON.toJSONString(res));
            }
        } catch (Exception e) {
            log.error("lock 冻结会员订单异常", e);
        }
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "冻结失败", null);
    }

    @Override
    public Response<String> lockTips(VipLockTipsDTO dto) {
        String result = "";
        VipOrderResponseDTO vipDetail = memberServiceFacade.getVipOrderByOrderNo(dto.getOrderNo());
        if (Objects.nonNull(vipDetail)) {
            if (DateUtil.isBeforeToday(vipDetail.getExpiryAt().getTime())) {
                result = "会员已过期，不支持退费";
                return Response.success(result);
            }
            LoanApplicationDTO loan = loanApplicationServiceFacade.getLoanApplication(dto.getUuid(), vipDetail.getPrivilegeApplicationId());
            if (Objects.nonNull(loan)) {
                if (LoanApplicationStateEnum.CONFIRMED.equals(LoanApplicationStateEnum.getStateEnum(loan.getState()))) {
                    result = "存在已确认订单，需取消订单才可退费";
                    return Response.success(result);
                } else if (LoanApplicationStateEnum.DISBURSED.equals(LoanApplicationStateEnum.getStateEnum(loan.getState()))) {
                    result = "存在已放款订单，需结清订单才可退费";
                    return Response.success(result);
                }
            }
        }
        return Response.success(result);
    }
}
