package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 机器人告警记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("robot_warning_record")
public class RobotWarningRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 告警员工
     */
    private String loginName;


    /**
     * 组别
     */
    private String groupCode;

    /**
     * 告警接口
     */
    private String type;

    /**
     * 告警内容
     */
    private String content;

    /**
     * 告警时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;


}
