package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资金方信息汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_partner_info")
public class OpPartnerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资金方名称
     */
    private String partnerName;

    /**
     * 机构号
     */
    private String institutionNumber;

    /**
     * 放款卡
     */
    private String loanCard;

    /**
     * 系统批扣时间-还款日
     */
    private String systemRepayTimeNormal;

    /**
     * 宽限期
     */
    private String gracePeriod;

    /**
     * h5还款
     */
    private String h5Repay;

    /**
     * 一期一期提前还款/账单展示
     */
    private String earlyRepayEachInstallment;

    /**
     * 提前结清说明
     */
    private String earlySettlementMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 结清证明说明
     */
    private String settlementProof;


}
