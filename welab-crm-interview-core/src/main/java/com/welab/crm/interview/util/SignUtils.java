package com.welab.crm.interview.util;

import com.welab.util.security.MD5;
import lombok.extern.slf4j.Slf4j;

/**
 * 校验签名工具类
 */
@Slf4j
public class SignUtils {

    private static final String SECRET_KEY = "csp_vip123";

    /**
     * 签名验证
     */
    public static boolean checkSign(String mobile, String sign, Long timestamp) {
        // 在5分钟范围之外，访问已过期
        Long currentTime = System.currentTimeMillis() / 1000;
        if (Math.abs(currentTime - timestamp) > 300) {
            return false;
        }
        String encryptStr = mobile + SECRET_KEY + timestamp;
        String signLocal = MD5.encrypt(encryptStr).toUpperCase();
        return sign.equals(signLocal);
    }

    public static void main(String[] args) {
        String mobile = "13745451122";
        Long currentTime = System.currentTimeMillis() / 1000;
        System.out.println("currentTime = " + currentTime);
        String encryptStr = mobile + SECRET_KEY + currentTime;
        String signLocal = MD5.encrypt(encryptStr).toUpperCase();

        System.out.println("signLocal = " + signLocal);
    }
}

