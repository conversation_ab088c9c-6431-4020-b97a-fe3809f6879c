package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.WoTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.dto.workorder.WorkOrderContactQueryDTO;
import com.welab.crm.interview.model.WorkOrderModel;
import com.welab.crm.interview.vo.workorder.WorkOrderNoticeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface WoTaskMapper extends BaseMapper<WoTask> {


    /**
     * 根据工单类型查询全部工单
     * @param typeList
     * @return 工单列表
     */
    List<WoTask> queryAllByType(@Param("typeList") List<String> typeList);

    /**
     * 根据条件查询需要通知的工单
     * @param dto 请求参数
     * @return 通知的工单列表
     */
    List<WorkOrderNoticeVO> queryNoticeOrder(WorkOrderContactQueryDTO dto);

	/**
	 * 根据工单三类跟客户Id查询工单
	 * @param reqDTO
	 * @return
	 */
	List<WoTask> queryWorkOrderHistoryByCondition(@Param("reqDTO") WorkOrderModel reqDTO);}
