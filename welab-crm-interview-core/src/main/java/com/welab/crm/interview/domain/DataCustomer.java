package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("data_customer")
public class DataCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 身份证
     */
    private String cnid;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 当前地址
     */
    private String address;

    /**
     * 家庭地址
     */
    private String familyAddress;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 信用额度
     */
    private BigDecimal creditline;

    /**
     * 可用额度
     */
    private BigDecimal avlCreditline;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 最后联系时间
     */
    private Date lastContactDate;

    /**
     * 学历
     */
    private String degree;

    /**
     * 是否vip
     */
    private Boolean vip;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册渠道
     */
    private String registerOrigin;

    /**
     * 用户类型
     */
    private String custType;

    /**
     * 绑卡时间
     */
    private Date bankDate;

    /**
     * 二类户账户
     */
    private String elecCardNo;

    /**
     * 支付宝授权
     */
    private Boolean isAlipayAuth;

    /**
     * 运营商授权
     */
    private Boolean isCarrierAuth;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 是否注销
     */
    private Boolean isZx;

    /**
     * 额度状态
     */
    private String creditState;


}
