package com.welab.crm.interview.service.impl;

import com.welab.crm.interview.bo.SatisfactionVerifyBO;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.service.SatisfactionService;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import com.welab.xdao.context.page.Page;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/24
 */
@Service
public class SatisfactionServiceImpl implements SatisfactionService {

    @Resource
    private SatisfactionVerifyBO satisfactionVerifyBO;

    @Override
    public Page<ReportSatisfactionVO> getSatisfactionReport(RepeatCallDTO dto) {
        return satisfactionVerifyBO.getSatisfactionReport(dto);
    }

    @Override
    public List<ReportSatisfactionVO> getSatisfactionReportList(RepeatCallDTO dto) {
        return satisfactionVerifyBO.getSatisfactionReportList(dto);
    }
}
