package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.dto.wechat.WeChatMarkdownReqDTO;
import com.welab.crm.interview.dto.wechat.WeChatReqBaseDTO;
import com.welab.crm.interview.dto.wechat.WeChatReqDTO;
import com.welab.crm.interview.enums.WeChatMsgEnum;
import com.welab.crm.interview.service.WeChatDubboService;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.security.util.AESUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 企业微信机器人群聊服务具体实现
 */
@Slf4j
@Service
public class WechatPushServiceImpl implements WeChatDubboService {

    @Value("${welab.wechat.robot.push.url}")
    private String wechatUrl;

    @Value("${welab.wechat.robot.push.key}")
    private String wechatKey;


    @Override
    public Response<Boolean> pushMsgToWeChatRobot(WeChatReqBaseDTO reqDto) {
        Response<Boolean> checkResponse = checkParam(reqDto);
        if (checkResponse != null) {
            return checkResponse;
        }

        String postUrl = getPostUrl(reqDto);
        String errMsg = null;
        try {
            String contentJson = "";
            if ("text".equals(reqDto.getMsgType())) {
                contentJson = getTextContentJson(reqDto);
            } else if ("markdown".equals(reqDto.getMsgType())){
                contentJson = getMarkdownContentJson(reqDto);
            }
            String jsonStr = HttpClientUtil.doPost(postUrl, contentJson);
            // 判断返回是否正确
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            String errCode = jsonObject.getString("errcode");
            if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(errCode)) {
                return Response.success(true);
            }
            errMsg = jsonObject.getString("errmsg");
            log.warn("push to wechat group robot error: {}, {}", errCode, errMsg);
        } catch (Exception e) {
            log.error("push to wechat group robot exception: {}", e.getMessage());
            errMsg = e.getMessage();
        }

        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), errMsg, false);
    }

    private Response<Boolean> checkParam(WeChatReqBaseDTO reqDto) {
        if (StringUtils.isBlank(reqDto.getMsgType())) {
            reqDto.setMsgType(WeChatMsgEnum.TEXT.getValue());
        }
        if (StringUtils.isBlank(reqDto.getContent())) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "content must not be null", false);
        }
        return null;
    }

    private String getPostUrl(WeChatReqBaseDTO reqDto) {
        if (StringUtils.isBlank(reqDto.getKey())) {
            String realKey = AESUtil.decrypt(wechatKey, "we*!@796PXdya953");
            return wechatUrl + "?key=" + realKey;
        } else if ("workOrder".equals(reqDto.getType())) {
            //工单发送不同分区机器人
            return wechatUrl + "?key=" + reqDto.getKey();
        } else {
            return wechatUrl + "?key=" + reqDto.getKey();
        }
    }

    private String getTextContentJson(WeChatReqBaseDTO reqDto) throws JsonProcessingException {
        WeChatReqDTO dstDTO = new WeChatReqDTO();
        dstDTO.setText(reqDto);
        dstDTO.setMsgType(reqDto.getMsgType());
        reqDto.setMsgType(null);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return mapper.writeValueAsString(dstDTO);
    }

    private String getMarkdownContentJson(WeChatReqBaseDTO reqDto) throws JsonProcessingException {
        WeChatMarkdownReqDTO dstDTO = new WeChatMarkdownReqDTO();
        dstDTO.setMarkdown(reqDto);
        dstDTO.setMsgType(reqDto.getMsgType());
        reqDto.setMsgType(null);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return mapper.writeValueAsString(dstDTO);
    }
}
