package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通话ai小结
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("call_info_ai_summary")
public class CallInfoAiSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通话记录唯一id
     */
    private String cdrMainUniqueId;

    /**
     * 总结
     */
    private String summary;

    /**
     * 流水号
     */
    private String uid;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 对话内容
     */
    private String dialogue;


}
