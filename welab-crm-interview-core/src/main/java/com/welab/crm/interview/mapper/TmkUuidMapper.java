package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.TmkUuid;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电销UUID表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-08-15
 */
public interface TmkUuidMapper extends BaseMapper<TmkUuid> {

    List<TmkUuid> selectNeedFilterUuid(@Param("userId") Integer userId);
}
