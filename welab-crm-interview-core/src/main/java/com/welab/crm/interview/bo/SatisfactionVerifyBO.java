package com.welab.crm.interview.bo;

import com.welab.crm.interview.domain.ConSatisfaction;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.dto.satisfaction.SatisfactionVerifyDTO;
import com.welab.crm.interview.mapper.ConSatisfactionMapper;
import com.welab.crm.interview.util.NumberUtil;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import com.welab.xdao.context.page.Page;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/24
 */
@Slf4j
@Component
public class SatisfactionVerifyBO {

    @Resource
    private ConSatisfactionMapper conSatisfactionMapper;

    /**
     * 添加满意度调查记录
     */
    public void addRecord(SatisfactionVerifyDTO dto) {
        ConSatisfaction satisfaction = new ConSatisfaction();
        satisfaction.setCdrEnterpriseId(dto.getCdr_enterprise_id());
        satisfaction.setCdrNumberTrunk(dto.getCdr_number_trunk());
        satisfaction.setCdrHotline(dto.getCdr_hotline());
        satisfaction.setCdrMainUniqueId(dto.getCdr_main_unique_id());
        satisfaction.setCdrCustomerNumber(dto.getCdr_customer_number());
        satisfaction.setCdrCallType(dto.getCdr_call_type());
        long timestamp = Long.parseLong(dto.getSv_start_time()) * 1000;
        satisfaction.setSvStartTime(new Date(timestamp));
        timestamp = Long.parseLong(dto.getSv_end_time()) * 1000;
        satisfaction.setSvEndTime(new Date(timestamp));
        satisfaction.setBridgedCno(dto.getBridged_cno());
        satisfaction.setCdrTransfer(dto.getCdr_transfer());
        satisfaction.setSvKeys(dto.getSv_keys());
        conSatisfactionMapper.insert(satisfaction);
    }

    /**
     * 分页查询坐席满意度报表
     */
    public Page<ReportSatisfactionVO> getSatisfactionReport(RepeatCallDTO dto) {
        List<String> hotlines = Arrays.asList(dto.getHotline().split(","));
        List<ReportSatisfactionVO> list = conSatisfactionMapper.selectSatisfactionReport(dto, hotlines);
        List<ReportSatisfactionVO> resultList = buildTeamAvg(list);
        int totalRows = resultList.size();
        return Page.initPage(getPageList(dto.getCurrentPage(), dto.getRowsPerPage(), resultList, totalRows), totalRows,
            dto.getRowsPerPage(), dto.getCurrentPage());
    }

    private List<ReportSatisfactionVO> getPageList(Integer currentPage, Integer rowsPerPage,
        List<ReportSatisfactionVO> resultList, int totalRows) {
        List<ReportSatisfactionVO> pageList = null;
        int startIndex = (currentPage - 1) * rowsPerPage;
        int endIndex = currentPage * rowsPerPage;
        if (totalRows <= startIndex) {
            pageList = new ArrayList<>();
        } else {
            pageList = resultList.subList(startIndex, Math.min(totalRows, endIndex));
        }

        return pageList;
    }


    /**
     * 查询坐席满意度报表
     */
    public List<ReportSatisfactionVO> getSatisfactionReportList(RepeatCallDTO dto) {
        List<String> hotlines = Arrays.asList(dto.getHotline().split(","));
        List<ReportSatisfactionVO> list = conSatisfactionMapper
                .selectSatisfactionReport(dto, hotlines);
        return buildTeamAvg(list);
    }


    /**
     * 计算团队平均
     * @param list
     */
    private List<ReportSatisfactionVO> buildTeamAvg(List<ReportSatisfactionVO> list) {

        Map<String, List<ReportSatisfactionVO>> satisfactionMap = list.stream()
                .collect(Collectors.groupingBy(ReportSatisfactionVO::getGroupName));
        // 这个对象用来保存总的合计
        ReportSatisfactionVO total = new ReportSatisfactionVO();
        total.setDate("合计");
        int totalCallInNum = 0;
        int totalInviteNum = 0;
        int totalNoInviteNum = 0;
        int totalPartNum = 0;
        int totalNoPartNum = 0;
        int totalVsNum = 0;
        int totalSNum = 0;
        int totalCommonNum = 0;
        int totalUSNum = 0;
        int totalUVsNum = 0;
        int totalNum1 = 0;
        int totalNum2 = 0;
        int totalNum3 = 0;
        int totalNum4 = 0;
        for (Entry<String, List<ReportSatisfactionVO>> entry : satisfactionMap.entrySet()) {
            ReportSatisfactionVO sumVo = new ReportSatisfactionVO();
            int callInNum = 0;
            int inviteNum = 0;
            int noInviteNum = 0;
            int partNum = 0;
            int noPartNum = 0;
            int vsNum = 0;
            int sNum = 0;
            int commonNum = 0;
            int uSNum = 0;
            int uVsNum = 0;
            int num1 = 0;
            int num2 = 0;
            int num3 = 0;
            int num4 = 0;

            for (ReportSatisfactionVO vo : entry.getValue()) {
                callInNum += Integer.parseInt(vo.getCallInNum());
                inviteNum += Integer.parseInt(vo.getInviteNum());
                noInviteNum += Integer.parseInt(vo.getNoInviteNum());
                partNum += Integer.parseInt(vo.getPartNum());
                noPartNum += Integer.parseInt(vo.getNoPartNum());
                vsNum += Integer.parseInt(vo.getVsNum());
                sNum += Integer.parseInt(vo.getSNum());
                commonNum += Integer.parseInt(vo.getCommonNum());
                uSNum += Integer.parseInt(vo.getUSNum());
                uVsNum += Integer.parseInt(vo.getUVsNum());
                num1 += Integer.parseInt(vo.getNum1());
                num2 += Integer.parseInt(vo.getNum2());
                num3 += Integer.parseInt(vo.getNum3());
                num4 += Integer.parseInt(vo.getNum4());

                totalCallInNum += Integer.parseInt(vo.getCallInNum());
                totalInviteNum += Integer.parseInt(vo.getInviteNum());
                totalNoInviteNum += Integer.parseInt(vo.getNoInviteNum());
                totalPartNum += Integer.parseInt(vo.getPartNum());
                totalNoPartNum += Integer.parseInt(vo.getNoPartNum());
                totalVsNum += Integer.parseInt(vo.getVsNum());
                totalSNum += Integer.parseInt(vo.getSNum());
                totalCommonNum += Integer.parseInt(vo.getCommonNum());
                totalUSNum += Integer.parseInt(vo.getUSNum());
                totalUVsNum += Integer.parseInt(vo.getUVsNum());
                totalNum1 += Integer.parseInt(vo.getNum1());
                totalNum2 += Integer.parseInt(vo.getNum2());
                totalNum3 += Integer.parseInt(vo.getNum3());
                totalNum4 += Integer.parseInt(vo.getNum4());
            }

            sumVo.setDate(entry.getKey() + " 团队合计");
            sumVo.setGroupName(entry.getKey());
            sumVo.setCallInNum(String.valueOf(callInNum));
            sumVo.setInviteNum(String.valueOf(inviteNum));
            sumVo.setNoInviteNum(String.valueOf(noInviteNum));
            sumVo.setPartNum(String.valueOf(partNum));
            sumVo.setNoPartNum(String.valueOf(noPartNum));
            sumVo.setVsNum(String.valueOf(vsNum));
            sumVo.setSNum(String.valueOf(sNum));
            sumVo.setCommonNum(String.valueOf(commonNum));
            sumVo.setUSNum(String.valueOf(uSNum));
            sumVo.setUVsNum(String.valueOf(uVsNum));
            sumVo.setNum1(String.valueOf(num1));
            sumVo.setNum2(String.valueOf(num2));
            sumVo.setNum3(String.valueOf(num3));
            sumVo.setNum4(String.valueOf(num4));
            sumVo.setInviteRate(NumberUtil.calcPercentage(noPartNum + partNum, callInNum));
            sumVo.setPartRate(NumberUtil.calcPercentage(partNum, noPartNum + partNum));
            sumVo.setSRate(NumberUtil.calcPercentage(sNum + vsNum, partNum));
            sumVo.setUsRate(NumberUtil.calcPercentage(commonNum + uSNum + uVsNum, partNum));
            sumVo.setNum1Rate(NumberUtil.calcPercentage(num1, partNum));
            sumVo.setNum2Rate(NumberUtil.calcPercentage(num2, partNum));
            sumVo.setNum3Rate(NumberUtil.calcPercentage(num3, partNum));
            sumVo.setNum4Rate(NumberUtil.calcPercentage(num4, partNum));

            list.add(sumVo);
        }

        total.setCallInNum(String.valueOf(totalCallInNum));
        total.setInviteNum(String.valueOf(totalInviteNum));
        total.setNoInviteNum(String.valueOf(totalNoInviteNum));
        total.setPartNum(String.valueOf(totalPartNum));
        total.setNoPartNum(String.valueOf(totalNoPartNum));
        total.setVsNum(String.valueOf(totalVsNum));
        total.setSNum(String.valueOf(totalSNum));
        total.setCommonNum(String.valueOf(totalCommonNum));
        total.setUSNum(String.valueOf(totalUSNum));
        total.setUVsNum(String.valueOf(totalUVsNum));
        total.setNum1(String.valueOf(totalNum1));
        total.setNum2(String.valueOf(totalNum2));
        total.setNum3(String.valueOf(totalNum3));
        total.setNum4(String.valueOf(totalNum4));
        total.setInviteRate(NumberUtil.calcPercentage(totalNoPartNum + totalPartNum, totalCallInNum));
        total.setPartRate(NumberUtil.calcPercentage(totalPartNum, totalNoPartNum + totalPartNum));
        total.setSRate(NumberUtil.calcPercentage(totalSNum + totalVsNum, totalPartNum));
        total.setUsRate(NumberUtil.calcPercentage(totalCommonNum + totalUSNum + totalUVsNum, totalPartNum));
        total.setNum1Rate(NumberUtil.calcPercentage(totalNum1, totalPartNum));
        total.setNum2Rate(NumberUtil.calcPercentage(totalNum2, totalPartNum));
        total.setNum3Rate(NumberUtil.calcPercentage(totalNum3, totalPartNum));
        total.setNum4Rate(NumberUtil.calcPercentage(totalNum4, totalPartNum));

        List<ReportSatisfactionVO> resultList = list.parallelStream()
                .sorted(Comparator.comparing(ReportSatisfactionVO::getGroupName)
                        .thenComparing(ReportSatisfactionVO::getDate))
                .collect(Collectors.toList());
        resultList.add(total);

        return resultList;

    }

    public static void main(String[] args) {
        List<String> list = Arrays.asList("2022-06-01", "呼入组", "2022-04-01");
        List<String> collect = list.stream().sorted(String::compareTo).collect(Collectors.toList());
        System.out.println("collect = " + collect);
    }
}
