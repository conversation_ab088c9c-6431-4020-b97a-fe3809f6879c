package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电销拦截规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_intercept_rule")
public class TmkInterceptRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 业务类型
     */
    private String tmkType;

    /**
     * 拦截类型，push推送拦截，assign分配拦截
     */
    private String interceptType;

    /**
     * 开关状态（1开启，0关闭）
     */
    private String status;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 进件渠道
     */
    private String channelCode;
    
    /**
     * 标签编码
     */
    private String labelCode;

    /**
     * 有效期，单位天
     */
    private Integer validDays;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 创建人
     */
    private String createStaffId;

    /**
     * 修改人
     */
    private String modifyStaffId;
}
