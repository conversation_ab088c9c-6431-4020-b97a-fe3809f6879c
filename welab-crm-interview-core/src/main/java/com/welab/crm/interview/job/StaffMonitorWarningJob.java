package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.StaffMonitorService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 员工监控告警服务
 * <AUTHOR>
 */
@Slf4j
public class StaffMonitorWarningJob implements SimpleJob {

    @Resource
    StaffMonitorService staffMonitorService;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            log.info("StaffMonitorWarningJob start");
            long startTime = System.currentTimeMillis();
            staffMonitorService.queryLogDetailAndWarning();
            staffMonitorService.queryLogByPhoneSummaryWarning();
            log.info("StaffMonitorWarningJob end use time:{}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("StaffMonitorWarningJob 执行失败", e);
        }
    }
}
