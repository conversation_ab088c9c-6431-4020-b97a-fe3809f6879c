package com.welab.crm.interview.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zj
 * @Date: 2022/3/31 0031 15:40
 * @Description:
 */
@Slf4j
public class FfmpegUtils {

    /**
     * ffmpeg执行目录
     */
    private final static String FFMPEG_PATH = "ffmpeg";


    /**
     * 合并视频
     * 
     * @param txtPath txtPath
     * @param newFilePath 新文件路径
     */
    public static void videoReCode(String txtPath, String newFilePath) {
        List<String> commands = new ArrayList<>();
        commands.add(FFMPEG_PATH);
        commands.add("-i");
        commands.add(txtPath);
        commands.add("-vcodec");
        commands.add("copy");
        commands.add("-f");
        commands.add("mp4");
        commands.add(newFilePath);
        log.info("视频重新编码,{},命令：{}", newFilePath, String.join(" ", commands));
        execAndWriteInfo(commands, "videoReCode");
        log.info("视频重新编码,{}", newFilePath);
    }

    /**
     * 执行命令并打印执行信息
     * 
     * @param commands 命令
     * @param methodName 方法名
     * @return 命令执行信息
     */
    private static String execAndWriteInfo(List<String> commands, String methodName) {
        ProcessBuilder builder = new ProcessBuilder().command(commands);
        StringBuilder sb = new StringBuilder();
        try {
            Process process = builder.start();
            BufferedReader br = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            br.close();
            return sb.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{},err:{}, info:{}", methodName, ex.getMessage(), sb);
        }
        log.info("{},info:{}", methodName, sb);
        return "";
    }

    public static void main(String[] args) {
        videoReCode("D:\\download\\01f43e65-2c66-4f27-ab2c-2185625531f3blob.mp4", "D:\\download\\abctest333.mp4");
    }
}
