package com.welab.crm.interview.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云智能对话分析质检配置
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.quality.check")
public class AliyunQualityCheckConfig {

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * 服务地址
     */
    private String endpoint = "qualitycheck.cn-hangzhou.aliyuncs.com";

    /**
     * 区域ID
     */
    private String regionId = "cn-hangzhou";

    /**
     * 协议
     */
    private String protocol = "HTTPS";

    /**
     * 业务空间ID
     */
    private Long baseMeAgentId;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 是否自动分轨（单轨录音）
     */
    private Integer autoSplit = 1;

    /**
     * 客服轨道编号（双轨录音）
     */
    private Integer serviceChannel = 0;

    /**
     * 客户轨道编号（双轨录音）
     */
    private Integer clientChannel = 1;

    /**
     * 录音采样率
     */
    private Integer sampleRate = 8;

    /**
     * 客服关键词列表（用于角色分离）
     */
    private String[] serviceChannelKeywords = {"客服", "您好", "请问", "为您", "帮您"};

    /**
     * 重试次数
     */
    private Integer maxRetryTimes = 3;

    /**
     * 重试间隔（毫秒）
     */
    private Long retryInterval = 5000L;

    /**
     * 是否启用质检
     */
    private Boolean enabled = true;

    /**
     * 质检任务查询间隔（毫秒）
     */
    private Long queryInterval = 30000L;

    /**
     * 质检任务最大查询次数
     */
    private Integer maxQueryTimes = 120;
}
