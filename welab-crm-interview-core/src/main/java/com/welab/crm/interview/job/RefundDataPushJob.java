package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.bo.ReduceAndRefundBO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
public class RefundDataPushJob implements SimpleJob {

	@Resource
	ReduceAndRefundBO reduceAndRefundBO;

	@Override
	public void execute(ShardingContext shardingContext) {
		reduceAndRefundBO.pushRefundDataToLender();
	}
}
