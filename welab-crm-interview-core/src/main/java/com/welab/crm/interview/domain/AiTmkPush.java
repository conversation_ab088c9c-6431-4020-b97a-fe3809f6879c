package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电销-ai推送数量历史表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_tmk_push")
public class AiTmkPush implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * ai推送主表关联id
     */
    private Long configId;

    /**
     * 推送数量
     */
    private Integer quantities;

    /**
     * 推送日期
     */
    private Date pushDay;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
