package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.service.BlackListService;
import com.welab.collection.interview.vo.blacklist.BlackListVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.BeanUtil;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.domain.CsLoanTransferContract;
import com.welab.crm.interview.dto.ApplicationOperateDTO;
import com.welab.crm.interview.dto.LoanInfoDTO;
import com.welab.crm.interview.dto.loanTransfer.LoanTransferDTO;
import com.welab.crm.interview.enums.DegreeEnum;
import com.welab.crm.interview.enums.RelationshipEnum;
import com.welab.crm.interview.mapper.CsLoanTransferContractMapper;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.interview.util.AppUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.CompanyVo;
import com.welab.crm.interview.vo.DocumentVo;
import com.welab.crm.interview.vo.EducationVo;
import com.welab.crm.interview.vo.LiaisonVo;
import com.welab.crm.interview.vo.LoanInfoVo;
import com.welab.crm.interview.vo.PersonalBasicVo;
import com.welab.crm.interview.vo.PersonalDetailsVo;
import com.welab.crm.interview.vo.ProfileVo;
import com.welab.document.constant.DocumentConstant;
import com.welab.document.enums.DocumentsOrderRuleEnum;
import com.welab.document.enums.DocumentsYesOrNoEnum;
import com.welab.document.interfaces.dto.DocumentDTO;
import com.welab.document.interfaces.dto.DocumentQueryDTO;
import com.welab.document.interfaces.facade.DocumentServiceFacade;
import com.welab.document.interfaces.facade.OssClientServiceFacade;
import com.welab.document.util.FilePathGenerator;
import com.welab.event.WelabEventMqPublisher;
import com.welab.event.entity.approval.CallEvent;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.loanapplication.interfaces.dto.CreditApplicationDTO;
import com.welab.loanapplication.interfaces.dto.DeviceInfoDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.CreditApplicationServiceFacade;
import com.welab.loanapplication.interfaces.facade.DeviceInfoServiceFacade;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.model.EducationDegreeSearch;
import com.welab.model.EducationInfo;
import com.welab.product.interfaces.dto.ProductDTO;
import com.welab.product.interfaces.facade.ProductServiceFacade;
import com.welab.support.credit.dto.GetUserQuotaReq;
import com.welab.support.credit.dto.GetUserQuotaResp;
import com.welab.support.credit.service.QuotaService;
import com.welab.usercenter.enums.EnumAddressType;
import com.welab.usercenter.model.CnidEntityDTO;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.model.base.Address;
import com.welab.usercenter.model.base.Admin;
import com.welab.usercenter.model.base.ApplicantInfo;
import com.welab.usercenter.model.base.Company;
import com.welab.usercenter.model.base.Education;
import com.welab.usercenter.model.base.Liaison;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.model.base.User;
import com.welab.usercenter.service.AdminService;
import com.welab.usercenter.service.CompanyService;
import com.welab.usercenter.service.EducationService;
import com.welab.usercenter.service.LiaisonService;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import com.welab.wallet.user.dto.LiaisonDTO;
import com.welab.wallet.user.service.UserInfoServiceFacade;
import com.welab.wdfgateway.service.IWeDefendService;
import com.wolaidai.appcenter.service.LoanApplicationServiceInterface;
import com.wolaidai.approval.model.CustomerTipReqDTO;
import com.wolaidai.approval.service.IApplicationService;
import com.wolaidai.metaspace.dto.AreaDTO;
import com.wolaidai.metaspace.service.AreaService;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/9/27 16:39
 */
@Service
@Slf4j
public class LoansApplicationServiceImpl implements LoansApplicationService {

    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;
    @Resource
    private UserService userService;
    @Resource
    private ProfileService profileService;
    @Resource
    private CompanyService companyService;
    @Resource
    private ProductServiceFacade productServiceFacade;
    @Resource
    private DeviceInfoServiceFacade deviceInfoServiceFacade;
    @Resource
    private AdminService adminService;
    @Resource
    private LoanDubboService loanDubboService;
    @Resource
    private CreditApplicationServiceFacade creditApplicationServiceFacade;
    @Resource
    private LoanApplicationServiceInterface loanApplicationServiceInterface;
    @Resource
    private QuotaService quotaService;
    @Resource
    private EducationService educationService;
    @Resource
    private IWeDefendService iWeDefendService;
    @Resource
    private AreaService areaService;
    @Resource
    private LiaisonService liaisonService;
    @Resource
    private DocumentServiceFacade documentServiceFacade;
    @Resource
    private OssClientServiceFacade ossClientServiceFacade;
    @Resource
    private IApplicationService applicationService;
    @Resource
    private WelabEventMqPublisher welabEventMqPublisher;
    @Resource
    private CsLoanTransferContractMapper csLoanTransferContractMapper;

    @Resource
    private BlackListService interviewBlackListService;

    @Resource
    private UserInfoServiceFacade userInfoServiceFacade;
    
    
    @Value("${lender.http.pre}")
    private String lenderHttpUrlPre;

    @Value("oss.prefix")
    private String ossPrefix;

    @Override
    public LoanInfoVo getLoansApplicationByMobileOrNameOrApplicationId(String name, String mobile,
            String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            return getLoansApplicationByMobileOrName(name, mobile);
        } else {
            LoanInfoVo result = new LoanInfoVo();
            LoanApplicationDTO loanApplication = loanApplicationServiceFacade
                    .getLoanApplicationByApplicationId(applicationId);
            if (loanApplication == null) {
                return result;
            }
            UserInfo userInfo = userService.getUserInfo(loanApplication.getBorrowerId());
            if (Objects.isNull(userInfo)) {
                result.setFlag(2);
                log.debug("---------------- >  no user");
                return result;
            }
            Profile profile = profileService.getProfileByUserId(userInfo.getUserId());
            LoanInfoDTO loanInfo = new LoanInfoDTO();
            Company company = companyService.getCompanyByUuid(userInfo.getUuid());
            if (company != null) {
                loanInfo.setCompany(company.getName());
            }
            try {
                ProductDTO product = productServiceFacade.getProductById(loanApplication.getWelabProductId());
                if (Objects.nonNull(product)) {
                    loanInfo.setProductName(product.getName());
                }
            } catch (RuntimeException e) {
                log.error("getProductById error:{}", e.getMessage());
            }
            if (profile != null) {
                loanInfo.setName(profile.getName());
            }
            loanInfo.setMobile(userInfo.getUserName());
            loanInfo.setApplicationId(loanApplication.getApplicationId());
            loanInfo.setApprovalAmount(loanApplication.getAmount());
            loanInfo.setApprovalTenor(loanApplication.getTenor());
            loanInfo.setStatus(loanApplication.getState());
            loanInfo.setApplyTime(loanApplication.getAppliedAt());
            loanInfo.setOrigin(loanApplication.getOrigin());
            //根据贷款号调用dubbo接口查询设备信息
            try {
                DeviceInfoDTO deviceInfo = deviceInfoServiceFacade
                        .selectByApplicationId(loanApplication.getApplicationId());
                if (deviceInfo != null && null != deviceInfo.getSourceId()) {
                    //过滤SourceId为null的情况
                    loanInfo.setApplyPlatform(AppUtil.getPlatform(deviceInfo.getSourceId()));
                }
            } catch (RuntimeException e) {
                log.error("查询设备接口异常,error:{}", e.getMessage());
            }
            loanInfo.setUuid(String.valueOf(userInfo.getUuid()));
            //通过uuid实时查询对应的用户状态信息
            User user = userService.getUserByUuid(userInfo.getUuid());
            if (Objects.nonNull(user)) {
                loanInfo.setBlocked(user.getBlocked());
            }
            List<LoanInfoDTO> loanInfoList = new ArrayList<>();
            loanInfoList.add(loanInfo);
            result.setFlag(0);
            result.setLoanInfo(loanInfoList);
            return result;
        }
    }


    /**
     * 手机号或姓名查询贷款申请信息
     *
     * @param name
     * @param mobile
     * @return
     */
    @Override
    public LoanInfoVo getLoansApplicationByMobileOrName(String name, String mobile) {
        List<Long> uuidList;
        LoanInfoVo result = new LoanInfoVo();
        if (StringUtils.isNotBlank(name)) {
            uuidList = userService.selectUuidByName(name);
        } else if (StringUtils.isNotBlank(mobile)) {
            uuidList = userService.selectUuidByMobile(mobile);
        } else {
            log.debug("互斥查询条件出错");
            return result;
        }
        //用户未注册
        if (uuidList == null || uuidList.isEmpty()) {
            result.setFlag(1);
            log.debug("---------------- >  no user");
            return result;
        }
        List<LoanInfoDTO> loanInfoList = new ArrayList<>();
        for (Long uuid : uuidList) {
            List<LoanInfoDTO> list = getLoanAndCreditByUuid(uuid);
            // 用户已注册未贷款
            if (CollectionUtils.isNotEmpty(list)) {
                loanInfoList.addAll(list);
                result.setFlag(0);
            }
        }
        result.setLoanInfo(loanInfoList);
        return result;
    }

    @Override
    public PersonalBasicVo getUserQuota(Long uuid) {
        PersonalBasicVo personalBasic = new PersonalBasicVo();
        //获取用户已用额度
        BigDecimal creditLine = new BigDecimal("0");
        BigDecimal avlCreditLine = new BigDecimal("0");
        BigDecimal aipAmount = new BigDecimal("0");
        String state = "";
        try {
            aipAmount = loanApplicationServiceInterface.getAipAmountByUuid(uuid);
        } catch (RuntimeException e) {
            log.warn("获取application-center的用户已用额度,uuid: {}, error: {}",uuid, e.getMessage());
        }
        //查询额度管理接口查询用户总额度
        Response<GetUserQuotaResp> userQuota = null;
        GetUserQuotaReq quotaReq = new GetUserQuotaReq();
        quotaReq.setUserId(uuid);
        try {
            userQuota = quotaService.getUserQuota(quotaReq);
        } catch (RuntimeException e) {
            log.error("调用额度管理接口异常,error:{}", e.getMessage());
        }
        if (userQuota != null && Objects.nonNull(userQuota.getResult())) {
            creditLine = userQuota.getResult().getCreditLine();
            avlCreditLine = userQuota.getResult().getAvlCreditLine().subtract(aipAmount);
            state = userQuota.getResult().getState();
        }
        personalBasic.setCreditLine(creditLine.toString());
        personalBasic.setAvailableCredit(avlCreditLine.toString());
        personalBasic.setState(state);
        return personalBasic;
    }

    /**
     * 根据uuid查出 申请人信息、教育信息、单位信息、联系人信息、证明信息
     *
     * @param uuid
     * @return
     */
    @Override
    public PersonalDetailsVo selectPersionBackgroundInformation(Long uuid) {
        PersonalDetailsVo personalDetails = new PersonalDetailsVo();
        try {
            User userInfo = userService.getUserByUuid(uuid);
//            log.info("个人信息userInfo:{}", BeanUtil.toString(userInfo));
            if (userInfo != null) {
                Integer userId = userInfo.getId();
                Profile ucProfile = profileService.getProfileByUserId(userId);
//                log.info("个人信息ucProfile:{}", BeanUtil.toString(ucProfile));
                if (ucProfile != null) {
                    String cnid = ucProfile.getCnid();
                    String name = ucProfile.getName();
                    List<EducationVo> educations = initEducation(userId, cnid, name);
                    personalDetails.setEducation(educations);
                    ProfileVo profile = initProposer(ucProfile, uuid);
                    personalDetails.setProfile(profile);
                }
                CompanyVo company = initCompany(userId);
                List<LiaisonVo> liaisons = initLiaison(userId);
                personalDetails.setCompany(company);
                personalDetails.setLiaison(liaisons);
            }
        } catch (Exception e) {
            log.error("selectPersionBackgroundInformation error:{}", e);
        }
        return personalDetails;
    }

    @Override
    public Boolean applicationOperate(ApplicationOperateDTO applicationOperateDTO) {
        CustomerTipReqDTO customerTipReqDTO = new CustomerTipReqDTO();
        boolean result = false;
        if (Objects.nonNull(applicationOperateDTO)) {
//            User user = userService.selectUnblockedUserByMobile(applicationOperateDTO.getCustServiceMobile());
//            if (user == null) {
//                throw new FastRuntimeException("客服信息不存在");
//            }
//            Profile profile = profileService.getProfileByUuid(user.getUuid());
//            if (profile == null) {
//                throw new FastRuntimeException("客服姓名不存在");
//            }
            customerTipReqDTO.setCustomerServiceName(applicationOperateDTO.getStaffName());
            customerTipReqDTO.setRemark(applicationOperateDTO.getComment());
            customerTipReqDTO.setType(Integer.parseInt(applicationOperateDTO.getType()));
            customerTipReqDTO.setApplicationId(applicationOperateDTO.getApplicationId());
            customerTipReqDTO.setTipDate(new Date());
        }

        result = applicationService.insertCustomerTips(customerTipReqDTO);

        return result;
    }

    @Override
    public String sendUrgentApproval(String applicationId) {
        if (Objects.isNull(applicationId)) {
            log.warn("applicationId is null");
            throw new FastRuntimeException("200001200", "applicationId不能为空!");
        }
        String result = sendApprovalInfo(applicationId);
        log.info("返回结果=[{}]", result);
        if (StringUtils.isBlank(result)) {
            log.info("执行修改信息,applicationId=[{}]", applicationId);
            if (applicationId.indexOf("CA") == 0) {
                /**
                 * 授信模式
                 */
                creditApplicationServiceFacade.updateUrgentApproval(1, applicationId);
            } else {
                loanApplicationServiceFacade.updateUrgentApproval(1, applicationId);
            }
        }
        return result;
    }

    @Override
    public Response<String> loanSettled(List<LoanTransferDTO> contractList) {
        log.info("push transfer data to application center start, param: {}", contractList);

        for (LoanTransferDTO transferDTO : contractList) {
            try {
                JSONObject params = new JSONObject();
                params.put("applicationIds", Collections.singletonList(transferDTO.getApplicationId()));
                params.put("settlementType", "writeoff");

                String result =
                    HttpClientUtil.doPost(lenderHttpUrlPre + "/application-service/v1/repayment/closeBySettlementType",
                        JSON.toJSONString(params));
                if (StringUtils.isNotBlank(result)) {
                    Response response = JSON.parseObject(result, Response.class);
                    if (Response.isSuccess(response)) {
                        updateTransferProcessStatus(1, null, Collections.singletonList(transferDTO.getId()));
                    } else {
                        updateTransferProcessStatus(0, response.getMessage(),
                            Collections.singletonList(transferDTO.getId()));
                    }
                } else {
                    updateTransferProcessStatus(0, "返回结果为空", Collections.singletonList(transferDTO.getId()));
                }

            } catch (Exception e) {
                String failReason;
                if (e.getMessage().length() > 100) {
                    failReason = e.getMessage().substring(0, 100);
                } else {
                    failReason = e.getMessage();
                }
                updateTransferProcessStatus(0, failReason, Collections.singletonList(transferDTO.getId()));

            }
        }

        log.info("push transfer data to application center end.");

        return Response.success();
    }

    private void updateTransferProcessStatus(int i, String failReason, List<Long> ids) {
        CsLoanTransferContract contract = new CsLoanTransferContract();
        contract.setProcessStatus(i);
        contract.setFailReason(failReason);
        csLoanTransferContractMapper.update(contract,
                Wrappers.lambdaUpdate(CsLoanTransferContract.class).in(CsLoanTransferContract::getId,ids));
    }

    @Override
    public boolean loanCancel(List<String> contractList) {
        try {
            log.info("push cancel data to application center start, param: {}", contractList);
            loanApplicationServiceFacade.loanApplicationCancel(contractList);
            log.info("push cancel data to application center end.");
            return true;
        } catch (Exception e) {
            log.error("push cancel data to application exception: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<LiaisonVo> getLiaisonByUserId(Integer userId) {
        List<LiaisonVo> result = new ArrayList<LiaisonVo>();
        try {
            List<Liaison> ucLianisonList = liaisonService.getLiaisonByUserIdList(userId);
            if (ucLianisonList != null && !ucLianisonList.isEmpty()) {
                for (Liaison ucLianison : ucLianisonList) {
                    result.add(new LiaisonVo(
                            ucLianison.getName(),
                            transRelation(ucLianison.getRelationship()),
                            ucLianison.getMobile())
                    );
                }
            }
            UserInfo userInfo = userService.getUserInfo(userId);
            if (Objects.isNull(userInfo)){
                throw new FastRuntimeException("用户不存在,userId" + userId);
            }
            // 查询钱包联系人
            List<LiaisonDTO> liaisons = userInfoServiceFacade.getLiaisons(userInfo.getUuid());
            if (CollectionUtils.isNotEmpty(liaisons)){
                for (LiaisonDTO liaison : liaisons) {
                    result.add(new LiaisonVo(
                            liaison.getName(),
                            transRelation(liaison.getRelationship()),
                            liaison.getMobile())
                    );
                }
            }
        } catch (RuntimeException e) {
            log.error("调用getLiaisonByUserIdList接口异常，error:{}", e.getMessage());
        }
        return result;
    }

    /**
     * 发送审批消息
     *
     * @param applicationId
     * @return
     */
    public String sendApprovalInfo(String applicationId) {
        log.info("LoansApplicationService sendApprovalInfo start loanApplication=[{}]", applicationId);
        String result = null;
        try {
            // 发送 进件申请单请求数据对象 到MQ
            CallEvent callEvent = new CallEvent("call_event");
            callEvent.setApplicationId(applicationId);
            log.info("[CallEventSender] Sent a CallEvent application_id:{}", applicationId);
            welabEventMqPublisher.publishEvent(callEvent);
            log.info("[CallEventSender] Sent a CallEvent task, params: {application_id:{}", applicationId);
        } catch (Throwable e) {
            log.error("[CallEventSender], params: {application_id:" + applicationId + "} occur an exception. ", e);
        }
        return result;
    }


    /**
     * 初始化证明文件
     *
     * @param userId
     * @return
     */
    public List<DocumentVo> initDocument(Integer userId) {
        List<DocumentVo> result = new ArrayList<>();
        //获取当前userId的图片
        List<DocumentDTO> documentList = getDocumentsByUuid(userId);
        if (CollectionUtils.isNotEmpty(documentList)) {
            //筛选照片
            documentList = documentList.stream().filter(d ->
                    DocumentVo.PhotoType.getType(d.getDocType()) != null
            ).collect(Collectors.toList());
            for (DocumentDTO document : documentList) {
                DocumentVo documentVo = new DocumentVo();
                if (document.getDocType() != null) {
                    documentVo.setPhotoType(document.getDocType());
                    log.info("ucDocument.getDoc_type() --->" + document.getDocType());
                    DocumentVo.PhotoType photoType = DocumentVo.PhotoType.getType(document.getDocType());
                    if (Objects.nonNull(photoType)) {
                        String photoGroup = photoType.getText();
                        log.info("photoGroup  --->" + photoGroup);
                        documentVo.setPhotoGroup(photoGroup);
                    }
                }
                if (document.getAccessToken() != null && document.getResourceFileName() != null) {
                    documentVo.setOriginalPhotoUrl(document.getWatermarkUrl());
//					documentVo.setThumbPhotoUrl(DocumentUtils.getImageUrl(document.getAccessToken(),
//						document.getResourceFileName(), ApplicationConstant.THUMB));
                    // 改成用消金接口获取oss URL
                    documentVo.setThumbPhotoUrl(ossClientServiceFacade.getOssUrl(FilePathGenerator.getInstance()
                            .getOssKey(ossPrefix, document.getAccessToken(), document.getResourceFileName(),
                                    ApplicationConstant.THUMB)));

                    if (CollectionUtils.isNotEmpty(document.getImageThumbnails())) {
                        for (DocumentDTO.ImageThumbnailInfo t : document.getImageThumbnails()) {
                            if (t.getStyle().equals(ApplicationConstant.THUMB)) {
                                documentVo.setThumbPhotoUrl(t.getWatermarkUrl());
                            }
                        }
                    }
                }
                documentVo.setId(document.getId());
                documentVo.setPhotoName(document.getResourceFileName());
                result.add(documentVo);
            }
        }
        return result;
    }

    /**
     * 调用dubbo接口获取当前用户文件
     *
     * @param userId
     * @return
     */
    public List<DocumentDTO> getDocumentsByUuid(Integer userId) {
        DocumentQueryDTO documentQueryDTO = new DocumentQueryDTO();
        documentQueryDTO.setDocumentableId(Long.valueOf(userId));
        documentQueryDTO.setDocumentableType(DocumentConstant.USER);
        documentQueryDTO.setContainDeleted(DocumentsYesOrNoEnum.YES.getValue());
        documentQueryDTO.setOrderRule(DocumentsOrderRuleEnum.DESC_CREATE_AT.getValue());
        List<DocumentDTO> documents = null;
        try {
            log.info("getDocumentsByUuid documentQueryDTO = {}", JSON.toJSONString(documentQueryDTO));
            documents = documentServiceFacade.getDocumentsByQuery(documentQueryDTO);
        } catch (Exception e) {
            log.warn("getDocumentsByUuid error.", e);
        }
        return documents;
    }

    /**
     * 初始化联系人信息
     *
     * @param userId
     * @return
     */
    private List<LiaisonVo> initLiaison(Integer userId) {
        List<LiaisonVo> result = new ArrayList<LiaisonVo>();
        try {
            List<Liaison> ucLianisonList = liaisonService.getLiaisonByUserIdList(userId);
            if (ucLianisonList != null && !ucLianisonList.isEmpty()) {
                // 查询全部催收黑名单
                List<BlackListVO> contactBlackList = interviewBlackListService.getContactBlackList();
                Map<String, List<BlackListVO>> mobileMap =
                    contactBlackList.stream().collect(Collectors.groupingBy(BlackListVO::getMobile));
                for (Liaison ucLianison : ucLianisonList) {
                    String mobile = ucLianison.getMobile();
                    boolean isBlack = mobileMap.containsKey(mobile);
                    if (isBlack) {
                        mobileMap.get(mobile).sort(Comparator.comparing(BlackListVO::getValidEndTime).reversed());
                    }
                    result.add(new LiaisonVo(ucLianison.getName(), transRelation(ucLianison.getRelationship()),
                        StringUtil.hideMobile(mobile), isBlack,
                        isBlack ? mobileMap.get(mobile).get(0).getStartTime() : null,
                        isBlack ? mobileMap.get(mobile).get(0).getEndTime() : null));
                }
            }
        } catch (RuntimeException e) {
            log.error("调用getLiaisonByUserIdList接口异常，error:{}", e.getMessage());
        }
        return result;
    }

    private String transRelation(String relation) {
        return RelationshipEnum.getDescByValue(relation);
    }

    /**
     * 初始化公司信息
     *
     * @param userId
     * @return
     */
    private CompanyVo initCompany(Integer userId) {
        CompanyVo result = new CompanyVo();
        try {
            List<Company> ucCompanyList = companyService.getAllByUserId(userId);
            if (ucCompanyList == null || ucCompanyList.isEmpty()) {
                return result;
            }
            Company ucCompany = ucCompanyList.get(0);        //最近添加的地址
            result.setCompanyName(ucCompany.getName());
            result.setDepartmentName(ucCompany.getDepartment());
            result.setCompanyAddr(getAddressDescription(ucCompany.getAddress(), false));
        } catch (Exception e) {
            log.error("initCompany异常,error:{}", e.getMessage());
        }
        return result;
    }

    /**
     * 初始化申请人信息
     *
     * @param ucProfile
     * @param uuid
     * @return
     */
    private ProfileVo initProposer(Profile ucProfile, Long uuid) {
        ProfileVo result = new ProfileVo();
        try {
            if (ucProfile != null) {
                result.setName(ucProfile.getName());
                result.setCnid(ucProfile.getCnid());
                if (StringUtils.isNotBlank(ucProfile.getCnid())) {
                    result.setAge(AppUtil.getAge(ucProfile.getCnid()));
                    // 设置生肖
                    result.setZodiacSign(AppUtil.getZodiacSign(ucProfile.getCnid()));
                    // 设置星座
                    result.setStarSign(AppUtil.getStarSign(ucProfile.getCnid()));
                }
            }
            ApplicantInfo applicantInfo = userService
                    .selectApplicantInfoByUuid(uuid, EnumAddressType.PROFILE_ADDRESSES_TYPE.getValue(),
                            EnumAddressType.RESIDENT_LOCATION_TYPE.getValue());
            if (applicantInfo != null) {
                AreaDTO area = null;
                try {
                    if (null != applicantInfo.getDistrict()) {
                        area = areaService.getAreaByDistrictCode(applicantInfo.getDistrict().toString());
                    }
                } catch (Exception e) {
                    log.error("getAreaByDistrictCode initProposer error:{}", e.getMessage());
                }
                StringJoiner residentSj = new StringJoiner(",");
                if (area != null) {
                    residentSj.add(area.getProvinceName()).add(area.getCityName());
                    result.setCurrentAddr(residentSj.toString().replaceAll("\\n", "")); //当前住址
                }
                ApplicantInfo applicantInfo2 = userService
                        .selectApplicantInfoByUuid(uuid, EnumAddressType.PROFILE_ADDRESSES_TYPE.getValue(),
                                EnumAddressType.FAMILY_LOCATION_TYPE.getValue());
                if (applicantInfo2 != null && area != null) {
                    StringJoiner familySj = new StringJoiner(",");
                    familySj.add(area.getProvinceName()).add(area.getCityName()).add(area.getDistrictName())
                            .add(applicantInfo2.getStreet());
                    result.setFamilyAddr(familySj.toString().replaceAll("\\n", "")); //家庭住址
                }

                try {
                    // 获取籍贯和签发机关
                    CnidEntityDTO entityDTO = userService.getCnidByUserUuid(uuid);
                    if (entityDTO != null) {
                        result.setIdCardAddr(getPartIdCardAddress(entityDTO.getAddress()));
                        result.setIdCardGenOrg(entityDTO.getAuthority());
                    }
                } catch (Exception e) {
                    log.warn("getCnidByUserUuid error, uuid:{}, errorMsg: {}", uuid, e.getMessage(), e);
                }

                //调用额度管理接口
                PersonalBasicVo basicVo = getUserQuota(uuid);
                if (Objects.nonNull(basicVo)) {
                    result.setCreditLine(basicVo.getCreditLine());
                    result.setAvailableCreditLine(new BigDecimal(basicVo.getAvailableCredit()));   //可用额度
                }
            }
            /*wedefend返回结果： 无此身份号码 姓名和公民身份号码一致*/
            try {
                result.setPengyuanIdentifyVeri(
                        iWeDefendService.identitycheck(applicantInfo.getCnid(), applicantInfo.getName()) == null ? ""
                                : iWeDefendService.identitycheck(applicantInfo.getCnid(), applicantInfo.getName())
                                        .getCheck_result());
            } catch (Exception e) {
                log.error("wedefend 鹏元验证异常,uuid:{}", uuid, e);
            }
        } catch (Exception e) {
            log.error("initProposer异常,uuid:{}", uuid, e);
        }
        return result;
    }

    /**
     * 获取身份证地址到市一级(如果没有则取县一级，否则全部返回)
     * @param idCardAddress 身份证完整地址
     */
    private String getPartIdCardAddress(String idCardAddress) {
        if (StringUtils.isNotBlank(idCardAddress)) {
            if (idCardAddress.contains("市")) {
                return idCardAddress.substring(0, idCardAddress.indexOf("市") + 1);
            } else if (idCardAddress.contains("盟")) {
                return idCardAddress.substring(0, idCardAddress.indexOf("盟") + 1);
            } else if (idCardAddress.contains("地区")) {
                return idCardAddress.split("地区")[0] + "地区";
            } else if (idCardAddress.contains("自治州")) {
                return idCardAddress.split("自治州")[0] + "自治州";
            } else if (idCardAddress.contains("县")) {
                return idCardAddress.substring(0, idCardAddress.indexOf("县") + 1);
            } else {
                return idCardAddress;
            }
        } else {
            return idCardAddress;
        }
    }

    /**
     * 初始化教育信息
     *
     * @param userId
     * @param cnid
     * @param name
     * @return
     */
    private List<EducationVo> initEducation(Integer userId, String cnid, String name) {
        List<EducationVo> result = new ArrayList<EducationVo>();
        List<Education> ucEducation = educationService.getEducationByUserId(userId);
        if (CollectionUtils.isEmpty(ucEducation)) {
            result.add(new EducationVo());
            return result;
        }
        EducationDegreeSearch educationDegreeSearch = new EducationDegreeSearch();
        try {
            educationDegreeSearch = iWeDefendService.pengYuanEducation(cnid, name);
//            log.info("educationDegreeSearch info:{}", BeanUtil.toString(educationDegreeSearch));
        } catch (Exception e) {
            log.error("iWeDefendService.pengYuanEducation error:{}", e.toString());
        }
        String pengyuanYanzheng = "验证失败";
        for (Education edu : ucEducation) {
            if (Objects.nonNull(educationDegreeSearch)) {
                List<EducationInfo> educationInfo = educationDegreeSearch.getEducationInfo();
                if (CollectionUtils.isNotEmpty(educationInfo)) {
                    List<EducationInfo> educationInfoList = educationInfo.stream().filter(
                            o -> StringUtils.isNotBlank(o.getCollege()) && o.getCollege()
                                    .equalsIgnoreCase(edu.getSchool()))
                            .collect(Collectors.toList());
                    if (educationInfoList.size() > 0) {
                        pengyuanYanzheng = "验证成功"; // 鹏元验证
                    }
                }
            }
            result.add(new EducationVo(
                    edu.getSchool(),
                    getAddressDescription(edu.getAddress(), true),
                    degreeApellation(edu.getDegree_id()),
                    pengyuanYanzheng,
                    edu.getEnrolled_at(),
                    edu.getGraduated_at()
            ));
        }
        return result;
    }

    private String degreeApellation(Integer degreeId) {
        if (degreeId == null) {
            return null;
        }
        return DegreeEnum.getNameById(degreeId);
    }

    /**
     * 邮编转换具体地址
     *
     * @param containsDistrict 是否包含具体的区和街道信息
     */
    private String getAddressDescription(Address address, boolean containsDistrict) {
        String result = "";
        if (Objects.isNull(address) || Objects.isNull(address.getDistrict())) {
            return "";
        }
        AreaDTO area;
        try {
            area = areaService.getAreaByDistrictCode(address.getDistrict().toString());
            StringJoiner sj = new StringJoiner(",");
            if (containsDistrict){
                result = sj.add(area.getProvinceName()).add(area.getCityName()).add(area.getDistrictName())
                        .add(address.getStreet()).toString();
            } else {
                result = sj.add(area.getProvinceName()).add(area.getCityName()).toString();
            }
        } catch (Exception e) {
            log.warn("getAddressDescription error: {}", e.getMessage(), e);
        }
        return result;
    }

    /**
     * 根据uuid查询普通进件和额度进件表的贷款数据
     *
     * @param uuid
     * @return
     */
    private List<LoanInfoDTO> getLoanAndCreditByUuid(Long uuid) {
        User userInfo = userService.getUserByUuid(uuid);
        if (userInfo == null) {
            return null;
        }
        List<LoanInfoDTO> dtoList = new ArrayList<>();
        try {
            LoanInfoDTO loanInfoDTO = null;
            //根据用户UUID获取相应的贷款申请信息列表
            List<LoanApplicationDTO> loanApplications = loanApplicationServiceFacade
                    .getLoanApplicationsByUserUuid(uuid);
            if (CollectionUtils.isNotEmpty(loanApplications)) {
                for (LoanApplicationDTO dto : loanApplications) {
                    loanInfoDTO = new LoanInfoDTO();
                    //申请人名称
                    Profile profile = profileService.getProfileByUserId(userInfo.getId());
                    if (profile != null) {
                        loanInfoDTO.setName(profile.getName());
                    }
                    //公司名称
                    Company company = companyService.getCompanyByUuid(uuid);
                    if (company != null) {
                        loanInfoDTO.setCompany(company.getName());
                    }
                    loanInfoDTO.setMobile(userInfo.getMobile());
                    loanInfoDTO.setApprovalAmount(dto.getAmount());
                    loanInfoDTO.setApprovalTenor(dto.getTenor());
                    loanInfoDTO.setOrigin(dto.getOrigin());
                    loanInfoDTO.setUuid(String.valueOf(uuid));
                    //根据贷款号调用dubbo接口查询设备信息
                    try {
                        DeviceInfoDTO deviceInfo = deviceInfoServiceFacade
                                .selectByApplicationId(dto.getApplicationId());
                        if (deviceInfo != null && null != deviceInfo.getSourceId()) {
                            //过滤SourceId为null的情况
                            loanInfoDTO.setApplyPlatform(AppUtil.getPlatform(deviceInfo.getSourceId()));
                        }
                    } catch (RuntimeException e) {
                        log.error("查询设备接口异常,error:{}", e.getMessage());
                    }
                    loanInfoDTO.setBorrowerId(dto.getBorrowerId());
                    loanInfoDTO.setApplicationId(dto.getApplicationId());
                    try {
                        ProductDTO product = productServiceFacade.getProductById(dto.getWelabProductId());
                        if (Objects.nonNull(product)) {
                            loanInfoDTO.setProductCode(product.getCode());
                            loanInfoDTO.setProductName(product.getName());
                        }
                    } catch (RuntimeException e) {
                        log.error("个人详情页-贷款申请列表,error:{}", e.getMessage());
                    }
                    loanInfoDTO.setApplyAmount(dto.getAppliedAmount());
                    loanInfoDTO.setApplyTime(dto.getAppliedAt());
                    loanInfoDTO.setApplyTenor(dto.getAppliedTenor());
                    loanInfoDTO.setStatus(dto.getState());
                    loanInfoDTO.setUrgent(dto.getUrgent());
                    loanInfoDTO.setAmount(dto.getAmount());
                    loanInfoDTO.setApprovedAt(dto.getApprovedAt());
                    if (ApplicationConstant.INIT_APPROVAL.equals(dto.getState())) {
                        if (null != dto.getAipPickedUpById()) {
                            Admin admin = adminService.getAdminById(dto.getAipPickedUpById());
                            if (null != admin) {
                                loanInfoDTO.setApprover(getAdminName(admin.getEmail()));
                            }
                        }
                    } else if (ApplicationConstant.PRETRIAL_APPLIED.equals(dto.getState())) {
                        if (null != dto.getPrePickedUpById()) {
                            Admin admin = adminService.getAdminById(dto.getPrePickedUpById());
                            if (null != admin) {
                                loanInfoDTO.setApprover(getAdminName(admin.getEmail()));
                            }
                        }
                    } else if (ApplicationConstant.APPLIED.equals(dto.getState())) {
                        if (null != dto.getPickedUpById()) {
                            Admin admin = adminService.getAdminById(dto.getPickedUpById());
                            if (null != admin) {
                                loanInfoDTO.setApprover(getAdminName(admin.getEmail()));
                            }
                        }
                    } else {
                        if (null != dto.getAipById()) {
                            Admin admin = adminService.getAdminById(dto.getAipById());
                            if (admin != null) {
                                loanInfoDTO.setApprover(getAdminName(admin.getEmail()));
                            }
                        }
                    }
                    try {
                        Response<LoanVO> loan = loanDubboService.findLoanByAppId(dto.getApplicationId());
                        log.info("getLoansApplicationByUuid findLoanByAppId,返回结果:{}", BeanUtil.toString(loan));
                        if (Objects.nonNull(loan) && Objects.nonNull(loan.getResult())) {
                            LoanVO loanVO = loan.getResult();
                            loanInfoDTO.setDisbursedTime(loanVO.getDisbursedAt());
                        }
                    } catch (RuntimeException e) {
                        log.error("getLoansApplicationByUuid查询findLoanByAppId报错,error:{}", e.getMessage());
                    }
                    loanInfoDTO.setUuid(String.valueOf(userInfo.getUuid()));
                    loanInfoDTO.setBlocked(userInfo.getBlocked());
                    loanInfoDTO.setPartnerCode(dto.getPartnerCode());
                    dtoList.add(loanInfoDTO);
                }
            }
            List<CreditApplicationDTO> creditApplications = creditApplicationServiceFacade
                    .getCreditApplicationsByUuid(uuid);
            if (CollectionUtils.isNotEmpty(creditApplications)) {
                for (CreditApplicationDTO dto : creditApplications) {
                    loanInfoDTO = new LoanInfoDTO();
                    try {
                        ProductDTO product = productServiceFacade.getProductByCode(dto.getProductCode());
                        if (Objects.nonNull(product)) {
                            loanInfoDTO.setProductCode(product.getCode());
                            loanInfoDTO.setProductName(product.getName());
                        }
                    } catch (RuntimeException e) {
                        log.error("个人详情页-贷款申请列表,error:{}", e.getMessage());
                    }
                    loanInfoDTO.setMobile(userInfo.getMobile());
                    loanInfoDTO.setApplicationId(dto.getApplicationId());
                    loanInfoDTO.setApprovalAmount(dto.getAmount());
                    loanInfoDTO.setAmount(dto.getAmount());
                    loanInfoDTO.setStatus(dto.getState());
                    loanInfoDTO.setApprovedAt(dto.getApprovedAt());
                    loanInfoDTO.setApplyTime(dto.getAppliedAt());
                    loanInfoDTO.setUrgent(dto.getUrgent());
                    loanInfoDTO.setOrigin(dto.getOrigin());
                    Profile profile = profileService.getProfileByUserId(userInfo.getId());
                    Company company = companyService.getCompanyByUuid(userInfo.getUuid());
                    if (company != null) {
                        loanInfoDTO.setCompany(company.getName());
                    }
                    if (profile != null) {
                        loanInfoDTO.setName(profile.getName());
                    }
                    //根据贷款号调用dubbo接口查询设备信息
                    try {
                        DeviceInfoDTO deviceInfo = deviceInfoServiceFacade
                                .selectByApplicationId(dto.getApplicationId());
                        if (deviceInfo != null && null != deviceInfo.getSourceId()) {
                            //过滤SourceId为null的情况
                            loanInfoDTO.setApplyPlatform(AppUtil.getPlatform(deviceInfo.getSourceId()));
                        }
                    } catch (RuntimeException e) {
                        log.error("查询设备接口异常,error:{}", e.getMessage());
                    }
                    loanInfoDTO.setUuid(String.valueOf(userInfo.getUuid()));
                    loanInfoDTO.setBlocked(userInfo.getBlocked());
                    dtoList.add(loanInfoDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(dtoList)) {
                //申请时间降序排序
                dtoList.sort(Comparator.comparing(LoanInfoDTO::getApplyTime).reversed());
            }
        } catch (RuntimeException e) {
            log.error("调用application—center的dubbo接口异常,error:{}", e.getMessage());
        }
        return dtoList;
    }


    /**
     * 邮箱转换成管理员姓名
     *
     * @param email
     * @return
     */
    private String getAdminName(String email) {
        if (email == null) {
            return "";
        }
        return email.substring(0, email.indexOf("@"));
    }
}
