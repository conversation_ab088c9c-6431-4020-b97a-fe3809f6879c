package com.welab.crm.interview.handler;

import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperConfiguration;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import com.welab.crm.interview.constant.ApplicationConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class ElasticJobConfig {

    @Value("${zookeeper.url}")
    private String serverLists;

    @Bean
    public ZookeeperConfiguration zkConfig() {
        return new ZookeeperConfiguration(serverLists, ApplicationConstant.JOB_NAMESPACE);
    }

    @Bean(initMethod = "init")
    public ZookeeperRegistryCenter registryCenter(ZookeeperConfiguration config) {
        return new ZookeeperRegistryCenter(config);
    }
}
