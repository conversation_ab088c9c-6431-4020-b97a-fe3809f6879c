package com.welab.crm.interview.job;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.impl.DictInfoServiceImpl;
import com.welab.crm.interview.service.impl.QuotaSyncJobServiceImpl;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Description: 额度更新任务
 * @date 2022/4/27 11:29
 */
@Slf4j
public class QuotaSyncJob implements SimpleJob {

    @Resource
    private QuotaSyncJobServiceImpl quotaSyncJobService;
    @Resource
    private DictInfoServiceImpl dictInfoService;

    @Override
    public void execute(ShardingContext shardingContext) {

        Boolean isJobStart = dictInfoService.queryJobSwitch("quotaSync");
        if (!isJobStart){
            log.info("QuotaSyncJob 未开启");
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            log.info("start QuotaSyncJob");
            quotaSyncJobService.syncQuota();
            log.info("end QuotaSyncJob,use time:{} ms", System.currentTimeMillis() - startTime);
        } catch (Exception e){
            log.error("QuotaSyncJob exception", e);
        }
    }


}
