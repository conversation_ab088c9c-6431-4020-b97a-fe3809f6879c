package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.collection.interview.dto.call.PhoneSummaryDTO;
import com.welab.collection.interview.service.PhoneSummaryService;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.ConPhoneSummaryDTO;
import com.welab.crm.interview.service.ConPhoneSummaryService;
import com.welab.crm.interview.vo.online.OnlineHistoryModel;
import com.welab.security.util.MD5Util;
import com.welab.privacy.util.http.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("conPhoneSummaryService")
@Slf4j
public class ConPhoneSummaryServiceImpl implements ConPhoneSummaryService {

    @Resource
    private PhoneSummaryService phoneSummaryService;


    @Value("${online.system.http.url.pre}")
    private String onlineSystemUrlPre;

    @Value("${online.system.user.token}")
    private String onlineHistoryUserToken;

    @Value("${online.system.app.key}")
    private String onlineSystemAppKey;

    @Override
    public void savePhoneSummary(ConPhoneSummaryDTO summaryDTO) {
        PhoneSummaryDTO dto = new PhoneSummaryDTO();
        BeanUtils.copyProperties(summaryDTO, dto);
        phoneSummaryService.savePhoneSummary(dto);
    }


    @Override
    public List<OnlineHistoryModel> queryOnlineHistoryContactRecord(Integer userId) {
        try {
            // 查询在线系统，返回联系历史
            Map<String, String> params = new HashMap<>();
            params.put("userId", String.valueOf(userId));
            params.put("userToken", onlineHistoryUserToken);
            String flowCode = UUID.randomUUID().toString();
            params.put("flowCode", flowCode);
            String timestamp = String.valueOf(System.currentTimeMillis());
            params.put("timestamp", timestamp);
            params.put("sign", MD5Util.md5(onlineSystemAppKey + timestamp + flowCode));

            String resStr =
                    HttpClients.create().setUrl(onlineSystemUrlPre + "/api/user/history").addURLParams(params).doGet();
            if (StringUtils.isNotBlank(resStr)) {
                JSONObject jsonObject = JSON.parseObject(resStr);
                if (jsonObject.getIntValue("ret") != 0) {
                    log.warn("催收系统查询在线系统联系历史失败,res:{}", resStr);
                    return Collections.emptyList();
                }
                return JSON.parseArray(jsonObject.getString("data"), OnlineHistoryModel.class);
            }
        } catch (Exception e){
            log.warn("queryOnlineHistoryContactRecord error", e);
        }

        return Collections.emptyList();
    }
}
