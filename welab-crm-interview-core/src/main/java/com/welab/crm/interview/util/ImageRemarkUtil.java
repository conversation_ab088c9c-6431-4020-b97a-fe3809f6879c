package com.welab.crm.interview.util;


import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfLabColor;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.welab.document.util.DocumentUtils;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Rectangle;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import javax.swing.JLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

/*******************************************************************************
 * Description: 图片水印工具类
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class ImageRemarkUtil {

    // 水印透明度
    private static float alpha = 0.5f;
    // 水印横向位置
    private static int positionWidth = 150;
    // 水印纵向位置
    private static int positionHeight = 300;


    /**
     * 原图目录
     */
    private static final String ORIGIN_FILE_DIR = "/data/crmFile/origin/";
    /**
     * 水印图片目录
     */
    private static final String WATERMARK_FILE_DIR = "/data/crmFile/watermark/";


    /**
     * @param sourceImgPath    源图片路径
     * @param tarImgPath       保存的图片路径
     * @param waterMarkContent 水印内容
     * @param fileExt          图片格式
     * @return void
     * @description
     */

    public static void imgWatermark(String sourceImgPath, String tarImgPath, String waterMarkContent, String fileExt) {

        // 水印文字大小

        int FONT_SIZE = 28;

        // 水印之间的间隔

        int XMOVE = 180;

        // 水印之间的间隔

        int YMOVE = 180;

//    Color markContentColor = Color.red;//水印颜色	220,20,60

        Color markContentColor = new Color(211 ,211 ,211);//水印颜色

        Integer degree = -45;//设置水印文字的旋转角度

        float alpha = 0.6f;//设置水印透明度 默认为1.0  值越小颜色越浅

        OutputStream outImgStream = null;

        OutputStream outImgStream2 = null;

        try {

            File srcImgFile = new File(sourceImgPath);//得到文件

            Image srcImg = ImageIO.read(srcImgFile);//文件转化为图片

            int srcImgWidth = srcImg.getWidth(null);//获取图片的宽

            int srcImgHeight = srcImg.getHeight(null);//获取图片的高

            if (srcImgWidth < srcImgHeight) {

                FONT_SIZE = 18;

                XMOVE = 130;

                YMOVE = 130;

            }

            Font font = new Font("宋体", Font.BOLD, FONT_SIZE);//水印字体，大小

            // 加水印

            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);

            // 得到画笔对象

            Graphics2D g = bufImg.createGraphics();

            // 设置对线段的锯齿状边缘处理

            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

            g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH),

                    0, 0, null);

            // 设置水印旋转

            if (null != degree) {

                g.rotate(Math.toRadians(degree), (double) bufImg.getWidth() / 2, (double) bufImg.getHeight() / 2);

            }

            g.setColor(markContentColor); //设置水印颜色

            g.setFont(font);              //设置字体

            // 设置水印文字透明度

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            int x = -srcImgWidth / 2;

            int y = -srcImgHeight / 2;

            int markWidth = FONT_SIZE * getTextLength(waterMarkContent);// 字体长度

            int markHeight = FONT_SIZE;// 字体高度

            // 循环添加水印
            g.drawString(waterMarkContent, 0,0);
            while (x < srcImgWidth * 1.5) {

                y = -srcImgHeight / 2;

                while (y < srcImgHeight * 1.5) {

                    g.drawString(waterMarkContent, x, y);

                    y += markHeight + YMOVE;

                }

                x += markWidth + XMOVE;

            }

            // 释放资源

            g.dispose();

            // 输出图片

            outImgStream = new FileOutputStream(tarImgPath);

            ImageIO.write(bufImg, fileExt, outImgStream);


        } catch (Exception e) {

            e.printStackTrace();

            e.getMessage();

        } finally {

            try {

                if (outImgStream != null) {

                    outImgStream.flush();

                    outImgStream.close();

                }

                if (outImgStream2 != null) {

                    outImgStream2.flush();

                    outImgStream2.close();

                }

            } catch (Exception e) {

                e.printStackTrace();

                e.getMessage();

            }

        }

    }


    /**
     * 获取文本长度。汉字为1:1，英文和数字为2:1
     */

    private static int getTextLength(String text) {

        int length = text.length();

        for (int i = 0; i < text.length(); i++) {

            String s = String.valueOf(text.charAt(i));

            if (s.getBytes().length > 1) {

                length++;

            }

        }

        length = length % 2 == 0 ? length / 2 : length / 2 + 1;

        return length;

    }

    public static byte[] getWatermarkFile(String url, String fileName, String watermarkContent) {

        InputStream inStream = null;
        ByteArrayOutputStream outStream = null;
        byte[] buffer = null;
        InputStream waterInputStream = null;
        String inputPath = ORIGIN_FILE_DIR + fileName;
        String outputPath = WATERMARK_FILE_DIR + fileName;
        try {
            URL urlConet = new URL(url);
            HttpURLConnection con = (HttpURLConnection) urlConet.openConnection();
//			con.setRequestMethod("GET");
            con.setConnectTimeout(10 * 1000);
            con.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            con.connect();
            inStream = con.getInputStream(); // 通过输入流获取图片数据

            DocumentUtils.saveFile(inStream, inputPath);
            File outFile = new File(outputPath);
            createDirectory(outFile);
            String ext = getExt(fileName).toLowerCase();
            if ("pdf".equalsIgnoreCase(ext)) {
                ImageRemarkUtil.pdfWaterMark(inputPath, outputPath, watermarkContent);
            } else {
                ImageRemarkUtil.imgWatermark(inputPath, outputPath, watermarkContent, ext);
            }
            waterInputStream = new FileInputStream(outputPath);
            outStream = new ByteArrayOutputStream();
            buffer = new byte[2048];
            int len = 0;
            while ((len = waterInputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            byte[] data = outStream.toByteArray();
            return data;
        } catch (Exception e) {
            log.warn("获取图片[url:{}]字节流数组失败，出现异常:{}", url, ExceptionUtils.getStackTrace(e));
        } finally {

            IOUtils.closeQuietly(waterInputStream);
            IOUtils.closeQuietly(inStream);
            IOUtils.closeQuietly(outStream);
            deleteDir(new File(inputPath));
            deleteDir(new File(outputPath));
        }

        return null;
    }

    private static void createDirectory(File file) {
        if (!file.exists() && !file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
    }

    public static String getExt(String fileName) {
        return fileName.split("\\.")[1];
    }

    private static void deleteDir(File file) {
        if (file.isDirectory()) {
            String[] list = file.list();
            for (int i = 0; i < list.length; i++) {
                deleteDir(new File(file, list[i]));
            }
        }
        if (!file.delete()){
            log.warn("file del fail");
        }
    }

    public static void pdfWaterMark(String inputFile, String outputFile, String waterMarkName) {
        try {
            PdfReader reader = new PdfReader(inputFile);
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(
                    outputFile));

            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);

            Rectangle pageRect = null;
            PdfGState gs = new PdfGState();
            gs.setFillOpacity(0.3f);
            gs.setStrokeOpacity(0.4f);
            int total = reader.getNumberOfPages() + 1;

            JLabel label = new JLabel();
            FontMetrics metrics;
            int textH = 0;
            int textW = 0;
            label.setText(waterMarkName);
            metrics = label.getFontMetrics(label.getFont());
            textH = metrics.getHeight();
            textW = metrics.stringWidth(label.getText());

            PdfContentByte under;
            for (int i = 1; i < total; i++) {
                under = stamper.getOverContent(i);// 在内容上方加水印
                //content = stamper.getUnderContent(i);//在内容下方加水印

                under.beginText();
                // 设置透明度
                gs.setFillOpacity(0.3f);
                under.setGState(gs);
                under.setColorFill(BaseColor.LIGHT_GRAY);
                under.setFontAndSize(base, 16);
                under.setTextMatrix(70, 200);

                for (int m = 0; m < 1200; m += 150) {
                    for (int n = 0; n < 600; n+=100) {
                        under.showTextAligned(Element.ALIGN_CENTER, waterMarkName, n, m, 55);
                    }
                }

                under.endText();
            }

            //一定不要忘记关闭流
            stamper.close();
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        String srcImgPath = "C:\\Users\\<USER>\\Desktop\\1.png";
        String logoText = "复 印 无 效";
        String targerTextPath = "E:/qie_text.jpg";
        imgWatermark(srcImgPath, targerTextPath, logoText, "jpg");

    }

}