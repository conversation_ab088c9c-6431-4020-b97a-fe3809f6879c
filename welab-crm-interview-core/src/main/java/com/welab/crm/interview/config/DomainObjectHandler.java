package com.welab.crm.interview.config;

import java.util.Date;
import javax.annotation.Resource;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.welab.common.keygen.KeyGenerator;

/**
 * 配置Mybatis Plus自动填充字段，全局生效
 * 
 * insert语句  自动填充》》id，gmtCreate，gmtModify 
 * update语句  自动填充》》gmtModify 
 * 
 * demain对象的字段必须如下注解才会生效，！！！注意字段命名必须准确！！！
 * @TableField(fill =FieldFill.INSERT)或者@TableField(fill = FieldFill.INSERT_UPDATE)
 * 
 * 举例如下：
 * 
 * @TableField(fill = FieldFill.INSERT) 
 * private Long id;
 * 
 * @TableField(fill = FieldFill.INSERT_UPDATE) 
 * private Date gmtModify;
 * 
 * Created by <PERSON> on 2021-09-28.
 */
@Component
public class DomainObjectHandler implements MetaObjectHandler {

    @Resource
    private KeyGenerator keyGenerator;

    @Override
    public void insertFill(MetaObject metaObject) {
        Long id = keyGenerator.generateKey();
        this.strictInsertFill(metaObject, "id", Long.class, id);
        this.strictInsertFill(metaObject, "gmtCreate", Date.class, new Date());
        this.strictInsertFill(metaObject, "gmtModify", Date.class, new Date());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "gmtModify", Date.class, new Date());
    }

}
