package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.dto.complain.ComplainAttachmentDTO;
import com.welab.collection.interview.dto.complain.ComplainDTO;
import com.welab.collection.interview.enums.ComplainEnum;
import com.welab.collection.interview.utils.AesUtils;
import com.welab.common.response.Response;
import com.welab.common.utils.IDCardUtils;
import com.welab.crm.base.workflow.busi.service.WFRunBusiService;
import com.welab.crm.base.workflow.common.dto.args.WFExecuteStartDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigTransitionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.interview.constant.WfVariableConstant;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.BairongReminderOrderReqDTO;
import com.welab.crm.interview.dto.BairongSubmitOrderReqDTO;
import com.welab.crm.interview.dto.workorder.InitCodeConfigDTO;
import com.welab.crm.interview.dto.workorder.WoAttachmentReqDTO;
import com.welab.crm.interview.dto.workorder.WorkOrderLoanReqDTO;
import com.welab.crm.interview.dto.workorder.WorkOrderSubmitReqDTO;
import com.welab.crm.interview.enums.OrderStatusEnum;
import com.welab.crm.interview.mapper.*;
import com.welab.crm.interview.model.WorkOrderModel;
import com.welab.crm.interview.service.BairongOrderService;
import com.welab.crm.interview.service.ComplainOrderService;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.util.AppUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.bairong.BairongAppVO;
import com.welab.crm.interview.vo.bairong.BairongSingleAppVO;
import com.welab.crm.interview.vo.workorder.WoAttachmentVO;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.loanapplication.interfaces.dto.CreditApplicationDTO;
import com.welab.loanapplication.interfaces.dto.EcLoanOrdersDTO;
import com.welab.loanapplication.interfaces.dto.EcOrderDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.CreditApplicationServiceFacade;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.product.interfaces.dto.ProductDTO;
import com.welab.product.interfaces.facade.ProductServiceFacade;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.model.base.BaseProfile;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BairongOrderServiceImpl implements BairongOrderService {

	@Resource
	private LoanApplicationServiceFacade loanApplicationServiceFacade;

	@Resource
	private DataCustomerMapper dataCustomerMapper;

	@Resource
	private UserService userService;

	@Resource
	private ProfileService profileService;

	@Resource
	private ProductServiceFacade productServiceFacade;

	@Resource
	private LoanApplicationServiceImpl loanApplicationService;

	@Resource
	private IUploadService iUploadService;

	@Resource
	private InAuthCrmStaffMapper inAuthCrmStaffMapper;

	@Resource
	private OpDictInfoConfMapper opDictInfoConfMapper;


	@Resource
	private WoTaskMapper woTaskMapper;

	@Resource
	private DataLoanApplicationMapper dataLoanApplicationMapper;

	@Resource
	private WoAttachmentMapper woAttachmentMapper;

	@Resource
	private WFRunBusiService runBusiService;

	@Resource
	private OpDictInfoMapper opDictInfoMapper;

	@Resource
	private ComplainOrderService complainOrderService;
	
	@Resource
	private FinanceService financeService;
	
	@Resource
	private WfRuExecutionMapper wfRuExecutionMapper;

	@Resource
	private CreditApplicationServiceFacade creditApplicationServiceFacade;


	private static final String BAIRONG_SOURCE1 = "qianxiaole";
	private static final String BAIRONG_SOURCE2 = "bairong";
	private static final String BAIRONG_SOURCE3 = "bairongcredit";


	private static final String MOBILE = "^1[3456789]\\d{9}$";


	/**
	 * 结案状态
	 */
	private static final List<String> ORDER_CLOSE_STATUS = new ArrayList<>(
			Arrays.asList("resolved_close", "unresolved_close", "not_connected_close"));

	/**
	 * 消金贷用户类型
	 */
	private static final String USER_TYPE_CASH = "cash";

	private static final String PROCESS_CODE = "NornalWO";

	/**
	 * 百融问题类型字典
	 */
	private static final String BARIONG_DICT_CATEGORY = "bairongQuestionType";


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void submitOrder(BairongSubmitOrderReqDTO dto) {

		// 1.根据渠道订单号查询出贷款号，查询出 userId
		BairongAppVO bairongAppVO;
		if (CollectionUtils.isNotEmpty(dto.getLoanOrderId())) {
			bairongAppVO = queryUserInfoByBairongOrderNo(dto.getLoanOrderId());
		} else if (CollectionUtils.isNotEmpty(dto.getUid())) {
			bairongAppVO = queryUserInfoByBairongUid(dto.getUid());
		} else {
			throw new FastRuntimeException("提现订单号和uid不能同时为空");
		}


		Integer userId = bairongAppVO.getUserId();
		List<String> applicationIdList = bairongAppVO.getApplicationId();
		// 2.查询该客户是否在 data_customer 表有记录，无则新增
		DataCustomer dataCustomer = getDataCustomer(userId);


		// 3.转化为工单提交参数并上传工单
		convertToParamsAndSubmit(dto, dataCustomer, applicationIdList);


	}

	private BairongAppVO queryUserInfoByBairongUid(List<String> uidList) {
		BairongAppVO bairongAppVO = new BairongAppVO();
		List<String> applicationList = new ArrayList<>();
		Integer userId = null;
		for (String uid : uidList) {
			BairongSingleAppVO singleAppVO = queryAppInfoByBairongUid(uid);
			checkUserIdIsSame(userId, singleAppVO.getUserId());
			userId = singleAppVO.getUserId();
			applicationList.add(singleAppVO.getApplicationId());

			if (Objects.isNull(userId) || CollectionUtils.isEmpty(applicationList)) {
				throw new FastRuntimeException("根据uid查询用户信息失败, uid:" + uid);
			}
		}
		bairongAppVO.setUserId(userId);
		bairongAppVO.setApplicationId(applicationList);

		return bairongAppVO;
	}

	private BairongSingleAppVO queryAppInfoByBairongUid(String uid) {
		EcOrderDTO ecOrderDTO = loanApplicationServiceFacade.getOrder(BAIRONG_SOURCE2, uid);
		if (Objects.isNull(ecOrderDTO)) {
			ecOrderDTO = loanApplicationServiceFacade.getOrder(BAIRONG_SOURCE3, uid);
			if (Objects.isNull(ecOrderDTO)) {
				throw new FastRuntimeException("根据uid查询用户信息失败, uid:" + uid);
			} else {
				BairongSingleAppVO bairongAppVO = new BairongSingleAppVO();
				bairongAppVO.setUserId(ecOrderDTO.getUserId());
				bairongAppVO.setApplicationId(ecOrderDTO.getApplicationId());
				return bairongAppVO;
			}
		} else {
			BairongSingleAppVO bairongAppVO = new BairongSingleAppVO();
			bairongAppVO.setUserId(ecOrderDTO.getUserId());
			bairongAppVO.setApplicationId(ecOrderDTO.getApplicationId());
			return bairongAppVO;
		}
	}

	@Override
	public void reminderOrder(BairongReminderOrderReqDTO dto) {
		List<WoTask> orderList = woTaskMapper.selectList(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, dto.getUniqueOrderNo()));
		if (CollectionUtils.isEmpty(orderList)){
			throw new FastRuntimeException("工单不存在");
		}
		WoTask woTask = orderList.get(0);
		if(woTask != null && !"1".equals(woTask.getReminderFlag())) {
			woTask.setGmtModify(new Date());
			woTask.setReminderFlag("1");
			woTaskMapper.updateById(woTask);
		}

		List<WfRuExecution> wfRuExecutionList = wfRuExecutionMapper.selectList(Wrappers.lambdaQuery(WfRuExecution.class).eq(WfRuExecution::getBusiKey, dto.getUniqueOrderNo()));
		if (CollectionUtils.isEmpty(wfRuExecutionList)){
			throw new FastRuntimeException("工单已结单");
		}
		JSONObject json = new JSONObject();
		json.put("opinion", dto.getContent());
		List<InAuthCrmStaff> staffs = inAuthCrmStaffMapper
				.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).eq(InAuthCrmStaff::getLoginName, "百融").eq(InAuthCrmStaff::getIsStatus, 1));
		
		runBusiService.urgeTask(wfRuExecutionList.get(0).getExecutionId(), String.valueOf(staffs.get(0).getId()), json.toJSONString());
	}

	private void convertToParamsAndSubmit(BairongSubmitOrderReqDTO dto, DataCustomer dataCustomer, List<String> applicationIdList) {
		// 封装工单提交参数
		WorkOrderSubmitReqDTO submitReqDTO = buildOrderSubmitReqDTO(dto, dataCustomer, applicationIdList);

		saveOrder(submitReqDTO);
	}

	private WorkOrderSubmitReqDTO buildOrderSubmitReqDTO(BairongSubmitOrderReqDTO dto, DataCustomer dataCustomer, List<String> applicationIdList) {
		WorkOrderSubmitReqDTO submitReqDTO = new WorkOrderSubmitReqDTO();
		submitReqDTO.setCustId(String.valueOf(dataCustomer.getId()));
		submitReqDTO.setCallbackFlag("1");
		if (dto.isUrgeFlag()) {
			submitReqDTO.setUrgentFlag("1");
		} else {
			submitReqDTO.setUrgentFlag("0");
		}
		submitReqDTO.setDescription(dto.getContent());
		submitReqDTO.setProcessCode(PROCESS_CODE);
		submitReqDTO.setOrderNo(dto.getUniqueOrderNo());
		submitReqDTO.setTransitionDTO(buildSubmitGdzTransitionDTO());
		submitReqDTO.setAssignedDTO(buildGdzAssignedDTO());
		submitReqDTO.setLoanList(buildLoanDetail(applicationIdList));
		List<LoanApplicationDTO> loanList = loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(Long.valueOf(dataCustomer.getUuid()), Arrays.asList("disbursed", "closed"));
		submitReqDTO.setSucLoanCount(loanList.size());

		if (CollectionUtils.isNotEmpty(dto.getAttachmentUrls())) {
			for (WoAttachmentVO attachmentVO : dto.getAttachmentUrls()) {
				byte[] bytes = HttpClientUtil.getFile(attachmentVO.getPath());
				Response<String> response = iUploadService.uploadFile(bytes, attachmentVO.getFilename());
				attachmentVO.setFilename(response.getResult());
			}
			submitReqDTO.setFileList(dto.getAttachmentUrls());
		}
		submitReqDTO.setExecutionDTO(buildExecutionDTO());


		OpDictInfoConf opDictInfoConf = convertBairongQuestionTypeToOrderType(dto.getQuestionType());
		submitReqDTO.setType(String.valueOf(opDictInfoConf.getWoTypeId()));
		submitReqDTO.setOrderOneClass(opDictInfoConf.getWoTypeFirId());
		submitReqDTO.setOrderTwoClass(opDictInfoConf.getWoTypeSecId());
		submitReqDTO.setOrderThreeClass(opDictInfoConf.getWoTypeThirId());

		return submitReqDTO;

	}

	private void saveOrder(WorkOrderSubmitReqDTO submitReqDTO) {
		List<WoTask> woTasks = woTaskMapper.selectList(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, submitReqDTO.getOrderNo()));
		if (CollectionUtils.isNotEmpty(woTasks)) {
			throw new FastRuntimeException("工单:" + submitReqDTO.getOrderNo() + "已存在");
		}
		isCustSameOrder(submitReqDTO);
		saveWorkOrderAndSubmit(submitReqDTO);
		CompletableFuture.runAsync(() -> saveWorkOrderComplain(submitReqDTO));
	}

	private void saveWorkOrderComplain(WorkOrderSubmitReqDTO reqDTO) {
		ComplainDTO complainDTO = null;
		try {
			// 组件需要的参数对象: 包括手机号、userId、合同号列表以及投诉相关内容
			complainDTO = setComplainLevelAndResult(reqDTO);
			// 投诉级别是正常，不需要核实和处理结果则不需要推送到催收系统
			if (ComplainEnum.NORMAL.getValue().equals(complainDTO.getComplainLevel()) && !complainDTO.getMustApprove()
					&& !complainDTO.getMustReview()) {
				return;
			}
			if (!StringUtils.isEmpty(reqDTO.getCustId())) {
				DataCustomer customer = dataCustomerMapper.selectById(reqDTO.getCustId());
				complainDTO.setUserId(String.valueOf(customer.getUserId()));
				complainDTO.setMobile(customer.getMobile());
			}
			List<WorkOrderLoanReqDTO> loanList = reqDTO.getLoanList();
			if (CollectionUtils.isNotEmpty(loanList)) {
				List<String> contractNoList = loanList.stream().map(WorkOrderLoanReqDTO::getApplicationId)
						.collect(Collectors.toList());
				complainDTO.setContractNoList(contractNoList);
			}

			List<ComplainAttachmentDTO> attachments = getComplainFilesDTO(reqDTO.getFileList(), Long.valueOf(reqDTO.getExecutionDTO().getCurrStaffId()), reqDTO.getExecutionDTO().getCurrStaffName());
			complainDTO.setWorkOrderNo(reqDTO.getOrderNo());
			complainDTO.setContent(reqDTO.getDescription());
			complainDTO.setAttachments(attachments);
			complainDTO.setOrderTwoClass(opDictInfoMapper.selectById(reqDTO.getOrderTwoClass()).getContent());
			complainOrderService.pushComplainOrder(complainDTO);
		} catch (Exception e) {
			log.warn("推送或者保存客户投诉异常: {}, 参数:{}", e.getMessage(), complainDTO, e);
		}
	}

	private List<ComplainAttachmentDTO> getComplainFilesDTO(List<WoAttachmentVO> fileList, Long staffId, String staffName) {
		List<ComplainAttachmentDTO> attachments = new ArrayList<>();
		if (CollectionUtils.isEmpty(fileList)){
			return attachments;
		}
		for (WoAttachmentVO attachmentVO : fileList) {
			ComplainAttachmentDTO dto = new ComplainAttachmentDTO();
			dto.setFilename(attachmentVO.getFilename());
			dto.setStaffId(staffId);
			dto.setStaffName(staffName);
			dto.setUploadTime(new Date());
			dto.setFilePath(attachmentVO.getPath());
			attachments.add(dto);
		}
		return attachments;
	}

	private ComplainDTO setComplainLevelAndResult(WorkOrderSubmitReqDTO reqDTO) {
		LambdaQueryWrapper<OpDictInfoConf> wrapper = Wrappers.<OpDictInfoConf>lambdaQuery()
				.eq(OpDictInfoConf::getWoTypeId, reqDTO.getType())
				.eq(OpDictInfoConf::getWoTypeFirId, reqDTO.getOrderOneClass())
				.eq(OpDictInfoConf::getWoTypeSecId, reqDTO.getOrderTwoClass())
				.eq(OpDictInfoConf::getWoTypeThirId, reqDTO.getOrderThreeClass())
				.eq(OpDictInfoConf::getIsStatus, 1);
		List<OpDictInfoConf> confList = opDictInfoConfMapper.selectList(wrapper);
		ComplainDTO complainDTO = new ComplainDTO();
		if (CollectionUtils.isNotEmpty(confList)) {
			OpDictInfoConf conf = confList.get(0);
			complainDTO.setComplainLevel(conf.getComplainLevel());
			complainDTO.setMustReview(conf.getMustReview());
			complainDTO.setMustApprove(conf.getMustApprove());
		} else {
			throw new FastRuntimeException("提交工单组合配置id组合异常: 参数" + reqDTO);
		}
		// 只要核实结果和处理结果有一个存在就需要处理，状态设置为处理中
		if (complainDTO.getMustReview() || complainDTO.getMustApprove()) {
			complainDTO.setApproveStatus(0);
		} else {
			complainDTO.setApproveStatus(1);
		}
		complainDTO.setUrgeStatus(Boolean.FALSE);
		return complainDTO;
	}

	private void saveWorkOrderAndSubmit(WorkOrderSubmitReqDTO reqDTO) {
		WFConfigAssignedDTO assignedDTO = reqDTO.getAssignedDTO();
		try {
			reqDTO.setStatus(OrderStatusEnum.SUBMIT.getValue());
			//保存工单信息
			saveOrUpdateWorkOrder(reqDTO);
			//工单流程
			InitCodeConfigDTO initDTO = new InitCodeConfigDTO();
			initDTO.setProcessCode(reqDTO.getProcessCode());
			// 进入流程发起页
			WFExecuteStartDTO executeStartArgs = new WFExecuteStartDTO();
			executeStartArgs.setCurGroupCode(reqDTO.getExecutionDTO().getCurrGroupCode());
			executeStartArgs.setCurStaffId(reqDTO.getExecutionDTO().getCurrStaffId());
			executeStartArgs.setBusiKey(reqDTO.getOrderNo());
			executeStartArgs.setProcessCode(reqDTO.getProcessCode());
			executeStartArgs.setAssignedStaff(assignedDTO);
			executeStartArgs.setTransition(reqDTO.getTransitionDTO());
			Map<String, Object> variables = new HashMap<>();
			variables.put(WfVariableConstant.BUSI_KEY, executeStartArgs.getBusiKey());
			variables.put(WfVariableConstant.DEPARTMENT, executeStartArgs.getCurGroupCode());
			variables.put(WfVariableConstant.IS_MANA_GROUP_MANAGER, "false");
			log.info(">>>>>>>>>业务代码对流程变量进行设值 variables={}", JSON.toJSONString(variables));
			executeStartArgs.setVariables(variables);
			JSONObject json = new JSONObject();
			json.put("opinion", reqDTO.getOpinion());
			json.put("callbackNote", reqDTO.getCallbackNote());
			executeStartArgs.setComment(json);
			// 发起流程并获得下步任务信息
			runBusiService.startExcute(executeStartArgs);
		} catch (Exception e) {
			log.error("submitWorkOrder error", e);
			throw new FastRuntimeException("工单提交失败");
		}
	}

	private void saveOrUpdateWorkOrder(WorkOrderSubmitReqDTO reqDTO) {
		String staffId = reqDTO.getExecutionDTO().getCurrStaffId();

		WoTask woTask = new WoTask();
		DataCustomer dataCustomer = dataCustomerMapper.selectById(reqDTO.getCustId());
		BeanUtils.copyProperties(reqDTO, woTask);
		saveMobileBak(reqDTO, woTask);
		woTask.setStatus(reqDTO.getStatus());
		if (dataCustomer != null) {
			woTask.setCnid(dataCustomer.getCnid());
			woTask.setAge(dataCustomer.getAge());
			woTask.setGender(dataCustomer.getGender());
			woTask.setMobile(dataCustomer.getMobile());
			woTask.setCustomerName(dataCustomer.getCustomerName());
			woTask.setCustId(dataCustomer.getId());
		}
		woTask.setSubmitTime(new Date());
		woTask.setDistributeTime(new Date());
		woTask.setCreateStaffId(staffId);
		woTask.setExternalSource(BAIRONG_SOURCE2);
		woTaskMapper.insert(woTask);

		//保存贷款信息
		List<WorkOrderLoanReqDTO> loanList = reqDTO.getLoanList();
		if (loanList != null && !loanList.isEmpty()) {
			for (WorkOrderLoanReqDTO loan : loanList) {
				DataLoanApplication dataLoan = new DataLoanApplication();
				BeanUtils.copyProperties(loan, dataLoan);
				dataLoan.setWoTaskId(woTask.getId());
				dataLoan.setConfirmTime(loan.getConfirmedTime());
				dataLoanApplicationMapper.insert(dataLoan);
			}

		}

		// 保存附件信息
		if (CollectionUtils.isNotEmpty(reqDTO.getFileList())) {
			WoAttachmentReqDTO woAttachmentReqDTO = new WoAttachmentReqDTO();
			woAttachmentReqDTO.setFileList(reqDTO.getFileList());
			woAttachmentReqDTO.setId(woTask.getId());
			saveWoAttachment(woAttachmentReqDTO, staffId);
		}
	}

	public void saveWoAttachment(WoAttachmentReqDTO woAttachmentReqDTO, String staffId) {
		if (Objects.nonNull(woAttachmentReqDTO)) {
			WoTask woTask = woTaskMapper.selectById(woAttachmentReqDTO.getId());
			if (Objects.isNull(woTask)) {
				throw new FastRuntimeException("该工单不存在:" + woAttachmentReqDTO.getId());
			}
			List<WoAttachmentVO> fileNameList = woAttachmentReqDTO.getFileList();
			// 根据文件名，获取 文件名:下载地址 map
			for (WoAttachmentVO vo : fileNameList) {
				WoAttachment woAttachment = new WoAttachment();
				woAttachment.setFilename(vo.getFilename());
				woAttachment.setPath(vo.getPath());
				woAttachment.setWoTaskId(woAttachmentReqDTO.getId());
				woAttachment.setStatus(Boolean.TRUE);
				woAttachment.setStaffId(Long.valueOf(staffId));
				woAttachmentMapper.insert(woAttachment);
			}
		}
	}

	private void isCustSameOrder(WorkOrderSubmitReqDTO dto) {
		// 查询该客户的uuid
		DataCustomer customer = dataCustomerMapper.selectById(Long.parseLong(dto.getCustId()));
		WorkOrderModel model = new WorkOrderModel();
		BeanUtils.copyProperties(dto, model);
		model.setOrderType(dto.getType());
		if (Objects.nonNull(customer) && StringUtils.isNotBlank(customer.getUuid())) {
			model.setUuid(customer.getUuid());
		} else {
			model.setCustId(Long.parseLong(dto.getCustId()));
		}
		model.setNotInStatusList(ORDER_CLOSE_STATUS);
		List<WoTask> woTasks = woTaskMapper.queryWorkOrderHistoryByCondition(model);
		if (CollectionUtils.isNotEmpty(woTasks)) {
			throw new FastRuntimeException("该客户已有同类工单处理中，无法再次提交");
		}
	}


	private void saveMobileBak(WorkOrderSubmitReqDTO reqDTO, WoTask woTask) {
		if (CollectionUtils.isEmpty(reqDTO.getMobileBakList())) {
			return;
		}
		StringBuilder mobileBakStr = new StringBuilder();
		for (String mobileBak : reqDTO.getMobileBakList()) {
			if (StringUtils.isBlank(mobileBak)) {
				continue;
			}
			boolean matches = Pattern.matches(MOBILE, mobileBak);
			if (!matches) {
				mobileBakStr.append(AesUtils.decrypt(mobileBak)).append(",");
			} else {
				mobileBakStr.append(mobileBak).append(",");
			}
		}
		if (StringUtils.isNotBlank(mobileBakStr)) {
			woTask.setMobileBaks(mobileBakStr.substring(0, mobileBakStr.length() - 1));
		}
	}

	private OpDictInfoConf convertBairongQuestionTypeToOrderType(String questionType) {
		List<OpDictInfo> opDictInfos = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, BARIONG_DICT_CATEGORY)
				.eq(OpDictInfo::getType, questionType).eq(OpDictInfo::getStatus, true));
		if (CollectionUtils.isEmpty(opDictInfos)){
			throw new FastRuntimeException("问题类型不合法");
		}
		Long bairongQuestionTypeId = opDictInfos.get(0).getId();
		List<OpDictInfoConf> confs = opDictInfoConfMapper.selectList(Wrappers.lambdaQuery(OpDictInfoConf.class).like(OpDictInfoConf::getBairongQuestionType, bairongQuestionTypeId).eq(OpDictInfoConf::getIsStatus, 1));
		if (CollectionUtils.isNotEmpty(confs)) {
			return confs.get(0);
		} else {
			throw new FastRuntimeException("问题类型不合法");
		}

	}

	private WFRunExecutionDTO buildExecutionDTO() {
		WFRunExecutionDTO executionDTO = new WFRunExecutionDTO();
		List<InAuthCrmStaff> staffs = inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).eq(InAuthCrmStaff::getLoginName, "百融").eq(InAuthCrmStaff::getIsStatus, 1));
		if (CollectionUtils.isNotEmpty(staffs)) {
			InAuthCrmStaff staff = staffs.get(0);
			executionDTO.setCurrStaffId(String.valueOf(staff.getId()));
			executionDTO.setCurrGroupCode(staff.getGroupCode());
			executionDTO.setCurrStaffName(staff.getStaffName());
		}
		return executionDTO;
	}

	private List<WorkOrderLoanReqDTO> buildLoanDetail(List<String> applicationIdList) {
		List<WorkOrderLoanReqDTO> loanList = new ArrayList<>();
		for (String applicationId : applicationIdList) {
			WorkOrderLoanReqDTO loanReqDTO = new WorkOrderLoanReqDTO();
			if (applicationId.startsWith("CA")) {
				buildWorkOrderLoanReqDTOWithCA(applicationId, loanReqDTO);
				loanList.add(loanReqDTO);
			} else {
				buildWorkOrderLoanReqDTO(applicationId, loanReqDTO);
				loanList.add(loanReqDTO);

			}
		}
		return loanList;

	}

	private void buildWorkOrderLoanReqDTO(String applicationId, WorkOrderLoanReqDTO loanReqDTO) {
		LoanApplicationDTO loanApplicationDTO = loanApplicationServiceFacade.getLoanApplicationByApplicationId(applicationId);
		if (Objects.isNull(loanApplicationDTO)) {
			loanReqDTO.setApplicationId(applicationId);
			return;
		}
		loanReqDTO.setApplicationId(applicationId);
		ProductDTO product = productServiceFacade.getProductById(loanApplicationDTO.getWelabProductId());
		if (Objects.nonNull(product)) {
			loanReqDTO.setProductName(product.getName());
			loanReqDTO.setApplyTime(loanApplicationDTO.getAppliedAt());
			loanReqDTO.setApprovalTime(loanApplicationDTO.getApprovedAt());
			LoanVO loanVO = loanApplicationService.findLoanByAppId(applicationId);
			if (Objects.nonNull(loanVO)) {
				loanReqDTO.setConfirmedTime(loanVO.getConfirmedAt());
				loanReqDTO.setLoanTime(loanVO.getDisbursedAt());
			}
			loanReqDTO.setChannelCode(loanApplicationDTO.getOrigin());
			loanReqDTO.setPartnerCodeNew(loanApplicationDTO.getPartnerCode());
			loanReqDTO.setPartnerCode(loanApplicationService.getPartnerName(loanApplicationDTO.getPartnerCode()));
			loanReqDTO.setApplyAmount(loanApplicationDTO.getAppliedAmount());
			loanReqDTO.setApprovalAmount(loanApplicationDTO.getAmount());
			loanReqDTO.setApplyTenor(loanApplicationDTO.getAppliedTenor());
			loanReqDTO.setApprovalTenor(loanApplicationDTO.getTenor());
			LoanApplicationStateEnum stateEnum = LoanApplicationStateEnum.getStateEnum(loanApplicationDTO.getState());
			if (Objects.nonNull(stateEnum)) {
				loanReqDTO.setStatus(stateEnum.getText());
			}
			loanReqDTO.setTotalRate(String.valueOf(loanApplicationDTO.getTotalRate()));
			WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(applicationId);
			if (Objects.nonNull(writeoffDTO)) {
				loanReqDTO.setTransferCompany(writeoffDTO.getCompanyName());
			}
		}

	}

	private void buildWorkOrderLoanReqDTOWithCA(String applicationId, WorkOrderLoanReqDTO loanReqDTO) {
		CreditApplicationDTO creditApplication = creditApplicationServiceFacade.getCreditApplicationByApplicationId(applicationId);
		loanReqDTO.setApplicationId(applicationId);
		if (Objects.isNull(creditApplication)) {
			return;
		}
		ProductDTO product = productServiceFacade.getProductByCode(creditApplication.getProductCode());
		if (Objects.nonNull(product)) {
			loanReqDTO.setProductName(product.getName());
		}
		loanReqDTO.setApprovalTime(creditApplication.getApprovedAt());
		loanReqDTO.setApplyTime(creditApplication.getAppliedAt());
		LoanApplicationStateEnum stateEnum = LoanApplicationStateEnum.getStateEnum(creditApplication.getState());
		if (Objects.nonNull(stateEnum)) {
			loanReqDTO.setStatus(stateEnum.getText());
		}
		loanReqDTO.setApprovalAmount(creditApplication.getAmount());
		loanReqDTO.setChannelCode(creditApplication.getOrigin());
		loanReqDTO.setPartnerCode("我来贷");
	}

	private WFConfigTransitionDTO buildSubmitGdzTransitionDTO() {
		WFConfigTransitionDTO transitionDTO = new WFConfigTransitionDTO();
		transitionDTO.setTransCode("nwoStartTrWorkOrder");
		transitionDTO.setTransName("开始节点提单投诉组");
		transitionDTO.setTargetNodeCode("nwoWorkOrder");
		transitionDTO.setShowExpr("");
		transitionDTO.setTargetState("process");
		transitionDTO.setBackFlag("0");
		transitionDTO.setShowName("提单工单组");
		return transitionDTO;
	}

	private WFConfigAssignedDTO buildGdzAssignedDTO() {
		WFConfigAssignedDTO assignedDTO = new WFConfigAssignedDTO();
		assignedDTO.setAssignedName("工单组");
		assignedDTO.setAssignedType("role");
		assignedDTO.setAssignedValue("gdz");
		assignedDTO.setAssignedModel("transfer");
		assignedDTO.setStaffId("");
		return assignedDTO;

	}

	private DataCustomer getDataCustomer(Integer userId) {
		List<DataCustomer> dataCustomers = dataCustomerMapper.selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getUserId, userId).eq(DataCustomer::getCustType, USER_TYPE_CASH));
		if (CollectionUtils.isEmpty(dataCustomers)) {
			UserInfo userInfo = userService.getUserInfo(userId);
			DataCustomer dataCustomer = new DataCustomer();
			if (Objects.nonNull(userInfo)) {
				dataCustomer.setMobile(userInfo.getMobile());
				dataCustomer.setUuid(String.valueOf(userInfo.getUuid()));
				dataCustomer.setUserId(Long.valueOf(userInfo.getUserId()));
				dataCustomer.setIsZx(userInfo.getBlocked());
				dataCustomer.setGmtCreate(new Date());
				dataCustomer.setGmtModify(new Date());
			}
			BaseProfile profile = profileService.getBaseProfileByUserId(userId);
			if (Objects.nonNull(profile)) {
				dataCustomer.setCustomerName(profile.getName());
				String gender = "未知";
				Integer result = IDCardUtils.getGenderFromCnid(profile.getCnid());
				if (result == 1) {
					gender = "男";
				} else if (result == 2) {
					gender = "女";
				}
				dataCustomer.setGender(gender);
				dataCustomer.setAge(AppUtil.getAge(profile.getCnid()));
				dataCustomer.setCnid(StringUtil.hideCnid(profile.getCnid()));
			}
			dataCustomerMapper.insert(dataCustomer);
			return dataCustomer;
		} else {
			return dataCustomers.get(0);
		}
	}

	/**
	 * 根据百融订单号查询用户信息
	 *
	 * @param loanOrderIdList 百融提现订单号
	 * @return 百融用户订单信息
	 */
	private BairongAppVO queryUserInfoByBairongOrderNo(List<String> loanOrderIdList) {
		BairongAppVO bairongAppVO = new BairongAppVO();
		List<String> applicationList = new ArrayList<>();
		Integer userId = null;
		for (String loanOrderId : loanOrderIdList) {
			BairongSingleAppVO singleAppVO = queryAppInfoByBairongOrderNo(loanOrderId);
			checkUserIdIsSame(userId, singleAppVO.getUserId());
			userId = singleAppVO.getUserId();
			applicationList.add(singleAppVO.getApplicationId());

			if (Objects.isNull(userId) || CollectionUtils.isEmpty(applicationList)) {
				throw new FastRuntimeException("根据提现订单号查询用户信息失败, loanOrderId:" + loanOrderId);
			}
		}
		bairongAppVO.setUserId(userId);
		bairongAppVO.setApplicationId(applicationList);

		return bairongAppVO;
	}

	private BairongSingleAppVO queryAppInfoByBairongOrderNo(String loanOrderId) {
		EcLoanOrdersDTO loanOrder = loanApplicationServiceFacade.getPartnerLoanApplicationsDTOByLoanOrderIdAndSouce(BAIRONG_SOURCE1, loanOrderId);
		if (Objects.isNull(loanOrder)) {
			EcLoanOrdersDTO loanOrder2 = loanApplicationServiceFacade.getPartnerLoanApplicationsDTOByLoanOrderIdAndSouce(BAIRONG_SOURCE2, loanOrderId);
			if (Objects.isNull(loanOrder2)) {
				EcOrderDTO ecOrderDTO = loanApplicationServiceFacade.getOrder(BAIRONG_SOURCE2, loanOrderId);
				if (Objects.isNull(ecOrderDTO)) {
					throw new FastRuntimeException("根据提现订单号查询用户信息失败, loanOrderId:" + loanOrderId);
				} else {
					BairongSingleAppVO singleAppVO = new BairongSingleAppVO();
					singleAppVO.setUserId(ecOrderDTO.getUserId());
					singleAppVO.setApplicationId(ecOrderDTO.getApplicationId());
					return singleAppVO;
				}
			} else {
				BairongSingleAppVO singleAppVO = new BairongSingleAppVO();
				singleAppVO.setUserId(loanOrder2.getUserId());
				singleAppVO.setApplicationId(loanOrder2.getApplicationId());
				return singleAppVO;
			}
		} else {
			BairongSingleAppVO singleAppVO = new BairongSingleAppVO();
			singleAppVO.setUserId(loanOrder.getUserId());
			singleAppVO.setApplicationId(loanOrder.getApplicationId());
			return singleAppVO;
		}

	}

	private void checkUserIdIsSame(Integer userId, Integer orderUserId) {
		if (Objects.nonNull(userId) && !userId.equals(orderUserId)) {
			throw new FastRuntimeException("根据提现订单号查询用户信息失败, 用户信息不一致");
		}
	}
}
	
	
	

