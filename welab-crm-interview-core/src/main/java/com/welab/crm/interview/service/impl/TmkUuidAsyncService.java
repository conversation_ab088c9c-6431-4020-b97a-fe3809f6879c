package com.welab.crm.interview.service.impl;

import com.alibaba.dubbo.common.utils.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.keygen.KeyGenerator;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.IDCardUtils;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.ai.AiCustomerDTO;
import com.welab.crm.interview.dto.ai.AiPushDTO;
import com.welab.crm.interview.dto.tmk.PushUuidDTO;
import com.welab.crm.interview.dto.tmk.UuidDTO;
import com.welab.crm.interview.mapper.TmkInterceptHistoryMapper;
import com.welab.crm.interview.mapper.TmkInterceptRuleMapper;
import com.welab.crm.interview.mapper.TmkUuidConfigMapper;
import com.welab.crm.interview.mapper.TmkUuidMapper;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.interview.service.TmkUuidSaveService;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.IvrMessageUtil;
import com.welab.crm.interview.vo.PersonalBasicVo;
import com.welab.usercenter.enums.EnumAddressType;
import com.welab.usercenter.model.base.ApplicantInfo;
import com.welab.usercenter.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description: 电销UUID任务异步执行服务
 * @date 2022/2/23 15:42
 */
@Slf4j
@Service
public class TmkUuidAsyncService {

    @Resource
    private UserService userService;
    @Resource
    private LoansApplicationService loansApplicationService;
    @Resource
    private KeyGenerator keyGenerator;
    @Resource
    private TmkUuidConfigMapper configMapper;
    @Resource
    private TmkUuidTempService uuidTempService;
    @Resource
    private TmkUuidSaveService tmkUuidSaveService;
    @Resource
    private TmkUuidMapper tmkUuidMapper;
    @Resource
    private TmkInterceptHistoryMapper interceptHistoryMapper;
    @Resource
    private TmkInterceptRuleMapper tmkInterceptRuleMapper;

    @Value("${ai.sale.push.url}")
    private String pushUrl;

    @PostConstruct
    public void initExecutor() {
        executor.allowCoreThreadTimeOut(true);
    }

    private static final int BATCH_SIZE = 200;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(20,
            20, 10L, TimeUnit.MINUTES, new ArrayBlockingQueue<>(3000)
            , new NamedThreadFactory("TmkUuidAsyncService"));

    private static final ThreadPoolExecutor singleExecutor = new ThreadPoolExecutor(1,
            1, 0, TimeUnit.MINUTES, new ArrayBlockingQueue<>(100000)
            , new NamedThreadFactory("TmkUuidAsyncServiceSingle"));

    void insertUuidData(PushUuidDTO pushUuidDTO) {
        log.info("insertUuidData 开始多线程分批次保存数据, callType:{}, numPackageId:{}, 数据量:{}",
                pushUuidDTO.getCallType(), pushUuidDTO.getNumPackageId(), pushUuidDTO.getUuid().size());
        // 校验对应的外呼任务配置表数据
        TmkUuidConfig config = getTmkUuidConfig(pushUuidDTO);
        if (config == null) {
            log.error("insertUuidData 外呼参数错误,不存在的配置: callType-{},numPkgId:{}",
                    pushUuidDTO.getCallType(), pushUuidDTO.getNumPackageId());
            return;
        }
        List<UuidDTO> uuidList = pushUuidDTO.getUuid();
        if (CollectionUtils.isNotEmpty(uuidList)) {
            for (int i = 0; i < uuidList.size(); i += BATCH_SIZE) {
                int end = Math.min(i + BATCH_SIZE, uuidList.size());
                List<UuidDTO> partUuidList = uuidList.subList(i, end);
                executor.execute(() -> saveOrAiCallUuidList(pushUuidDTO, partUuidList, config));
            }
        }
        log.info("insertUuidData 完成多线程分批次保存数据, callType:{}, numPackageId:{}", pushUuidDTO.getCallType(),
                pushUuidDTO.getNumPackageId());
    }

    private TmkUuidConfig getTmkUuidConfig(PushUuidDTO pushUuidDTO) {
        LambdaQueryWrapper<TmkUuidConfig> w = Wrappers.<TmkUuidConfig>lambdaQuery()
                .eq(TmkUuidConfig::getCallType, pushUuidDTO.getCallType())
                .eq(TmkUuidConfig::getNumPackageId, pushUuidDTO.getNumPackageId())
                .eq(TmkUuidConfig::getDeleteFlag, Boolean.FALSE);
        return configMapper.selectOne(w);
    }

    public TmkUuidConfig getTmkUuidConfigById(Long configId) {
        LambdaQueryWrapper<TmkUuidConfig> w = Wrappers.<TmkUuidConfig>lambdaQuery()
                .eq(TmkUuidConfig::getConfigId, configId);
        return configMapper.selectOne(w);
    }

    private void saveOrAiCallUuidList(PushUuidDTO pushUuidDTO, List<UuidDTO> partUuidList, TmkUuidConfig config) {
        List<TmkUuid> insertList = new ArrayList<>(partUuidList.size());
        List<AiCustomerDTO> callList = new ArrayList<>(partUuidList.size());
        for (UuidDTO uuidDTO : partUuidList) {
            // 1.新用户做新增操作
            ApplicantInfo applicantInfo;
            try {
                applicantInfo = userService.selectApplicantInfoByUuid(uuidDTO.getUuid(), EnumAddressType.PROFILE_ADDRESSES_TYPE.getValue(), EnumAddressType.RESIDENT_LOCATION_TYPE.getValue());
            } catch (Exception e) {
                log.warn("saveOrAiCallUuidList selectApplicantInfoByUuid exception: uuid-{}, numPkgId-{}, callType-{}",
                        uuidDTO.getUuid(), pushUuidDTO.getNumPackageId(), pushUuidDTO.getCallType());
                continue;
            }

            if (null != applicantInfo) {
                TmkUuid record = constructInsertTmkUuid(uuidDTO, applicantInfo, pushUuidDTO);
                insertList.add(record);
                // 2.是ai外呼配置类型则需准备数据外呼
                if (isAiConfig(pushUuidDTO.getCallType())) {
                    AiCustomerDTO aiCustomerDTO = constructAiCustomerDTO(record, config, uuidDTO);
                    callList.add(aiCustomerDTO);
                }
            } else {
                log.info("saveOrAiCallUuidList selectApplicantInfoByUuid null: uuid-{}, numPkgId-{}, callType-{}",
                        uuidDTO, pushUuidDTO.getNumPackageId(), pushUuidDTO.getCallType());
            }
        }

        try {
            if (CollectionUtils.isNotEmpty(insertList)) {
                log.info("saveOrAiCallUuidList 本批次callType:{}, numPackageId:{},新增数据量{}",
                        pushUuidDTO.getCallType(), pushUuidDTO.getNumPackageId(), insertList.size());
                try {
                    if (CollectionUtils.isNotEmpty(callList)) {
                        // 通过welab_voice_ivr推送到创研
                        String errMsg = pushCallToIvr(config, callList);
                        if (errMsg != null) {
                            log.warn("saveOrAiCallUuidList pushDataToIvr return error: {}", errMsg);
                            resetAiPushFlag(insertList);
                        }
                    }
                    if (isAiConfig(pushUuidDTO.getCallType())) {
                        // ai外呼的直接保存即可
                        tmkUuidSaveService.saveBatch(insertList);
                    } else {
                        // 人工外呼的需要做去重处理
                        filterAndSaveBatch(insertList);
                    }
                } catch (Exception e) {
                    log.warn("saveOrAiCallUuidList PushToIvr call error：{}", e.getMessage(), e);
                    resetAiPushFlag(insertList);
                    tmkUuidSaveService.saveBatch(insertList);
                }
            }
        } catch (Exception e) {
            log.warn("insertUuidData saveOrAiCallUuidList 未知异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 单线程处理人工保存数据，实际效果相当于做串行化处理，目的是防止任一uuid重复
     */
    private void filterAndSaveBatch(List<TmkUuid> originInsertList) {
        TmkInterceptRule interceptRule = getUuidInterceptRule();
        if (interceptRule == null) {
            // 未配置拦截规则直接保存
            tmkUuidSaveService.saveBatch(originInsertList);
            return;
        }

        final int times = Integer.parseInt(interceptRule.getRemark());
        final int days = interceptRule.getValidDays();
        singleExecutor.execute(() -> {
            List<TmkUuid> uuidList = new ArrayList<>(originInsertList.size());
            List<TmkUuidTemp> uuidTempList = new ArrayList<>();
            for (TmkUuid tmkUuid : originInsertList) {
                List<TmkUuid> existList = listExistUsers(tmkUuid.getUserId());
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime beginTime = endTime.minusDays(days);
                // 拦截规则: 拦截天数内已推送次数满足则做拦截操作，例如15天内只能推送一次
                int existTimes = 0;
                for (TmkUuid existUuid : existList) {
                    LocalDateTime realTime = existUuid.getGmtCreate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    if (realTime.isAfter(beginTime) && realTime.isBefore(endTime)) {
                        existTimes++;
                    }
                }
                if (existTimes >= times) {
                    TmkUuidTemp temp = new TmkUuidTemp();
                    BeanUtils.copyProperties(tmkUuid, temp);
                    temp.setRuleId(interceptRule.getId());
                    uuidTempList.add(temp);
                } else {
                    // 保存数据到uuid主表
                    uuidList.add(tmkUuid);
                }
            }
            if (CollectionUtils.isNotEmpty(uuidList)) {
                // 批量保存uuid主数据到表中
                tmkUuidSaveService.saveBatch(uuidList);
            }
            if (CollectionUtils.isNotEmpty(uuidTempList)) {
                // 保存剔除数据到临时表中并且更新uuid的拦截统计数据
                saveInterceptDataAndUpdateCount(uuidTempList, interceptRule.getId());
            }
        });
    }

    private TmkInterceptRule getUuidInterceptRule() {
        LambdaQueryWrapper<TmkInterceptRule> wrapper = Wrappers.<TmkInterceptRule>lambdaQuery()
                .eq(TmkInterceptRule::getStatus, "1")
                .eq(TmkInterceptRule::getTmkType, "uuid");
        return tmkInterceptRuleMapper.selectOne(wrapper);
    }

    private void saveInterceptDataAndUpdateCount(List<TmkUuidTemp> uuidTempList, Long interceptRuleId) {
        uuidTempService.saveBatch(uuidTempList);
        TmkInterceptHistory his = interceptHistoryMapper.selectOne(Wrappers.<TmkInterceptHistory>lambdaQuery()
                .eq(TmkInterceptHistory::getInterceptDate, DateUtil.getTodayDate(0, 0, 0))
                .eq(TmkInterceptHistory::getRuleId, interceptRuleId));
        if (his != null) {
            //做更新操作
            his.setGmtModify(new Date());
            his.setNum(uuidTempList.size() + his.getNum());
            tmkUuidSaveService.updateInterceptHistoryById(his);
        } else {
            //做插入操作
            TmkInterceptHistory history = new TmkInterceptHistory();
            history.setRuleId(interceptRuleId);
            Date date = new Date();
            history.setGmtCreate(date);
            history.setGmtModify(date);
            history.setInterceptDate(DateUtil.getTodayDate(0, 0, 0));
            history.setNum(uuidTempList.size());
            tmkUuidSaveService.insertInterceptHistory(history);
        }
    }

    public String pushCallToIvr(TmkUuidConfig config, List<AiCustomerDTO> callList) throws Exception {
        AiPushDTO aiPushDTO = getAiPushDTO(config);
        aiPushDTO.setCustomers(callList);
        long beginTime = System.currentTimeMillis();
        String requestBody = JSON.toJSONString(aiPushDTO);
        log.info("saveOrCallUuidList pushDataToIvr num:{}, param:{}", callList.size(), requestBody);
        String postResult = HttpClientUtil.doPost(pushUrl, requestBody);
        log.info("saveOrCallUuidList pushDataToIvr spend time:{}ms", System.currentTimeMillis() - beginTime);
        return IvrMessageUtil.getMessageResult(postResult);
    }

    private void resetAiPushFlag(List<TmkUuid> insertList) {
        for (TmkUuid tmkUuid : insertList) {
            tmkUuid.setAiPushFlag(Boolean.FALSE);
        }
    }

    private AiCustomerDTO constructAiCustomerDTO(TmkUuid tmkUuid, TmkUuidConfig config, UuidDTO uuidDTO) {
        AiCustomerDTO customerDTO = new AiCustomerDTO();
        if (StringUtils.isBlank(tmkUuid.getUsername())) {
            customerDTO.setName("尊敬的我来数科用户");
        } else {
            customerDTO.setName(tmkUuid.getUsername());
        }
        if (StringUtils.isBlank(tmkUuid.getGender())) {
            customerDTO.setSex("");
        } else if ("1".equals(tmkUuid.getGender())) {
            customerDTO.setSex("先生");
        } else {
            customerDTO.setSex("女士");
        }
        customerDTO.setSex(tmkUuid.getGender());
        customerDTO.setPhone(tmkUuid.getMobile());
        customerDTO.setAiPushParams(uuidDTO.getAiPushParams());
        AiTmkUuidCallback callback = new AiTmkUuidCallback();
        callback.setConfigId(config.getConfigId());
        callback.setTmkTaskId(tmkUuid.getTmkTaskId());
        callback.setCreateUser("oss");
        customerDTO.setExtraParam(JSON.toJSONString(callback));
        return customerDTO;
    }

    private AiPushDTO getAiPushDTO(TmkUuidConfig config) {
        AiPushDTO dto = new AiPushDTO();
        dto.setSpeechId(String.valueOf(config.getSpeechId()));
        dto.setTaskId(String.valueOf(config.getTaskId()));
        dto.setChannelType("ZY");
        return dto;
    }

    private TmkUuid constructInsertTmkUuid(UuidDTO uuidDTO, ApplicantInfo applicantInfo, PushUuidDTO pushUuidDTO) {
        TmkUuid record = new TmkUuid();
        record.setUuid(uuidDTO.getUuid().toString());
        record.setTmkTaskId("uuid" + keyGenerator.generateKey());
        record.setUserId(applicantInfo.getUserId());
        record.setUsername(applicantInfo.getName());
        record.setMobile(applicantInfo.getMobile());
        record.setCnid(applicantInfo.getCnid());
        if (StringUtils.isNotEmpty(applicantInfo.getCnid())) {
            record.setGender(IDCardUtils.getGenderFromCnid(applicantInfo.getCnid()).toString());
        }
        record.setType("tx");
        record.setIsIncome("0");
        record.setIsWithdrawal("0");
        Date date = new Date();
        record.setGmtCreate(date);
        record.setGmtModify(date);
        record.setPackageCreateTime(date);
        record.setFlag("0");
        // ai外呼直接设置为1，其后推送失败设置为0
        if (isAiConfig(pushUuidDTO.getCallType())) {
            record.setAiPushFlag(Boolean.TRUE);
            record.setAiPushDate(date);
            record.setAiPushTime(date);
        } else {
            record.setAiPushFlag(Boolean.FALSE);
        }
        // 号码包定义
        record.setPackageDefine(pushUuidDTO.getPackageDefine());
        record.setNumPackageId(pushUuidDTO.getNumPackageId());
        record.setCallType(pushUuidDTO.getCallType());
        record.setLabelName(uuidDTO.getLabelName());

        // 设置额度信息
        setCreditLineMessage(record);
        return record;
    }

    private boolean isAiConfig(String callType) {
        return "ai".equals(callType);
    }

    private List<TmkUuid> listExistUsers(Integer userId) {
        return tmkUuidMapper.selectNeedFilterUuid(userId);
    }

    private void setCreditLineMessage(TmkUuid record) {
        PersonalBasicVo userQuota = loansApplicationService.getUserQuota(Long.valueOf(record.getUuid()));
        record.setAvlCredit(new BigDecimal(userQuota.getAvailableCredit()));
        record.setCreditLine(new BigDecimal(userQuota.getCreditLine()));
        record.setCreditStatus(userQuota.getState());
    }
}