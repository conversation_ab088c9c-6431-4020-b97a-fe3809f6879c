package com.welab.crm.interview.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.config.AliyunQualityCheckConfig;
import com.welab.crm.interview.dto.QualityInspectionRequestDTO;
import com.welab.crm.interview.dto.QualityInspectionResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云质检客户端工具类
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Slf4j
@Component
public class AliyunQualityCheckClient {

    @Autowired
    private AliyunQualityCheckConfig config;

    /**
     * 上传音频质检数据
     * @param requestDTO 请求参数
     * @return 任务ID
     */
    public String uploadAudioData(QualityInspectionRequestDTO requestDTO) {
        try {
            if (!config.getEnabled()) {
                log.info("阿里云质检功能已禁用");
                return null;
            }

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("autoSplit", requestDTO.getAutoSplit());
            params.put("serviceChannelKeywords", requestDTO.getServiceChannelKeywords());
            params.put("serviceChannel", requestDTO.getServiceChannel());
            params.put("clientChannel", requestDTO.getClientChannel());
            params.put("sampleRate", requestDTO.getSampleRate());
            params.put("callbackUrl", requestDTO.getCallbackUrl());
            params.put("callList", requestDTO.getCallList());

            String jsonStr = JSON.toJSONString(params);
            log.info("阿里云质检上传参数: {}", jsonStr);

            // 调用阿里云SDK
            String response = callAliyunApi("UploadAudioData", jsonStr, requestDTO.getBaseMeAgentId());
            log.info("阿里云质检上传响应: {}", response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getBoolean("Success") && "200".equals(responseJson.getString("Code"))) {
                return responseJson.getString("Data");
            } else {
                log.error("阿里云质检上传失败: {}", response);
                return null;
            }
        } catch (Exception e) {
            log.error("阿里云质检上传异常", e);
            return null;
        }
    }

    /**
     * 获取质检结果
     * @param taskId 任务ID
     * @param baseMeAgentId 业务空间ID
     * @return 质检结果
     */
    public QualityInspectionResultDTO getResult(String taskId, Long baseMeAgentId) {
        try {
            if (!config.getEnabled()) {
                log.info("阿里云质检功能已禁用");
                return null;
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", taskId);
            params.put("requiredFields", "taskId,status,score,errorMessage,asrResult,hitResult,recording,agent");

            String jsonStr = JSON.toJSONString(params);
            log.info("阿里云质检结果查询参数: taskId={}, params={}", taskId, jsonStr);

            // 调用阿里云SDK
            String response = callAliyunApi("GetResult", jsonStr, baseMeAgentId);
            log.info("阿里云质检结果响应: taskId={}, response={}", taskId, response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getBoolean("Success") && "200".equals(responseJson.getString("Code"))) {
                JSONObject data = responseJson.getJSONObject("Data");
                if (data != null && data.getJSONArray("ResultInfo") != null 
                    && !data.getJSONArray("ResultInfo").isEmpty()) {
                    
                    JSONObject resultInfo = data.getJSONArray("ResultInfo").getJSONObject(0);
                    return parseQualityResult(resultInfo);
                }
            } else {
                log.error("阿里云质检结果获取失败: taskId={}, response={}", taskId, response);
            }
            return null;
        } catch (Exception e) {
            log.error("阿里云质检结果获取异常: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 调用阿里云API（这里使用模拟实现，实际应使用阿里云SDK）
     * @param action API动作
     * @param jsonStr JSON参数
     * @param baseMeAgentId 业务空间ID
     * @return 响应结果
     */
    private String callAliyunApi(String action, String jsonStr, Long baseMeAgentId) {
        // TODO: 这里应该使用真正的阿里云SDK调用
        // 由于项目可能没有引入阿里云SDK依赖，这里提供一个模拟实现
        // 实际使用时需要添加阿里云SDK依赖并使用真正的API调用
        
        log.warn("使用模拟的阿里云API调用，实际部署时需要替换为真正的SDK调用");
        
        // 模拟成功响应
        if ("UploadAudioData".equals(action)) {
            return "{\"Success\":true,\"Code\":\"200\",\"Message\":\"successful\",\"Data\":\"mock-task-id-" + System.currentTimeMillis() + "\"}";
        } else if ("GetResult".equals(action)) {
            return "{\"Success\":true,\"Code\":\"200\",\"Message\":\"successful\",\"Data\":{\"ResultInfo\":[{\"TaskId\":\"mock-task-id\",\"Status\":1,\"Score\":85,\"ErrorMessage\":\"\"}]}}";
        }
        
        return "{\"Success\":false,\"Code\":\"500\",\"Message\":\"Unknown action\"}";
    }

    /**
     * 解析质检结果
     * @param resultInfo 结果信息
     * @return 质检结果DTO
     */
    private QualityInspectionResultDTO parseQualityResult(JSONObject resultInfo) {
        QualityInspectionResultDTO result = new QualityInspectionResultDTO();
        
        result.setTaskId(resultInfo.getString("TaskId"));
        result.setStatus(resultInfo.getInteger("Status"));
        result.setScore(resultInfo.getInteger("Score"));
        result.setCreateTime(resultInfo.getString("CreateTime"));
        result.setReviewStatus(resultInfo.getInteger("ReviewStatus"));
        result.setReviewResult(resultInfo.getInteger("ReviewResult"));
        result.setComments(resultInfo.getString("Comments"));
        result.setErrorMessage(resultInfo.getString("ErrorMessage"));

        // 解析转写结果
        if (resultInfo.containsKey("AsrResult")) {
            // TODO: 解析AsrResult
        }

        // 解析质检命中结果
        if (resultInfo.containsKey("HitResult")) {
            // TODO: 解析HitResult
        }

        // 解析录音信息
        if (resultInfo.containsKey("Recording")) {
            // TODO: 解析Recording
        }

        // 解析客服信息
        if (resultInfo.containsKey("Agent")) {
            // TODO: 解析Agent
        }

        return result;
    }

    /**
     * 检查配置是否有效
     * @return 是否有效
     */
    public boolean isConfigValid() {
        return config.getEnabled() 
            && config.getAccessKeyId() != null 
            && config.getAccessKeySecret() != null
            && config.getCallbackUrl() != null;
    }
}
