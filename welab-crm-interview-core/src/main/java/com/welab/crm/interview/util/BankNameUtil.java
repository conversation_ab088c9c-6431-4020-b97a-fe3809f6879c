package com.welab.crm.interview.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class BankNameUtil {

    public static String getBankName(String account) {
        if (StringUtils.isNotBlank(account) && account.length() > 6) {
            for (int i = 0; i < bankBin.length; i++) {
                if (String.valueOf(bankBin[i]).equals(account.substring(0, 6))) {
                    return bankName[i];
                }
            }
        }
        return "";
    }

    private final static int[] bankBin = {102033, 103000, 185720, 303781,
        356827, 356828, 356833, 356835, 356837, 356838, 356839, 356840,
        356885, 356886, 356887, 356888, 356889, 356890, 370246, 370247,
        370248, 370249, 400360, 400937, 400938, 400939, 400940, 400941,
        400942, 402658, 402673, 402791, 403361, 403391, 404117, 404157,
        404171, 404172, 404173, 404174, 404738, 404739, 405512, 405512,
        406252, 406254, 406365, 407405, 409665, 409666, 409667, 409668,
        409669, 409670, 409671, 409672, 410062, 412962, 412963, 415599,
        421317, 421349, 421393, 421437, 421865, 421869, 421870, 421871,
        422160, 422161, 424106, 424107, 424108, 424109, 424110, 424111,
        424902, 425862, 427010, 427018, 427019, 427020, 427028, 427029,
        427038, 427039, 427062, 427064, 427571, 428911, 431502, 431502,
        433666, 433670, 433680, 434061, 434062, 435744, 435745, 436718,
        436728, 436738, 436742, 436745, 436748, 436768, 438088, 438125,
        438126, 438588, 438589, 438600, 439188, 439225, 439227, 442729,
        442730, 451289, 451291, 451804, 451804, 451810, 451810, 453242,
        456351, 456418, 458060, 458060, 458071, 458071, 458123, 458124,
        468203, 472067, 472068, 479228, 479229, 481699, 486466, 486493,
        486494, 486497, 487013, 489592, 489734, 489735, 489736, 491020,
        491020, 491031, 491032, 491040, 493427, 493878, 498451, 504923,
        510529, 512315, 512316, 512411, 512412, 512425, 512431, 512466,
        512695, 512732, 514906, 514957, 514958, 517636, 518212, 518364,
        518378, 518379, 518474, 518475, 518476, 518710, 518718, 519412,
        519498, 520082, 520108, 520131, 520152, 520169, 520194, 520382,
        521899, 522153, 523036, 524011, 524047, 524070, 524091, 524094,
        524864, 524865, 525498, 525745, 525746, 526410, 526855, 527414,
        528020, 528931, 528948, 530970, 530980, 530980, 530990, 532420,
        532430, 532450, 532458, 535910, 535910, 535918, 537830, 540297,
        540838, 541068, 541709, 543159, 544033, 545619, 545623, 545947,
        547628, 547648, 547766, 547766, 548259, 548844, 552245, 552288,
        552534, 552587, 552599, 552742, 552794, 552801, 552853, 553131,
        553242, 556610, 556617, 558360, 558730, 558808, 558809, 558868,
        558868, 558894, 558895, 558916, 566666, 584016, 601100, 601101,
        601121, 601122, 601123, 601124, 601125, 601126, 601127, 601128,
        601131, 601136, 601137, 601138, 601140, 601142, 601143, 601144,
        601145, 601146, 601147, 601148, 601149, 601174, 601177, 601178,
        601179, 601186, 601187, 601188, 601189, 601382, 601382, 601428,
        601428, 601428, 601428, 602907, 602907, 602969, 602969, 603128,
        603128, 603367, 603367, 603445, 603445, 603506, 603506, 603601,
        603601, 603601, 603601, 603601, 603601, 603602, 603602, 603694,
        603694, 603708, 603708, 621021, 621201, 621977, 621977, 622126,
        622126, 622127, 622127, 622127, 622127, 622128, 622128, 622129,
        622129, 622131, 622131, 622132, 622132, 622133, 622133, 622134,
        622134, 622135, 622135, 622136, 622136, 622137, 622137, 622138,
        622138, 622139, 622139, 622140, 622140, 622141, 622141, 622143,
        622143, 622146, 622146, 622147, 622147, 622148, 622148, 622149,
        622149, 622150, 622150, 622151, 622151, 622152, 622152, 622153,
        622153, 622154, 622154, 622155, 622155, 622156, 622156, 622165,
        622165, 622166, 622166, 622168, 622168, 622169, 622169, 622178,
        622178, 622179, 622179, 622184, 622184, 622188, 622188, 622199,
        622199, 622200, 622200, 622202, 622202, 622203, 622203, 622208,
        622208, 622210, 622210, 622211, 622211, 622212, 622212, 622213,
        622213, 622214, 622214, 622215, 622215, 622220, 622220, 622225,
        622225, 622230, 622230, 622235, 622235, 622240, 622240, 622245,
        622245, 622250, 622250, 622251, 622251, 622252, 622252, 622253,
        622253, 622254, 622254, 622258, 622258, 622259, 622259, 622260,
        622260, 622261, 622261, 622280, 622280, 622291, 622291, 622292,
        622292, 622301, 622301, 622302, 622302, 622303, 622303, 622305,
        622305, 622307, 622307, 622308, 622308, 622310, 622310, 622311,
        622311, 622312, 622312, 622316, 622316, 622318, 622318, 622319,
        622319, 622321, 622321, 622322, 622322, 622323, 622323, 622324,
        622324, 622325, 622325, 622327, 622327, 622328, 622328, 622329,
        622329, 622331, 622331, 622332, 622332, 622333, 622333, 622335,
        622335, 622336, 622336, 622337, 622337, 622338, 622338, 622339,
        622339, 622340, 622340, 622341, 622341, 622342, 622342, 622343,
        622343, 622345, 622345, 622346, 622346, 622347, 622347, 622348,
        622348, 622349, 622349, 622350, 622350, 622351, 622351, 622352,
        622352, 622353, 622353, 622355, 622355, 622358, 622358, 622359,
        622359, 622360, 622360, 622361, 622361, 622362, 622362, 622363,
        622363, 622365, 622365, 622366, 622366, 622367, 622367, 622368,
        622368, 622369, 622369, 622370, 622370, 622371, 622371, 622373,
        622373, 622375, 622375, 622376, 622376, 622377, 622377, 622378,
        622378, 622379, 622379, 622382, 622382, 622383, 622383, 622384,
        622384, 622385, 622385, 622386, 622386, 622387, 622387, 622388,
        622388, 622389, 622389, 622391, 622391, 622392, 622392, 622393,
        622393, 622394, 622394, 622395, 622395, 622396, 622396, 622397,
        622397, 622398, 622399, 622399, 622400, 622400, 622406, 622406,
        622407, 622407, 622411, 622411, 622412, 622412, 622413, 622413,
        622415, 622415, 622418, 622418, 622420, 622420, 622421, 622421,
        622422, 622422, 622423, 622423, 622425, 622425, 622426, 622426,
        622427, 622427, 622428, 622428, 622429, 622429, 622432, 622432,
        622434, 622434, 622435, 622435, 622436, 622436, 622439, 622439,
        622440, 622440, 622441, 622441, 622442, 622442, 622443, 622443,
        622447, 622447, 622448, 622448, 622449, 622449, 622450, 622450,
        622451, 622451, 622452, 622452, 622453, 622453, 622456, 622456,
        622459, 622459, 622462, 622462, 622463, 622463, 622466, 622466,
        622467, 622467, 622468, 622468, 622470, 622470, 622471, 622471,
        622472, 622472, 622476, 622476, 622477, 622477, 622478, 622478,
        622481, 622481, 622485, 622485, 622486, 622486, 622487, 622487,
        622487, 622487, 622488, 622488, 622489, 622489, 622490, 622490,
        622490, 622490, 622491, 622491, 622491, 622491, 622492, 622492,
        622492, 622492, 622493, 622493, 622495, 622495, 622496, 622496,
        622498, 622498, 622499, 622499, 622500, 622500, 622506, 622506,
        622509, 622509, 622510, 622510, 622516, 622516, 622517, 622517,
        622518, 622518, 622519, 622519, 622521, 622521, 622522, 622522,
        622523, 622523, 622525, 622525, 622526, 622526, 622538, 622538,
        622546, 622546, 622547, 622547, 622548, 622548, 622549, 622549,
        622550, 622550, 622561, 622561, 622562, 622562, 622563, 622563,
        622575, 622575, 622576, 622576, 622577, 622577, 622578, 622578,
        622579, 622579, 622580, 622580, 622581, 622581, 622582, 622582,
        622588, 622588, 622598, 622598, 622600, 622600, 622601, 622601,
        622602, 622602, 622603, 622603, 622615, 622615, 622617, 622617,
        622619, 622619, 622622, 622622, 622630, 622630, 622631, 622631,
        622632, 622632, 622633, 622633, 622650, 622650, 622655, 622655,
        622658, 622658, 622660, 622660, 622678, 622678, 622679, 622679,
        622680, 622680, 622681, 622681, 622682, 622682, 622684, 622684,
        622688, 622688, 622689, 622689, 622690, 622690, 622691, 622691,
        622692, 622692, 622696, 622696, 622698, 622698, 622700, 622700,
        622725, 622725, 622728, 622728, 622750, 622750, 622751, 622751,
        622752, 622752, 622753, 622753, 622754, 622755, 622755, 622756,
        622756, 622757, 622757, 622758, 622758, 622759, 622759, 622760,
        622760, 622761, 622761, 622762, 622762, 622763, 622763, 622770,
        622770, 622777, 622777, 622821, 622821, 622822, 622822, 622823,
        622823, 622824, 622824, 622825, 622825, 622826, 622826, 622827,
        622836, 622836, 622837, 622837, 622840, 622840, 622841, 622842,
        622843, 622844, 622844, 622845, 622845, 622846, 622846, 622847,
        622847, 622848, 622848, 622849, 622855, 622855, 622856, 622856,
        622857, 622857, 622858, 622858, 622859, 622859, 622860, 622860,
        622861, 622861, 622864, 622864, 622865, 622865, 622866, 622866,
        622867, 622867, 622869, 622869, 622870, 622870, 622871, 622871,
        622877, 622877, 622878, 622878, 622879, 622879, 622880, 622880,
        622881, 622881, 622882, 622882, 622884, 622884, 622885, 622885,
        622886, 622886, 622891, 622891, 622892, 622892, 622893, 622893,
        622895, 622895, 622897, 622897, 622898, 622898, 622900, 622900,
        622901, 622901, 622908, 622908, 622909, 622909, 622940, 622982,
        628218, 628288, 628366, 628368, 650600, 650600, 650700, 650700,
        650800, 650800, 650900, 650900, 682900, 682900, 683970, 683970,
        685800, 685800, 685800, 685800, 685800, 685800, 690755, 690755,
        690755, 690755, 694301, 694301, 695800, 695800, 843010, 843010,
        843360, 843360, 843420, 843420, 843610, 843610, 843730, 843730,
        843800, 843800, 843850, 843850, 843900, 843900, 870000, 870000,
        870100, 870100, 870300, 870300, 870400, 870400, 870500, 870500,
        888000, 888000, 940056, 955880, 955881, 955882, 955888, 984301,
        998800};

    /**
     * 发卡行.卡种名称
     */
    private static final String[] bankName = {"广东发展银行", "农业银行", "昆明农联社", "中国光大银行", "上海银行", "上海银行",
        "中国银行", "中国银行", "中国光大银行", "中国光大银行", "中国光大银行", "中国光大银行", "招商银行", "招商银行", "招商银行",
        "招商银行", "招商银行", "招商银行", "工商银行", "工商银行", "中国工商银行", "中国工商银行", "中信实业银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "招商银行", "上海银行", "工商银行", "农业银行", "中信实业银行",
        "农业银行", "中信实业银行", "中信实业银行", "中信实业银行", "中信实业银行", "中信实业银行", "上海浦东发展银行",
        "上海浦东发展银行", "交通银行", "交通银行", "中国光大银行", "中国光大银行", "广东发展银行", "民生银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "招商银行银行", "深圳发展银行",
        "深圳发展银行", "民生银行", "北京银行", "建设银行", "民生银行", "中信实业银行", "民生银行", "民生银行", "民生银行",
        "民生银行", "北京银行", "北京银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行",
        "中国光大银行", "工商银行", "工商银行", "工商银行", "工商银行", "工商银行", "工商银行", "工商银行", "工商银行", "工商银行",
        "工商银行", "中国民生银行", "广东发展银行", "华夏", "华夏", "中信实业银行", "中信实业银行", "中信实业银行", "建设银行",
        "建设银行", "深圳发展银行", "深圳发展银行", "建设银行", "建设银行", "建设银行", "建设银行", "建设银行", "建设银行",
        "广东发展银行", "中国银行", "工商银行", "中国工商银行", "兴业银行", "兴业银行", "上海银行", "招商银行", "招商银行",
        "招商银行", "中信实业银行", "中信实业银行", "兴业银行", "中国银行", "工商银行", "工商银行", "工商银行", "工商银行",
        "建设银行", "中国银行", "上海浦东发展银行", "工商银行", "工商银行", "工商银行", "工商银行", "交通银行", "交通银行",
        "招商银行", "民生银行", "民生银行", "招商银行", "招商银行", "中国光大银行", "上海银行", "兴业银行", "兴业银行",
        "中国光大银行", "广东发展银行", "中国建设银行", "中国工商银行", "中国工商银行", "中国工商银行", "农业银行", "农业银行",
        "建设银行", "广东发展银行", "交通银行", "广东发展银行", "中国银行", "上海浦东发展银行", "常州商业银行", "工商银行",
        "中国银行", "中国银行", "中国银行", "中国银行", "招商银行", "宁波市商业银行", "民生银行", "中国银行", "中国银行",
        "中信实业银行", "中国银行", "中国银行", "民生银行", "中信实业银行", "广东发展银行", "中国银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "招商银行", "招商银行", "农业银行", "上海银行", "农业银行", "中信实业银行",
        "上海银行", "广东发展银行", "交通银行", "宁波市商业银行", "广东发展银行", "交通银行", "中国银行", "兴业银行", "招商银行",
        "工商银行", "兴业银行", "中国工商银行", "建设银行", "中国银行", "中国银行", "中国工商银行", "中国银行", "中国银行",
        "建设银行", "深圳市商业银行", "兴业银行", "深圳市商业银行", "广东发展银行", "民生银行", "工商银行", "工商银行", "工商银行",
        "工商银行", "建设银行", "建设银行", "建设银行", "建设银行", "农业银行", "农业银行", "农业银行", "交通银行", "中国银行",
        "中国银行", "中国银行", "广东发展银行", "中国光大银行", "建设银行", "招商银行", "招商银行", "招商银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "工商银行", "广东发展银行", "建设银行", "民生银行", "招商银行", "招商银行", "农业银行",
        "中国银行", "广东发展银行", "建设银行", "交通银行", "中国银行", "建设银行", "民生银行", "中信实业银行", "工商银行",
        "农业银行", "中国银行", "中国银行", "中国银行", "中国银行", "广东发展银行", "建设银行", "中信实业银行", "沈阳市商业银行",
        "深圳农联社", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I",
        "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I",
        "D.F.S.I",
        "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I", "D.F.S.I",
        "D.F.S.I",
        "中国银行", "中国银行", "交通银行", "交通银行", "交通银行", "交通银行", "深圳商业银行", "深圳商业银行", "北京银行",
        "北京银行", "南京市商业银行", "南京市商业银行", "杭州商业银行", "杭州商业银行", "广州市商业银行", "广州市商业银行",
        "苏州市商业银行", "苏州市商业银行", "徽商银行合肥分行", "徽商银行合肥分行", "徽商银行合肥分行", "徽商银行合肥分行",
        "徽商银行合肥分行", "徽商银行合肥分行", "绍兴商业银行", "绍兴商业银行", "常熟农村商业银行", "常熟农村商业银行",
        "大连商业银行", "大连商业银行", "河北省农村信用社", "韩亚银行", "温州商业银行", "温州商业银行", "阜新市商业银行",
        "阜新市商业银行", "福建省农村信用社联合社", "厦门市农村信用合作社", "福建省农村信用社联合社", "厦门市农村信用合作社",
        "深圳农信社", "深圳农信社", "深圳市农村信用合作社联合社", "深圳市农村信用合作社联合社", "淮安市商业银行",
        "淮安市商业银行", "嘉兴市商业银行", "嘉兴市商业银行", "贵阳市商业银行", "贵阳市商业银行", "重庆市商业银行",
        "重庆市商业银行", "成都商业银行", "成都商业银行", "西安市商业银行", "西安市商业银行", "徽商银行芜湖分行",
        "徽商银行芜湖分行", "北京农联社", "北京农联社", "兰州市商业银行", "兰州市商业银行", "廊坊市商业银行", "廊坊市商业银行",
        "泰隆城市信用社", "泰隆城市信用社", "乌鲁木齐市商业银行", "乌鲁木齐市商业银行", "青岛商行", "青岛商行", "呼市商业银行",
        "呼市商业银行", "上海银行", "上海银行", "上海银行", "上海银行", "国家邮政局", "国家邮政局", "国家邮政局", "国家邮政局",
        "成都市商业银行", "成都市商业银行", "成都市商业银行", "成都市商业银行", "成都市商业银行", "成都市商业银行",
        "深圳市商业银行", "深圳市商业银行", "深圳市商业银行", "深圳市商业银行", "包头市商业银行", "包头市商业银行",
        "中国建设银行", "中国建设银行", "中国建设银行", "中国建设银行", "湖南省农村信用社联合社", "湖南省农村信用社联合社",
        "吉林市商业银行", "吉林市商业银行", "吉林市商业银行", "吉林市商业银行", "福建省农村信用社联合社", "福建省农村信用社联合社",
        "国家邮政局", "国家邮政局", "国家邮政局", "国家邮政局", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行",
        "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行",
        "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行",
        "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行",
        "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行", "中国工商银行",
        "交通银行股份有限公司太平洋双币信用卡中心", "交行太平洋卡中心", "交通银行股份有限公司太平洋双币信用卡中心",
        "交行太平洋卡中心", "交通银行股份有限公司太平洋双币信用卡中心", "交行太平洋卡中心",
        "交通银行股份有限公司太平洋双币信用卡中心", "交行太平洋卡中心", "交通银行", "交通银行", "交通银行", "交通银行", "交通银行",
        "交通银行", "交通银行", "交通银行", "交通银行", "交通银行", "建设银行", "建设银行", "柳州市商业银行", "柳州市商业银行",
        "柳州市商业银行", "柳州市商业银行", "湖州市商业银行", "湖州市商业银行", "佛山市禅城区农村信用联社",
        "佛山市禅城区农村信用联社", "南京市商业银行", "南京市商业银行", "南京市商业银行", "南京市商业银行", "九江市商业银行",
        "九江市商业银行", "昆明商业银行", "昆明商业银行", "西宁市商业银行", "西宁市商业银行", "淄博市商业银行", "淄博市商业银行",
        "徐州市郊农村信用合作联社", "徐州市郊农村信用合作联社", "宁波市商业银行", "宁波市商业银行", "宁波市商业银行",
        "宁波市商业银行", "山东农村信用联合社", "山东农村信用联合社", "台州市商业银行", "台州市商业银行", "顺德农信社",
        "顺德农信社", "常熟农村商业银行", "常熟农村商业银行", "江苏农信", "江苏农信", "武汉市商业银行", "武汉市商业银行",
        "徽商银行马鞍山分行", "徽商银行马鞍山分行", "东莞农村信用合作社", "东莞农村信用合作社", "天津市农村信用社",
        "天津市农村信用社", "天津市商业银行", "天津市商业银行", "张家港市农村商业银行", "张家港市农村商业银行", "东莞市商业银行",
        "东莞市商业银行", "南宁市商业银行", "南宁市商业银行", "包头市商业银行", "包头市商业银行", "连云港市商业银行",
        "连云港市商业银行", "焦作市商业银行", "焦作市商业银行", "鄞州农村合作银行", "鄞州农村合作银行", "徽商银行淮北分行",
        "徽商银行淮北分行", "江阴农村商业银行", "江阴农村商业银行", "攀枝花市商业银行", "攀枝花市商业银行",
        "佛山市三水区农村信用合作社", "佛山市三水区农村信用合作社", "成都农信社", "成都农信社", "中国银行", "中国银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "南洋商业银行", "南洋商业银行", "南洋商业银行", "南洋商业银行", "南洋商业银行",
        "南洋商业银行", "集友银行", "集友银行", "集友银行", "集友银行", "集友银行", "集友银行", "沧州农信社", "沧州农信社",
        "临沂市商业银行", "临沂市商业银行", "香港上海汇丰银行有限公司", "香港上海汇丰银行有限公司", "香港上海汇丰银行有限公司",
        "香港上海汇丰银行有限公司", "中山市农村信用合作社", "中山市农村信用合作社", "珠海市商业银行", "珠海市商业银行",
        "东亚银行有限公司", "东亚银行有限公司", "徽商银行安庆分行", "徽商银行安庆分行", "绵阳市商业银行", "绵阳市商业银行",
        "长沙市商业银行", "长沙市商业银行", "昆明市农村信用联社", "昆明市农村信用联社", "泉州市商业银行", "泉州市商业银行",
        "花旗银行有限公司", "花旗银行有限公司", "大新银行有限公司", "大新银行有限公司", "大新银行有限公司", "大新银行有限公司",
        "恒生银行有限公司", "恒生银行有限公司", "恒生银行有限公司", "恒生银行有限公司", "恒生银行有限公司", "恒生银行有限公司",
        "济南市商业银行", "济南市商业银行", "美国银行", "美国银行", "大连市商业银行", "大连市商业银行", "恒丰银行", "恒丰银行",
        "大连市商业银行", "大连市商业银行", "上海商业银行", "上海商业银行", "永隆银行有限公司", "永隆银行有限公司",
        "福州市商业银行", "福州市商业银行", "宁波鄞州农村合作银行", "宁波鄞州农村合作银行", "潍坊商业银行", "潍坊商业银行",
        "泸州市商业银行", "泸州市商业银行", "厦门市商业银行", "厦门市商业银行", "镇江市商业银行", "镇江市商业银行",
        "大同市商业银行", "大同市商业银行", "宜昌市商业银行", "宜昌市商业银行", "宜昌市商业银行", "宜昌市商业银行",
        "葫芦岛市商业银行", "辽阳市商业银行", "辽阳市商业银行", "营口市商业银行", "营口市商业银行", "香港上海汇丰银行有限公司",
        "香港上海汇丰银行有限公司", "香港上海汇丰银行有限公司", "香港上海汇丰银行有限公司", "威海市商业银行", "威海市商业银行",
        "湖北农信社", "湖北农信社", "鞍山市商业银行", "鞍山市商业银行", "丹东商行", "丹东商行", "南通市商业银行", "南通市商业银行",
        "洛阳市商业银行", "洛阳市商业银行", "郑州商业银行", "郑州商业银行", "扬州市商业银行", "扬州市商业银行", "永隆银行有限公司",
        "永隆银行有限公司", "哈尔滨市商业银行", "哈尔滨市商业银行", "天津市商业银行", "天津市商业银行", "台州市商业银行",
        "台州市商业银行", "银川市商业银行", "银川市商业银行", "银川市商业银行", "银川市商业银行", "大西洋银行股份有限公司",
        "大西洋银行股份有限公司", "澳门国际银行", "澳门国际银行", "澳门国际银行", "澳门国际银行", "澳门国际银行", "澳门国际银行",
        "广州农村信用合作社联合社", "广州农村信用合作社", "吉林市商业银行", "吉林市商业银行", "三门峡市城市信用社",
        "三门峡市城市信用社", "抚顺市商业银行", "抚顺市商业银行", "昆山农村信用合作社联合社", "昆山农村信用合作社联合社",
        "常州商业银行", "常州商业银行", "湛江市商业银行", "湛江市商业银行", "金华市商业银行", "金华市商业银行", "金华市商业银行",
        "金华市商业银行", "大新银行有限公司", "大新银行有限公司", "江苏农信社", "江苏农信社", "中信嘉华银行有限公司",
        "中信嘉华银行有限公司", "中信嘉华银行有限公司", "中信嘉华银行有限公司", "中信嘉华银行有限公司", "中信嘉华银行有限公司",
        "常熟市农村商业银行", "常熟市农村商业银行", "廖创兴银行有限公司", "廖创兴银行有限公司", "沈阳市商业银行", "沈阳市商业银行",
        "广州市商业银行", "广州市商业银行", "上海银行", "上海银行", "江门市新会农信社", "江门市新会农信社", "东亚银行有限公司",
        "东亚银行有限公司", "东亚银行有限公司", "东亚银行有限公司", "乌鲁木齐市商业银行", "乌鲁木齐市商业银行",
        "高要市农村信用联社", "高要市农村信用联社", "上海市农村信用合作社联合社", "上海市农村信用合作社联社",
        "江阴市农村商业银行", "江阴市农村商业银行", "无锡市商业银行", "无锡市商业银行", "绍兴市商业银行", "绍兴市商业银行",
        "星展银行", "星展银行", "星展银行", "星展银行", "吴江农村商业银行", "吴江农村商业银行", "大新银行有限公司",
        "大新银行有限公司", "星展银行", "星展银行", "星展银行", "星展银行", "星展银行", "星展银行", "星展银行", "星展银行",
        "星展银行", "星展银行", "星展银行", "星展银行", "AEON信贷财务", "AEON信贷财务", "Travelex", "Travelex", "Travelex",
        "Travelex", "石家庄市商业银行", "石家庄市商业银行", "石家庄市商业银行", "石家庄市商业银行", "上海浦东发展银行",
        "上海浦东发展银行", "陕西省农村信用社联合社", "陕西省农村信用社联合社", "高要市农村信用合作社联合社",
        "高要市农村信用合作社联社", "高要市农村信用合作社联合社", "高要市农村信用合作社联社", "上海浦东发展银行",
        "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行",
        "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行", "上海浦东发展银行",
        "上海浦东发展银行", "深圳发展银行", "深圳发展银行", "深圳发展银行", "深圳发展银行", "深圳发展银行", "深圳发展银行",
        "大丰银行有限公司", "大丰银行有限公司", "大丰银行有限公司", "大丰银行有限公司", "大丰银行有限公司", "大丰银行有限公司",
        "哈萨克斯坦国民储蓄银行", "哈萨克斯坦国民储蓄银行", "哈萨克斯坦国民储蓄银行", "哈萨克斯坦国民储蓄银行",
        "德阳市商业银行", "德阳市商业银行", "德阳市商业银行", "德阳市商业银行", "德阳市商业银行", "德阳市商业银行", "招商银行",
        "招商银行银行", "招商银行", "招商银行银行", "招商银行", "招商银行银行", "招商银行", "招商银行银行", "招商银行",
        "招商银行银行", "招商银行", "招商银行银行", "招商银行", "招商银行银行", "招商银行", "招商银行银行", "招商银行",
        "招商银行银行", "招商银行", "招商银行银行", "民生银行", "民生银行", "民生银行", "民生银行", "中国民生银行", "中国民生银行",
        "中国民生银行", "中国民生银行", "民生银行", "民生银行", "中国民生银行", "中国民生银行", "中国民生银行", "中国民生银行",
        "中国民生银行", "中国民生银行", "华夏银行", "华夏银行", "华夏银行", "华夏银行", "华夏银行", "华夏银行", "华夏银行",
        "华夏银行", "中国光大银行", "中国光大银行", "中国光大银行", "中国光大银行", "中国光大银行", "中国光大银行", "光大银行",
        "光大银行", "中信实业银行信用卡中心", "中信实业银行信用卡中心", "中信实业银行信用卡中心", "中信实业银行信用卡中心",
        "中信实业银行信用卡中心", "中信实业银行信用卡中心", "江西省农村信用社联合社", "江西省农村信用社联合社",
        "江西省农村信用社联合社", "江西省农村信用社联合社", "渤海银行", "渤海银行", "中信实业银行信用卡中心",
        "中信实业银行信用卡中心", "中信实业银行信用卡中心", "中信实业银行信用卡中心", "中信实业银行", "中信实业银行",
        "中信实业银行", "中信实业银行", "中信实业银行", "中信实业银行", "中信银行", "中信银行", "中信银行", "中信银行", "建设银行",
        "中国建设银行", "中国建设银行", "中国建设银行", "中国建设银行", "中国建设银行", "中国银行澳门分行", "中国银行澳门分行",
        "中国银行澳门分行", "中国银行澳门分行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行",
        "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行", "中国银行澳门分行", "中国银行澳门分行",
        "曲靖市商业银行", "曲靖市商业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行",
        "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行",
        "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行",
        "中国农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "农业银行", "江苏东吴农村商业银行", "江苏东吴农村商业银行",
        "桂林市商业银行", "桂林市商业银行", "日照市商业银行", "日照市商业银行", "浙江省农村信用社联合社", "浙江省农村信用社联社",
        "珠海农村信用合作社联社", "珠海农村信用合作联社", "大庆市商业银行", "大庆市商业银行", "澳门永亨银行股份有限公司",
        "澳门永亨银行股份有限公司", "莱芜市商业银行", "莱芜市商业银行", "长春市商业银行", "长春市商业银行", "徐州市商业银行",
        "徐州市商业银行", "重庆市农村信用社联合社", "重庆市农村信用社联合社", "太仓农村商业银行", "太仓农村商业银行",
        "靖江市长江城市信用社", "靖江市长江城市信用社", "永亨银行", "永亨银行", "徽商银行", "徽商银行", "杭州市商业银行",
        "杭州市商业银行", "徽商银行", "徽商银行", "柳州市商业银行", "柳州市商业银行", "柳州市商业银行", "柳州市商业银行",
        "尧都区农村信用合作社联社", "尧都区农村信用合作社联社", "渤海银行", "渤海银行", "重庆市农村信用社联合社",
        "重庆市农村信用社联合社", "烟台市商业银行", "烟台市商业银行", "武进农村商业银行", "武进农村商业银行", "上海银行",
        "上海银行", "贵州省农村信用社联合社", "贵州省农村信用社联合社", "江苏锡州农村商业银行", "江苏锡州农村商业银行",
        "中外合资", "中外合资", "长沙市商业银行", "长沙市商业银行", "长沙市商业银行", "长沙市商业银行", "兴业银行", "兴业银行",
        "兴业银行", "兴业银行", "兴业银行", "兴业银行", "石嘴山城市信用社", "张家口市商业银行", "交通银行", "中国工商银行",
        "中国建设银行", "大庆市商业银行", "Discover Financial Services，Inc", "", "Discover Financial Services，Inc",
        "", "Discover Financial Services，Inc", "", "Discover Financial Services，Inc", "", "上海银行", "上海银行",
        "泉州市商业银行", "泉州市商业银行", "广东发展银行", "广东发展银行", "广东发展银行", "广东发展银行", "广东发展银行",
        "广东发展银行", "招商", "招商", "招商银行", "招商银行", "长沙市商业银行", "长沙市商业银行", "南通商业银行",
        "南通商业银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行",
        "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行",
        "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行",
        "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "浦东发展银行", "贵阳市商业银行",
        "贵阳市商业银行", "郑州市商业银行", "工商银行", "工商银行", "工商银行", "工商银行", "上海浦东发展银行", "深圳发展银行"};
}
