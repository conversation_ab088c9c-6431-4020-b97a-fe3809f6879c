package com.welab.crm.interview.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 减免申请记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_reduce_apply_record")
public class OpReduceApplyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 申请减免金额
     */
    private BigDecimal amount;

    /**
     * 系统可减免金额
     */
    private BigDecimal reducibleAmount;

    /**
     * 减免后费率
     */
    private BigDecimal rateAfterReduce;

    /**
     * 减免级别(类型)
     */
    private String reductionType;

    /**
     * 减免原因
     */
    private String reductionReason;

    /**
     * 审核状态；0-申请；1-初审通过；2-复审通过；3-拒绝
     */
    private String approvalStatus;

    /**
     * 推送财务状态
     */
    private String pushFlag;

    /**
     * 申请人
     */
    private Long staffId;

    /**
     * 流水号；用来关联单次提交的全表减免申请单和退款申请单
     */
    private String requestNo;

    /**
     * 减免状态
     */
    private String result;

    /**
     * 投诉渠道
     */
    private String complaintChannel;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 任务状态；0-处理中；1-完成
     */
    private String taskStatus;


    /**
     * 失败原因
     */
    private String failReason;


}
