package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.base.exception.CrmBaseException;
import com.welab.crm.base.utils.DateUtils;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.wechat.WeChatReqBaseDTO;
import com.welab.crm.interview.dto.workorder.WorkOrderContactQueryDTO;
import com.welab.crm.interview.dto.workorder.WorkOrderNoticeDTO;
import com.welab.crm.interview.mapper.*;
import com.welab.crm.interview.service.WeChatDubboService;
import com.welab.crm.interview.service.WorkOrderService;
import com.welab.crm.interview.util.DateUtil;
import com.welab.crm.interview.vo.MobileBakVO;
import com.welab.crm.interview.vo.workorder.WorkOrderNoticeVO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.privacy.util.http.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单相关服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {

    @Resource
    private DataCustomerMapper dataCustomerMapper;

    @Resource
    private WoTaskMapper woTaskMapper;

    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;
    
    @Resource
    private WeChatDubboService weChatDubboService;
	
	@Resource
	private SensitiveWorkorderConfigMapper sensitiveWorkorderConfigMapper;
	
	@Resource
	private DataLoanApplicationMapper dataLoanApplicationMapper;

    /**
     * 日常通知的工单类型
     */
    private static final List<String> DAILY_NOTICE_ORDER_TYPE_LIST = new ArrayList<>(Arrays.asList("资方工单", "监管工单"));

    /**
     * 需要跑批的工单类型列表
     */
    private static final List<String> BATCH_TYPE_LIST = new ArrayList<>(Arrays.asList("资方工单", "监管工单", "舆情工单"));


    @Value("${staff.work.order.monitor.warning.robot.key}")
    private String robotKey;

    /**
     * 工单日常通知
     */
    private static final String DAILY_NOTICE_PREFIX = "<font color=\"warning\">【工单任务通知_未首联】</font>\n";

	private static final String OVER_SEVEN_DAY_UN_CONTACT_PREFIX = "<font color=\"warning\">【超7天无联系记录-资方、监管】</font>\n";
	private static final String OVER_TWENTY_FOUR_HOUR_UN_CONTACT_PREFIX = "<font color=\"warning\">【超24小时无联系记录-资方、监管】</font>\n";
    
    private static final String ORDER_TASK_NOTICE_PREFIX = "<font color=\"warning\">【工单任务通知_超**{0}**天未结案统计】总**{1}**单量</font>\n" + 
            "> ";
	
	
	private static final String REMINDER_ORDER_NOTICE = "【<font color=\"warning\">催单通知</font>】\n" +
			"> 用户姓名：{0}\n" +
			"> uuid：{1}\n" +
			"> 当前处理人：{2}\n" +
			"> 催单客服：{3}\n" +
			"> 催单内容：{4}";
	@Autowired
	private DateInfoMapper dateInfoMapper;

	@Override
    public List<JSONObject> queryOrderMobileBak(String mobile) {
		
		List<JSONObject> mobileList = new ArrayList<>();
        List<MobileBakVO> mobileBakVoList = dataCustomerMapper.queryMobileBakByMobile(mobile);
        for (MobileBakVO bakVO : mobileBakVoList) {
            if (Objects.isNull(bakVO)){
                continue;
            }
            if (StringUtils.isNotBlank(bakVO.getMobileBak())){
	            JSONObject mobileObject = new JSONObject();
				mobileObject.put("mobile", bakVO.getMobileBak());
				mobileObject.put("mobileUpdateTime", bakVO.getMobileUpdateTime());
				mobileList.add(mobileObject);
            }   
            
            if (StringUtils.isNotBlank(bakVO.getMobileBaks())){
                for (String mobileBakSplit : bakVO.getMobileBaks().split(",")) {
                    if (StringUtils.isNotBlank(mobileBakSplit)){
	                    JSONObject mobileObject = new JSONObject();
	                    mobileObject.put("mobile", mobileBakSplit);
	                    mobileObject.put("mobileUpdateTime", bakVO.getMobileUpdateTime());
	                    mobileList.add(mobileObject);
                    }
                }
            }
        }
            
        return mobileList;

    }

    @Override
    public void batchUpdateWoTaskSucLoanCount() {

        log.info("batchUpdateWoTaskSucLoanCount start ");
        List<WoTask> woTasks = woTaskMapper.queryAllByType(BATCH_TYPE_LIST);
        if (CollectionUtils.isEmpty(woTasks)) {
            log.info("batchUpdateWoTaskSucLoanCount 工单数量为空");
        }

        for (WoTask woTask : woTasks) {
            try {
                if (Objects.nonNull(woTask) && Objects.nonNull(woTask.getCustId())) {
                    DataCustomer dataCustomer = dataCustomerMapper.selectById(woTask.getCustId());
                    if (Objects.nonNull(dataCustomer)) {
                        List<LoanApplicationDTO> loanList =
                            loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(
                                Long.valueOf(dataCustomer.getUuid()), Arrays.asList("disbursed", "closed"));
                        woTask.setSucLoanCount(loanList.size());
                        woTaskMapper.updateById(woTask);
                    }

                }
            } catch (Exception e) {
                log.warn("batchUpdateWoTaskSucLoanCount error", e);
            }
        }

        log.info("batchUpdateWoTaskSucLoanCount end");


    }

	@Override
	public void queryDailyOrderAndNotice() {

		// 休息日不通知
		String isWorkDay = queryTodayIsWorkDay();
		if ("2".equals(isWorkDay)) {
			return;
		}

		WorkOrderContactQueryDTO dto = new WorkOrderContactQueryDTO();
		dto.setOrderTypeList(DAILY_NOTICE_ORDER_TYPE_LIST);
		dto.setIsFirstContact(1);
		dto.setStartTime(DateUtil.getDateAtTime(17, 0, 0, -1));
		dto.setEndTime(DateUtil.getDateAtTime(17, 0, 0, 0));
		List<WorkOrderNoticeVO> list = woTaskMapper.queryNoticeOrder(dto);
		if (CollectionUtils.isEmpty(list)) {
			log.info("queryDailyOrderAndNotice 工单数量为空");
			return;
		}
		StringBuilder noticeMsg = new StringBuilder(DAILY_NOTICE_PREFIX);
		list.forEach(noticeVo -> {
			noticeMsg.append("> uuid：").append(noticeVo.getUuid()).append("\n")
					.append("分单时间：").append(com.welab.common.utils.DateUtil.dateToString(noticeVo.getDistributeTime(), com.welab.common.utils.DateUtil.TimeFormatter.YYYY_MM_DD_HH_MM_SS)).append("\n")
					.append("客服姓名：").append(noticeVo.getStaffName()).append("\n");

			noticeMsg.append("\n");
		});

		pushWechatNotice(robotKey, noticeMsg.toString());
	}

	/**
	 * 查询今天是否是工作日,"1":是，"2":"不是"
	 * @return
	 */
	private String queryTodayIsWorkDay() {
		// yyyyMMdd
		String dateStrLong = DateUtils.formatDate(new Date(), DateUtils.YYYYMMDD);

		// 获取本地map,查询当天是否节假日
		DateInfo dateInfo = dateInfoMapper.selectOne(Wrappers.lambdaQuery(DateInfo.class).eq(DateInfo::getDate, Integer.parseInt(dateStrLong)));
		return dateInfo.getWorkday().toString();
	}


	@Override
	public void queryNotCloseOrderAndNotice(Integer startDay, Integer endDay) {
		WorkOrderContactQueryDTO dto = new WorkOrderContactQueryDTO();
		dto.setNotOverStartDays(startDay);
		dto.setNotOverEndDays(endDay);
		List<WorkOrderNoticeVO> list = woTaskMapper.queryNoticeOrder(dto);
		if (CollectionUtils.isEmpty(list)) {
			log.info("queryNotCloseOrderAndNotice 工单数量为空");
			return;
		}
		StringBuilder orderNotice = new StringBuilder(ORDER_TASK_NOTICE_PREFIX);
		list.stream().collect(Collectors.groupingBy(WorkOrderNoticeVO::getStaffName)).forEach((staffName, staffList) -> {
			orderNotice.append(staffName).append("：");
			staffList.stream().collect(Collectors.groupingBy(WorkOrderNoticeVO::getOrderType)).forEach((orderType, orderTypeList) -> {
				orderNotice.append(orderType, 0, 2).append(" ").append(orderTypeList.size()).append("单、");
			});
			orderNotice.delete(orderNotice.length() - 1, orderNotice.length());
			orderNotice.append("\n");
		});

		pushWechatNotice(robotKey, MessageFormat.format(orderNotice.toString(), startDay + (Objects.isNull(endDay) ? "" : "~" + endDay), list.size()));
	}

	@Override
	public void sensitiveOrderNotice(WorkOrderNoticeDTO dto) {
		pushWechatNotice(robotKey, MessageFormat.format(REMINDER_ORDER_NOTICE, dto.getUserName(), dto.getUuid(), dto.getCurrentStaff(), dto.getReminderStaff(), dto.getContent()));
	}

	@Override
	public void queryOverSevenDayUnContactOrderAndNotice() {
		// 休息日不通知
		String isWorkDay = queryTodayIsWorkDay();
		if ("2".equals(isWorkDay)) {
			return;
		}
		WorkOrderContactQueryDTO dto = new WorkOrderContactQueryDTO();
		dto.setOrderTypeList(DAILY_NOTICE_ORDER_TYPE_LIST);
		dto.setIsFirstContact(1);
		List<WorkOrderNoticeVO> list = woTaskMapper.queryNoticeOrder(dto);
		list = list.stream().filter(item -> com.welab.common.utils.DateUtil.getDaysBetween(item.getOrderCreateTime(), new Date()) >= 7).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(list)) {
			log.info("querySevenDayNotContactOrderAndNotice 工单数量为空");
			return;
		}
		StringBuilder noticeMsg = new StringBuilder(OVER_SEVEN_DAY_UN_CONTACT_PREFIX);
		list.stream().collect(Collectors.groupingBy(WorkOrderNoticeVO::getOrderType)).forEach((key, value) -> {
			noticeMsg.append("> ");
			noticeMsg.append("**").append(key).append("**").append(" ").append(value.size()).append("单").append("：");
			value.stream().collect(Collectors.groupingBy(WorkOrderNoticeVO::getStaffName)).forEach((k, v) -> {
				noticeMsg.append(k).append(" ").append(v.size()).append("单、");
			});
			noticeMsg.delete(noticeMsg.length() - 1, noticeMsg.length());
			noticeMsg.append("\n");
			noticeMsg.append("\n");
		});

		pushWechatNotice(robotKey, noticeMsg.toString());
	}

	@Override
	public void queryOverTwentyFourHourUnContactOrderAndNotice() {
		// 休息日不通知
		String isWorkDay = queryTodayIsWorkDay();
		if ("2".equals(isWorkDay)) {
			return;
		}
		WorkOrderContactQueryDTO dto = new WorkOrderContactQueryDTO();
		dto.setOrderTypeList(DAILY_NOTICE_ORDER_TYPE_LIST);
		List<WorkOrderNoticeVO> list = woTaskMapper.queryNoticeOrder(dto);
		list = list.stream()
				.map(item -> {
					if (Objects.isNull(item.getLastContactTime()) || item.getLastContactTime().before(item.getOrderCreateTime())) {
						item.setLastContactTime(item.getOrderCreateTime());
					}
					return item;
				})
				.filter(item -> com.welab.common.utils.DateUtil.getDaysBetween(item.getLastContactTime(), new Date()) >= 1).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(list)) {
			log.info("queryOverTwentyFourHourUnContactOrderAndNotice 工单数量为空");
			return;
		}
		StringBuilder noticeMsg = new StringBuilder(OVER_TWENTY_FOUR_HOUR_UN_CONTACT_PREFIX);
		
		list.forEach(noticeVo -> {
			noticeMsg.append("> uuid:").append(noticeVo.getUuid()).append("\n")
//					.append("上次联系时间：").append(com.welab.common.utils.DateUtil.dateToString(noticeVo.getLastContactTime(), "yyyy-MM-dd")).append("\n")
					.append("客服:").append(noticeVo.getStaffName()).append("\n");
			
			noticeMsg.append("\n");
		});

		pushWechatNotice(robotKey, noticeMsg.toString());
	}


	@Override
	public void orderResponseTimeOutStateUpdate() {
		List<SensitiveWorkorderConfig> configList = sensitiveWorkorderConfigMapper.selectList(Wrappers.lambdaQuery(SensitiveWorkorderConfig.class)
				.like(SensitiveWorkorderConfig::getWarnType, "system")
				.eq(SensitiveWorkorderConfig::getIsStatus, 1)
				.isNotNull(SensitiveWorkorderConfig::getFollowUpTime));

		for (SensitiveWorkorderConfig config : configList) {
			WorkOrderContactQueryDTO queryDTO = new WorkOrderContactQueryDTO();
			queryDTO.setWoTypeId(config.getWoTypeId());
			queryDTO.setWoTypeFirId(config.getWoTypeFirId());
			queryDTO.setWoTypeSecId(config.getWoTypeSecId());
			queryDTO.setWoTypeThirId(config.getWoTypeThirId());
			queryDTO.setIsFirstContact(1);
			List<WorkOrderNoticeVO> orderList = woTaskMapper.queryNoticeOrder(queryDTO);
			List<String> configPartnerList = new ArrayList<>();
			if (StringUtils.isNotBlank(config.getPartnerNames())){
				configPartnerList = Arrays.stream(config.getPartnerNames().split(",")).collect(Collectors.toList());
			}
			List<String> finalConfigPartnerList = configPartnerList;
			orderList.stream()
					// 过滤资金方
					.filter(item ->{
						if (CollectionUtils.isEmpty(finalConfigPartnerList)){
							return true;
						}
						List<String> partnerList = dataLoanApplicationMapper
								.selectList(Wrappers.lambdaQuery(DataLoanApplication.class).eq(DataLoanApplication::getWoTaskId, item.getId()))
								.stream().map(DataLoanApplication::getPartnerCode).collect(Collectors.toList());
						return partnerList.stream().anyMatch(finalConfigPartnerList::contains);
					})
					.filter(item -> DateUtil.getHoursDifference(item.getOrderCreateTime()) >= config.getFollowUpTime() && Objects.isNull(item.getResponseTimeOut()))
					.forEach(order -> {
						updateResponseTimeoutState(order.getId());
					});
			
		}
	}

	private void updateResponseTimeoutState(Long id) {
		WoTask woTask = new WoTask();
		woTask.setResponseTimeOut(true);
		woTaskMapper.update(woTask, Wrappers.lambdaUpdate(WoTask.class).eq(WoTask::getId, id));
	}

	private void pushWechatNotice(String robotKey, String msg) {
		WeChatReqBaseDTO dto = new WeChatReqBaseDTO();
		dto.setMsgType("markdown");
		dto.setContent(msg);
		dto.setKey(robotKey);
		dto.setType("workOrder");
		weChatDubboService.pushMsgToWeChatRobot(dto);
	}


    
}
