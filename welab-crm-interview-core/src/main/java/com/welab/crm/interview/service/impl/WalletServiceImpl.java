package com.welab.crm.interview.service.impl;

import static com.welab.util.DateUtils.DATE_TIME_FORMAT;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.capital.dto.CapitalBasicVo;
import com.welab.capital.provider.CapitalBasicProvider;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.BeanUtil;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.DateUtil.TimeFormatter;
import com.welab.crm.interview.constant.BusiConstant;
import com.welab.crm.interview.constant.LoanConstant;
import com.welab.crm.interview.dto.wallet.WalletBillQueryDTO;
import com.welab.crm.interview.dto.wallet.WalletOrderRecordDTO;
import com.welab.crm.interview.dto.wechat.WalletDueDTO;
import com.welab.crm.interview.dto.wechat.WalletDueSettleDTO;
import com.welab.crm.interview.dto.wechat.WalletLoanDTO;
import com.welab.crm.interview.enums.*;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.WalletService;
import com.welab.crm.interview.util.NumberUtil;
import com.welab.crm.interview.util.RandomUtil;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.interview.vo.wallet.WalletGrantHistoryVO;
import com.welab.crm.interview.vo.wallet.WalletGrantQuotaVO;
import com.welab.crm.interview.vo.wallet.WalletLoanDueInfoVO;
import com.welab.crm.interview.vo.wallet.WalletLoanDueVO;
import com.welab.crm.interview.vo.wallet.WalletMonthBillVO;
import com.welab.crm.interview.vo.wallet.WalletOrderRecordVO;
import com.welab.crm.interview.vo.wallet.WalletOutstandingVO;
import com.welab.crm.interview.vo.wallet.WalletRepayRecordVO;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.finance.loanprocedure.dto.DueDTO;
import com.welab.finance.loanprocedure.dto.UserLoanDTO;
import com.welab.finance.loanprocedure.dubbo.DueDubboService;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.vo.DueVO;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.finance.repayment.vo.RepaymentFeeItemVo;
import com.welab.loanapplication.interfaces.dto.DueDTO.DueType;
import com.welab.support.credit.dto.cs.GetGrantHistoryReq;
import com.welab.support.credit.dto.cs.GetGrantHistoryResp;
import com.welab.support.credit.service.QuotaServiceForCS;
import com.welab.usercenter.model.base.User;
import com.welab.usercenter.service.UserService;
import com.welab.util.DateUtils;
import com.welab.wallet.app.dto.BillIntervalQueryDTO;
import com.welab.wallet.app.dto.WalletOrderDTO;
import com.welab.wallet.app.dubbo.BillDubboService;
import com.welab.wallet.app.dubbo.WalletOrderDubboService;
import com.welab.wallet.app.enums.BillStatusEnum;
import com.welab.wallet.app.enums.OrderChannelTypeEnum;
import com.welab.wallet.app.enums.OrderTradeChannelEnum;
import com.welab.wallet.app.enums.OrderTypeEnum;
import com.welab.wallet.app.enums.WalletOrderStatusEnum;
import com.welab.wallet.app.vo.MonthBillVO;
import com.welab.wallet.app.vo.WalletOrderReportVO;
import com.welab.wallet.application.interfaces.dto.CreditApplicationDTO;
import com.welab.wallet.application.interfaces.facade.CreditApplicationServiceFacade;
import com.welab.wallet.application.interfaces.facade.WalletCreditTransformServiceFacade;
import com.welab.wallet.application.interfaces.vo.WalletCreditTransformVO;
import com.welab.wallet.installment.dto.LoanDueQueryDTO;
import com.welab.wallet.installment.dto.QueryLoanDTO;
import com.welab.wallet.installment.dubbo.WalletLoanDubbboService;
import com.welab.wallet.installment.vo.WalletDueVO;
import com.welab.wallet.installment.vo.WalletLoanRateVO;
import com.welab.wallet.installment.vo.WalletLoanVO;
import com.welab.wallet.payment.vo.WalletOrderVO;
import com.welab.wallet.repayment.dto.QueryBillDTO;
import com.welab.wallet.repayment.dto.RepayRecordQueryDTO;
import com.welab.wallet.repayment.dto.RepaymentCalculateDTO;
import com.welab.wallet.repayment.dto.RepaymentDTO;
import com.welab.wallet.repayment.dubbo.RepaymentCalculationDubboService;
import com.welab.wallet.repayment.dubbo.RepaymentDubboService;
import com.welab.wallet.repayment.enums.DebitResourceEnum;
import com.welab.wallet.repayment.enums.RepaymentModeEnum;
import com.welab.wallet.repayment.enums.UserRepaysStatusEnum;
import com.welab.wallet.repayment.vo.RepaymentFeeItemVO;
import com.welab.wallet.repayment.vo.RepaymentRecordsVO;
import com.welab.xdao.context.page.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Slf4j
@Service
public class WalletServiceImpl implements WalletService {

    @Resource
    private WalletLoanDubbboService walletLoanDubbboService;
    @Resource
    private RepaymentDubboService iRepaymentDubboService;
    @Resource
    private WalletOrderDubboService walletOrderDubboService;
    @Resource
    private CapitalBasicProvider capitalBasicProvider;
    @Resource
    private RepaymentCalculationDubboService walletRepaymentCalculationDubboService;
    @Resource
    private BillDubboService billDubboService;
    @Resource
    private CreditApplicationServiceFacade walletCreditApplicationService;
    @Resource
    private UserService userService;
    @Resource
    private WalletCreditTransformServiceFacade walletCreditTransformServiceFacade;
    @Resource
    private QuotaServiceForCS quotaServiceForCS;
    @Resource
    private LoanDubboService loanDubboService;
    @Resource
    private DueDubboService dueDubboService;
    @Resource
    private com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService calculationDubboService;
    @Resource
    private com.welab.finance.repayment.dubbo.RepaymentDubboService repaymentDubboService;
    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource(name = "walletPaymentOrderDubboService")
    private com.welab.wallet.payment.dubbo.WalletOrderDubboService walletPaymentOrderDubboService;

    @Override
    public WalletLoanDueInfoVO getWalletLoanDue(Integer userId, String month) {
        LoanDueQueryDTO loanDueQueryDTO = new LoanDueQueryDTO();
        loanDueQueryDTO.setUserId(userId);
        if (StringUtils.isNotEmpty(month)) {
            loanDueQueryDTO.setMonth(DateUtil.stringToDate(month, TimeFormatter.YYYYMM));
        }
        // 查询指定月的贷款
        Response<List<WalletLoanVO>> walletResponse = walletLoanDubbboService.getLoanDue(loanDueQueryDTO);
        if (Objects.isNull(walletResponse) || Objects.isNull(walletResponse.getResult())) {
            return null;
        }
        return convertWalletLoanDueInfoVO(walletResponse.getResult());
    }

    @Override
    public List<WalletMonthBillVO> getMonthBill(WalletBillQueryDTO queryDTO) {
        List<WalletMonthBillVO> result = new ArrayList<>();
        if (Objects.isNull(queryDTO)) {
            return result;
        }
        BillIntervalQueryDTO dto = new BillIntervalQueryDTO();
        dto.setUserId(queryDTO.getUserId());
        if (StringUtils.isNotEmpty(queryDTO.getBillStart())) {
            String[] startStr = queryDTO.getBillStart().split("-");
            dto.setYearStart(Integer.parseInt(startStr[0]));
            dto.setMonthStart(Integer.parseInt(startStr[1]));
        }
        if (StringUtils.isNotEmpty(queryDTO.getBillEnd())) {
            String[] endStr = queryDTO.getBillEnd().split("-");
            dto.setYearEnd(Integer.parseInt(endStr[0]));
            dto.setMonthEnd(Integer.parseInt(endStr[1]));
        }
        Response<List<MonthBillVO>> response = billDubboService.monthBillIntervalQuery(dto);
        if (Objects.nonNull(response) && response.getResult() != null) {
            result = response.getResult().stream().map(m -> {
                m.setStatus(BillStatusEnum.getEnumByStatus(m.getStatus()).getDesc());
                WalletMonthBillVO walletMonthBillVO = new WalletMonthBillVO();
                BeanUtils.copyProperties(m, walletMonthBillVO);
                return walletMonthBillVO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WalletOrderRecordVO> getWalletOrderRecords(WalletOrderRecordDTO dto) {
        List<WalletOrderRecordVO> result = new ArrayList<>();
        if (Objects.isNull(dto)) {
            return result;
        }
        WalletOrderDTO walletOrderDTO = new WalletOrderDTO();
        walletOrderDTO.setUserId(dto.getUserId());
        walletOrderDTO.setCurrentPage(dto.getCurrentPage());
        walletOrderDTO.setRowsPerPage(dto.getRowsPerPage());
        Response<Page<WalletOrderReportVO>> response = walletOrderDubboService.getOrderByPage(walletOrderDTO);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            List<WalletOrderReportVO> list = response.getResult().getList();
            result = list.stream().map(d -> {
                WalletOrderRecordVO vo = new WalletOrderRecordVO();
                BeanUtils.copyProperties(d, vo);
                //交易类型
                OrderTypeEnum orderTypeEnum = OrderTypeEnum.getEnumByStatus(d.getOrderType());
                vo.setOrderType(Objects.nonNull(orderTypeEnum) ? orderTypeEnum.getDesc() : "");
                //交易状态
                WalletOrderStatusEnum orderStatusEnum = WalletOrderStatusEnum.getEnumByStatus(d.getStatus());
                vo.setStatus(Objects.nonNull(orderStatusEnum) ? orderStatusEnum.getDesc() : "");
                //消费渠道
                OrderTradeChannelEnum channelEnum = OrderTradeChannelEnum.getEnumByStatus(d.getTradeChannel());
                vo.setTradeChannel(Objects.nonNull(channelEnum) ? channelEnum.getDesc() : "");
                //通道类型
                OrderChannelTypeEnum orderChannelTypeEnum = OrderChannelTypeEnum.getEnumByStatus(d.getChannelType());
                vo.setChannelType(Objects.nonNull(orderChannelTypeEnum) ? orderChannelTypeEnum.getDesc() : "");
                return vo;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WalletRepayRecordVO> getWalletRepayRecords(Long userId) {
        List<WalletRepayRecordVO> result = new ArrayList<>();
        if (Objects.isNull(userId)) {
            return result;
        }
        RepayRecordQueryDTO queryDTO = new RepayRecordQueryDTO();
        queryDTO.setUserId(userId);
        Response<List<RepaymentRecordsVO>> response = iRepaymentDubboService.queryRepayRecords(queryDTO);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
            List<RepaymentRecordsVO> list = response.getResult();
            result = list.stream().map(d -> {
                WalletRepayRecordVO walletRepayRecordVO = new WalletRepayRecordVO();
                BeanUtils.copyProperties(d, walletRepayRecordVO);
                //还款类型
                if (StringUtils.isNotEmpty(d.getAction())) {
                    walletRepayRecordVO.setAction(RepaymentModeEnum.getDisplayByCode(d.getAction()));
                }
                //还款状态
                if (StringUtils.isNotEmpty(d.getStatus())) {
                    walletRepayRecordVO.setStatus(UserRepaysStatusEnum.getCommentByStatus(d.getStatus()));
                }
                return walletRepayRecordVO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<WalletGrantQuotaVO> getGrantQuotaInfo(Long uuid) {
        List<WalletGrantQuotaVO> list = new ArrayList<>();
        if (Objects.isNull(uuid)) {
            return list;
        }
        //查询所有的额度进件订单
        List<CreditApplicationDTO> dtoList = walletCreditApplicationService.getApplicationsByUuid(uuid);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            list = dtoList.stream().map(dto -> {
                WalletGrantQuotaVO walletGrantQuotaVO = new WalletGrantQuotaVO();
                BeanUtils.copyProperties(dto, walletGrantQuotaVO);
                //申请时间
                walletGrantQuotaVO.setAppliedAt(DateUtil.dateToString(dto.getAppliedAt()));
                //审批时间
                walletGrantQuotaVO.setApprovedAt(DateUtil.dateToString(dto.getApprovedAt()));
                //订单状态
                walletGrantQuotaVO.setState(CreditStateEnum.getText(dto.getState()));
                //加急
                walletGrantQuotaVO.setUrgent(dto.getUrgent() == null ? "否" : "是");
                return walletGrantQuotaVO;
            }).collect(Collectors.toList());
        }
        User user = userService.getUserByUuid(uuid);
        if (Objects.nonNull(user)) {
            //钱包交叉营销数据
            WalletCreditTransformVO vo = walletCreditTransformServiceFacade.getWalletCreditTransform(user.getId());
            if (Objects.nonNull(vo)) {
                WalletGrantQuotaVO walletGrantQuotaVO = new WalletGrantQuotaVO();
                walletGrantQuotaVO.setApplicationId(vo.getApplicationId());
                walletGrantQuotaVO.setAmount(vo.getAmount());
                walletGrantQuotaVO.setAppliedAt(DateUtil.dateToString(vo.getCreatedAt()));
                walletGrantQuotaVO.setApprovedAt(DateUtil.dateToString(vo.getUpdatedAt()));
                if (StringUtils.isNotEmpty(vo.getStatus())) {
                    walletGrantQuotaVO.setState(LoanApplicationStateEnum.getStateEnum(vo.getStatus()).getText());
                }
                walletGrantQuotaVO.setOrigin(vo.getOrigin());
                walletGrantQuotaVO.setApplyType("normal");
                list.add(walletGrantQuotaVO);
            }
        }
        return list;
    }

    @Override
    public List<WalletGrantHistoryVO> getGrantQuotaHistory(GetGrantHistoryReq req) {
        List<WalletGrantHistoryVO> result = new ArrayList<>();
        if (Objects.isNull(req)) {
            return result;
        }
        Response<Page<GetGrantHistoryResp>> historyResp = quotaServiceForCS.getGrantHistory(req);
        log.info("额度授信记录,response:{}", BeanUtil.toString(historyResp));
        if (Objects.nonNull(req) && Objects.nonNull(historyResp.getResult())) {
            List<GetGrantHistoryResp> resps = historyResp.getResult().getList();
            if (CollectionUtils.isNotEmpty(resps)) {
                WalletGrantHistoryVO historyDTO = null;
                for (GetGrantHistoryResp d : resps) {
                    historyDTO = new WalletGrantHistoryVO();
                    historyDTO.setAipNo(d.getAipNo());
                    historyDTO.setQuotaType(d.getQuotaType());
                    historyDTO.setAmount(d.getAmount());
                    historyDTO.setChangeBy(d.getChangeBy());
                    historyDTO.setCreditType(d.getCreditType());
                    historyDTO.setGmtCreate(DateUtil.dateToString(d.getGmtCreate()));
                    historyDTO.setOpType(d.getOpType());
                    historyDTO.setRevoked(d.getRevoked());
                    result.add(historyDTO);
                }
            }
        }
        return result;
    }

    @Override
    public void earlyRepay(Long userId, String applicationId) {
        if (Objects.isNull(userId) || StringUtils.isBlank(applicationId)) {
            throw new CrmInterviewException("useId和贷款号不能为空");
        }
        RepaymentDTO repaymentDTO = new RepaymentDTO();
        repaymentDTO.setUserId(userId);
        repaymentDTO.setApplicationId(applicationId);
        repaymentDTO.setAction(RepaymentModeEnum.REPAY_FULL.getCode());
        repaymentDTO.setServiceNo(RandomUtil.getRandomFileName("KF", 11));
        repaymentDTO.setDebitResource(DebitResourceEnum.KEFU.code);
        Response response = iRepaymentDubboService.repayment(repaymentDTO);
        log.info("用户提前结清还款, userId={},applicationId={}, {}", userId.toString(), applicationId,
            Objects.nonNull(response) ? response.toString() : "");
    }

    @Override
    public boolean updateLoanYBStatus(String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            throw new CrmInterviewException("贷款号不能为空");
        }
        boolean result = false;
        try {
            Response<Integer> response = iRepaymentDubboService.updateLoanYBStatus(applicationId, true);
            log.info("打开允许全额结清标识, applicationId:{}, response:{}", applicationId,
                Objects.nonNull(response) ? BeanUtil.toString(response) : "");
            if (Objects.nonNull(response) && ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                result = true;
            }
        } catch (Exception e) {
            log.error("打开允许全额结清标识异常, applicationId:{}", applicationId, e);
        }
        return result;
    }

    @Override
    public List<String> allowEarlyRepayBatch(List<String> applicationIdList) {
        List<String> failedList = new ArrayList<>();
        if (CollectionUtils.isEmpty(applicationIdList)) {
            return failedList;
        }
        for (String applicationId : applicationIdList) {
            try {
                Response<Integer> response = iRepaymentDubboService.updateLoanYBStatus(applicationId, true);
                if (Objects.isNull(response) || !ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                    log.error("打开允许全额结清标识失败, applicationId={}, response={}", applicationId,
                        Objects.nonNull(response) ? BeanUtil.toString(response) : "null");
                    failedList.add(applicationId);
                }
            } catch (Exception e) {
                log.error("打开允许全额结清标识异常, applicationId:{}", applicationId, e);
                failedList.add(applicationId);
            }
        }
        return failedList;
    }

    @Override
    public List<WalletOutstandingVO> getOutstandingLoanList(Integer userId) {
        UserLoanDTO dto = new UserLoanDTO();
        dto.setUserId(userId.longValue());
        dto.setStatusList(Arrays.asList(LoanConstant.DISBURSED));
        Response<List<LoanVO>> response = loanDubboService.findLoanListByUser(dto);
        if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
            return null;
        }
        List<LoanVO> loans = response.getResult();
        return loans.stream().map(this::buildWalletOutstandingVO).collect(Collectors.toList());
    }

    private WalletOutstandingVO buildWalletOutstandingVO(LoanVO loan) {
        WalletOutstandingVO outstandingVO = new WalletOutstandingVO();
        BeanUtils.copyProperties(loan, outstandingVO);
        outstandingVO.setProductName(loanApplicationService.getProductName(loan.getProductCode()));
        outstandingVO.setState(loanApplicationService.getState(loan.getStatus()));
        outstandingVO.setOrigin(loan.getOriginCode());
        outstandingVO.setDisbursedTime(loan.getDisbursedAt());
        //转贷时间
        outstandingVO.setTransLoanDate(DateUtil.dateToString(loan.getCreatedAt()));
        //添加合作资金方
        outstandingVO.setPartnerName(getPartnerName(loan.getPartnerCode()));
        Integer currTenor = 0;
        Date deadDate = null;
        //获取整笔贷款还款计划
        List<DueVO> totalDues = loan.getDueList();
        if (CollectionUtils.isNotEmpty(totalDues)) {
            //期数排序
            totalDues.sort(Comparator.comparing(DueVO::getDueDate));
            for (DueVO due : totalDues) {
                if (due.getAmount().compareTo(due.getSettledAmount()) != 0) {
                    //贷款当前期数
                    currTenor = due.getIndex();
                    deadDate = due.getDueDate();
                    break;
                }
            }
        }
        //获取该笔贷款当前期的due list
        DueDTO dueDTO = new DueDTO();
        dueDTO.setApplicationId(loan.getApplicationId());
        dueDTO.setDueIndex(currTenor);
        try {
            Response<List<DueVO>> response = dueDubboService.queryDues(dueDTO);
            if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResult())) {
                List<DueVO> dues = response.getResult();
                //到期日期
                if (deadDate == null) {
                    DueVO lastDue = dues.get(dues.size() - 1);
                    deadDate = lastDue.getDueDate();
                }
            }
        } catch (Exception e) {
            log.warn("buildOutstandingVO queryDues error. {}", dueDTO.toString(), e);
        }
        //到期日期
        if(Objects.nonNull(deadDate)){
            outstandingVO.setDeadDate(DateUtil.dateToDate(deadDate, TimeFormatter.YYYY_MM_DD));
        }
        //贷款总期数
        Integer totalTenor = getTotalTenor(loan.getTenor());
        //贷款的剩余期数
        Integer leftTenor = totalTenor - currTenor + 1;
        outstandingVO.setTenor(leftTenor + "/" + totalTenor);
        //如果是confirmed状态没有还款记录，默认为0
        outstandingVO.setLoanBalance(BigDecimal.ZERO);
        com.welab.finance.repayment.dto.RepaymentCalculateDTO dto = new com.welab.finance.repayment.dto.RepaymentCalculateDTO();
        dto.setRepaymentMode("YB");
        dto.setApplicationId(loan.getApplicationId());
        dto.setRepaymentDate(new Date());
        dto.setOrgId("0");
        try {
            Response<RepaymentFeeItemVo> itemVo = calculationDubboService.getRepaymentFeeItems(dto);
            if (Objects.nonNull(itemVo) && Objects.nonNull(itemVo.getResult())) {
                outstandingVO.setLoanBalance(itemVo.getResult().getAmount());
            }
        } catch (Exception e) {
            log.warn("buildOutstandingVO getRepaymentFeeItems error. {}", dto.toString(), e);
        }
        //已结清
        if (!LoanConstant.DISBURSED.equals(loan.getStatus()) && NumberUtil.eqZero(outstandingVO.getLoanBalance())) {
            outstandingVO.setLastTenor("0/" + totalTenor);
        }
        if (StringUtils.isNotEmpty(loan.getChannelCode())) {
            outstandingVO.setPaymentChannel(ChannelCodeEnum.getChannelNameByChannelCode(loan.getChannelCode()));
        }
        try {
            Response<Boolean> isAllow = repaymentDubboService.checkAllowEarlySettle(loan.getApplicationId());
            if (Boolean.TRUE.equals(isAllow.getResult())) {
                outstandingVO.setIsAllowEarlySettle("是");
            } else {
                outstandingVO.setIsAllowEarlySettle("否");
            }
        } catch (Exception e) {
            log.warn("buildOutstandingVO checkAllowEarlySettle error,applicationId={}", loan.getApplicationId(), e);
        }
        return outstandingVO;
    }

    @Override
    public WalletLoanDTO getWalletRepayPlan(String applicationId) {
        WalletLoanDTO loanDTO = new WalletLoanDTO();
        // 根据贷款号查询贷款和还款计划
        Response<WalletLoanVO> response =  walletLoanDubbboService.getLoanDueByApplicationId(applicationId);
        if (Objects.nonNull(response) && Objects.nonNull(response.getResult())){
            List<WalletDueDTO> dues = new ArrayList<>();
            loanDTO = buildLoanDue(response.getResult());
            // 获取还款计划
            List<WalletDueVO> dueList = response.getResult().getDues();
            if (CollectionUtils.isNotEmpty(dueList)) {
                // 根据期数进行排序
                dueList.sort(Comparator.comparing(WalletDueVO::getIndexNo));
                // 按照index进行分组
                Map<Integer, List<WalletDueVO>> dueMap = dueList.stream()
                        .collect(Collectors.groupingBy(WalletDueVO::getIndexNo));
                for (Map.Entry entry : dueMap.entrySet()) {
                    BigDecimal sumStillAmount = new BigDecimal(0);
                    BigDecimal sumDueAmount = new BigDecimal(0);
                    //获取当前期数和计划
                    Integer index = (Integer) entry.getKey();
                    List<WalletDueVO> dueVos = (List<WalletDueVO>) entry.getValue();
                    WalletDueDTO dueDTO = buildDueVo(dueVos,loanDTO.getStatus());

                    Date dueDate = null;
                    for(WalletDueVO d : dueVos){
                        //已还金额
                        if (null != d.getSettledAmount()) {
                            sumStillAmount = sumStillAmount.add(d.getSettledAmount());
                        }
                        //应还金额
                        if (null != d.getAmount()) {
                            sumDueAmount = sumDueAmount.add(d.getAmount());
                        }
                        //还款日期
                        if(DueType.PRINCIPAL.getValue().equals(d.getDueType())){
                            dueDate = d.getDueDate();
                        }
                    }
                    dueDTO.setIndex(index);
                    dueDTO.setSumStillAmount(sumStillAmount);
                    dueDTO.setSumDueAmount(sumDueAmount);
                    dueDTO.setDueDate(DateUtils.parseDate(dueDate,DATE_TIME_FORMAT));
                    dues.add(dueDTO);
                }
                loanDTO.setDues(dues);
            }
        }
        return loanDTO;
    }


    @Override
    public Response<RepaymentVO> repayment(com.welab.crm.interview.dto.repay.RepaymentDTO repaymentDTO) {
        Response rsp = new Response();
        if (repaymentDTO.getApplicationId().startsWith(BusiConstant.WL)){
            RepaymentDTO dto = new RepaymentDTO();
            //还款需要传还款日，需要调用还款试算接口
            QueryBillDTO queryBillDTO = new QueryBillDTO();
            queryBillDTO.setUserId(Long.valueOf(repaymentDTO.getUserId()));
            Response<RepaymentFeeItemVO> result = walletRepaymentCalculationDubboService.getUserBill(queryBillDTO);

            dto.setApplicationId(repaymentDTO.getApplicationId());
            dto.setRepayDate(result.getResult().getNeedRepaymentDate());
            dto.setServiceNo(repaymentDTO.getServiceNo());
            dto.setDebitResource(repaymentDTO.getRepayOrigin());
            dto.setUserId(Long.valueOf(repaymentDTO.getUserId()));
            dto.setAction(repaymentDTO.getRepaymentMode());
            log.info("repayment 钱包代扣开始,请求参数:{}", JSON.toJSONString(dto));
            rsp = iRepaymentDubboService.repayment(dto);
            log.info("repayment 钱包代扣结束,返回结果:{}", JSON.toJSONString(rsp));
        }

        return rsp;
    }

    /**
     * 获取贷款的总共期数
     */
    private Integer getTotalTenor(String tenor) {
        return Integer.parseInt(tenor.split("M")[0]);
    }

    private WalletLoanDueInfoVO convertWalletLoanDueInfoVO(List<WalletLoanVO> result) {
        List<WalletLoanDueVO> list = new ArrayList<>();
        BigDecimal sumFeeAmount = BigDecimal.ZERO;
        BigDecimal sumSettledAmount = BigDecimal.ZERO;
        BigDecimal sumOutstandingAmount = BigDecimal.ZERO;
        BigDecimal sumSettleAllAmount = BigDecimal.ZERO;
        for (WalletLoanVO vo : result) {
            WalletLoanDueVO walletLoanDueVO = new WalletLoanDueVO();
            BeanUtils.copyProperties(vo, walletLoanDueVO);
            sumFeeAmount = sumFeeAmount.add(vo.getCurrentFeeAmount());
            sumSettledAmount = sumSettledAmount.add(vo.getCurrentSettledAmount());
            sumOutstandingAmount = sumOutstandingAmount.add(vo.getCurrentOutstandingAmount());
            if (WalletLoanStatus.DISBURSED.getValue().equals(walletLoanDueVO.getStatus())) {
                if (NumberUtil.gtZero(vo.getCurrentOutstandingAmount())) {
                    walletLoanDueVO.setStatus(DisbursedLoanStateEnum.UNSETTLED.getText());
                } else {
                    walletLoanDueVO.setStatus(DisbursedLoanStateEnum.SETTLED.getText());
                }
            } else {
                walletLoanDueVO.setStatus(WalletLoanStatus.getText(walletLoanDueVO.getStatus()));
            }
            if (null != vo.getDisbursedAt()) {
                walletLoanDueVO.setDisbursedAt(DateUtils.parseDate(vo.getDisbursedAt(), DATE_TIME_FORMAT));
            }
            if (null != vo.getClosedAt()) {
                walletLoanDueVO.setClosedAt(DateUtils.parseDate(vo.getClosedAt(), DATE_TIME_FORMAT));
            }
            //查询是否可以全额结清
            try {
                Response<Boolean> response = iRepaymentDubboService.isAllowEarlySettle(vo.getApplicationId());
                if (response != null && Objects.nonNull(response.getResult())) {
                    walletLoanDueVO.setIsAllowEarlySettle(response.getResult() ? "是" : "否");
                }
            } catch (Exception e) {
                log.error("钱包查询是否可以全额结清异常,applicationId:{}", vo.getApplicationId(), e);
            }
            //查询资金方
            walletLoanDueVO.setPartnerName(getPartnerName(vo.getPartnerCode()));

            // 查询费率
            try {
                Response<WalletLoanRateVO> res = walletLoanDubbboService.getLoanRateByApplicationId(vo.getApplicationId());
                walletLoanDueVO.setLoanRate(res.getResult().getTotalRate());
            } catch (Exception e){
                log.warn("convertWalletLoanDueInfoVO 查询费率异常", e);
            }
            //查询全额结清金额
            // 判断是否已经结清，如果已经结清则不进行结清金额查询
            boolean isSettled = queryIsSettled(walletLoanDueVO.getApplicationId());
            if (StringUtils.isNotEmpty(vo.getPartnerCode()) && !isSettled) {
                RepaymentCalculateDTO calculateDTO = new RepaymentCalculateDTO();
                calculateDTO.setUserId(walletLoanDueVO.getUserId().toString());
                calculateDTO.setAction("YB");
                calculateDTO.setApplicationId(walletLoanDueVO.getApplicationId());
                calculateDTO.setRepaymentDate(new Date());
                try {
                    Response<RepaymentFeeItemVO> itemVo = walletRepaymentCalculationDubboService
                        .getRepaymentFeeItems(calculateDTO);
                    log.info("buildLoanDue#itemVo:{}", JSONObject.toJSONString(itemVo));
                    if (Objects.nonNull(itemVo) && Objects.nonNull(itemVo.getResult())) {
                        walletLoanDueVO.setSettleAllAmount(itemVo.getResult().getAmount());
                        sumSettleAllAmount = sumSettleAllAmount.add(itemVo.getResult().getAmount());
                    }
                } catch (Exception e) {
                    log.error("查询钱包全额结清金额异常，{}", calculateDTO.toString(), e);
                }
            }
            list.add(walletLoanDueVO);
        }
        WalletLoanDueInfoVO walletLoanDueInfoVO = new WalletLoanDueInfoVO();
        walletLoanDueInfoVO.setList(list);
        walletLoanDueInfoVO.setSumFeeAmount(sumFeeAmount);
        walletLoanDueInfoVO.setSumSettledAmount(sumSettledAmount);
        walletLoanDueInfoVO.setSumOutstandingAmount(sumOutstandingAmount);
        walletLoanDueInfoVO.setSumSettleAllAmount(sumSettleAllAmount);
        return walletLoanDueInfoVO;
    }

    /**
     * 查询钱包分期是否提前结清
     * @param applicationId
     * @return
     */
    private boolean queryIsSettled(String applicationId) {
        try {
            QueryLoanDTO queryLoanDTO = new QueryLoanDTO();
            queryLoanDTO.setApplicationId(applicationId);
            Response<List<com.welab.wallet.installment.vo.LoanVO>> walletLoanList = walletLoanDubbboService
                    .queryLoan(queryLoanDTO);
            if (Objects.nonNull(walletLoanList)) {
                List<com.welab.wallet.installment.vo.LoanVO> loanResultList = walletLoanList.getResult();
                if (CollectionUtils.isNotEmpty(loanResultList)) {
                    com.welab.wallet.installment.vo.LoanVO loanVO = loanResultList.get(0);
                    if ("early_settled".equals(loanVO.getStatus())) {
                        return true;
                    }
                }
            }
        } catch (Exception e){
            log.warn("queryIsSettled 异常", e);
        }
        return false;
    }

    /**
     * 获得合作资金方名称
     *
     * @param partnerCode 资金方编码
     * @return
     */
    private String getPartnerName(String partnerCode) {
        try {
            if (StringUtils.isNotBlank(partnerCode)) {
                Response<List<CapitalBasicVo>> response = capitalBasicProvider.getCapitalBasicByCode(partnerCode, 0L);
                if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResult())) {
                    return response.getResult().get(0).getCompanyName();
                }
            }
            // 如果为空则默认为我来贷资金方
            return "我来贷";
        } catch (Exception e) {
            log.error("查询资金方接口异常, partnerCode:{}", partnerCode, e);
        }
        return "";
    }


    private WalletLoanDTO buildLoanDue(WalletLoanVO vo) {
        WalletLoanDTO dto = new WalletLoanDTO();
        BeanUtils.copyProperties(vo,dto);
        dto.setStatus(WalletLoanStatus.getText(dto.getStatus()));
        if(null != vo.getConfirmedAt()){
            dto.setConfirmedAt(DateUtils.parseDate(vo.getConfirmedAt(),DATE_TIME_FORMAT));
        }
        if(null != vo.getDisbursedAt()){
            dto.setDisbursedAt(DateUtils.parseDate(vo.getDisbursedAt(),DATE_TIME_FORMAT));
        }
        if(null != vo.getClosedAt()){
            dto.setConfirmedAt(DateUtils.parseDate(vo.getClosedAt(),DATE_TIME_FORMAT));
        }
        /**
         * 查询是否可以全额结清
         */
        try{
            Response<Boolean> response = iRepaymentDubboService.isAllowEarlySettle(vo.getApplicationId());
            if(response != null && Objects.nonNull(response.getResult())){
                dto.setIsAllowEarlySettle(response.getResult().equals(Boolean.TRUE) ? "是":"否");
            }
        } catch (RuntimeException e){
            log.error("查询是否可以全额结清,异常error:{}",e.getMessage());
        }
        /**
         * 查询资金方
         */
        if (StringUtils.isNotEmpty(dto.getPartnerCode())) {
            try{
                Response<List<CapitalBasicVo>> response = capitalBasicProvider.getCapitalBasicByCode(dto.getPartnerCode(),0L);
                if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResult())){
                    dto.setPartnerName(response.getResult().get(0).getCompanyName());
                }
            }catch (RuntimeException e){
                log.error("查询资金方dubbo接口异常,error:{}",e.getMessage());
            }
        }
        /**
         * 查询全额结清金额
         */

        boolean isSettled = queryIsSettled(vo.getApplicationId());
        if (StringUtils.isNotEmpty(dto.getPartnerCode()) && !isSettled) {
            try{
                RepaymentCalculateDTO calculateDTO = new RepaymentCalculateDTO();
                calculateDTO.setUserId(dto.getUserId().toString());
                calculateDTO.setAction("YB");
                calculateDTO.setApplicationId(dto.getApplicationId());
                calculateDTO.setRepaymentDate(new Date());
                Response<RepaymentFeeItemVO> itemVo = walletRepaymentCalculationDubboService.getRepaymentFeeItems(calculateDTO);
                log.info("buildLoanDue#itemVo:{}", JSONObject.toJSONString(itemVo));
                if(Objects.nonNull(itemVo) && Objects.nonNull(itemVo.getResult())){
                    dto.setSettleAllAmount(itemVo.getResult().getAmount());
                }
            }catch (RuntimeException e){
                log.warn("查询全额结清,异常error:{}",e.getMessage());
            }
        }

        // 查询银行卡号
        try {
            WalletOrderVO order = walletPaymentOrderDubboService.findWalletOrderByApplicationId(dto.getApplicationId());
            dto.setElecCardNo(order.getElecCardNo());
        } catch (Exception e){
            log.warn("buildLoanDue 查询钱包银行卡号异常", e);
        }

        return dto;
    }


    private WalletDueDTO buildDueVo(List<WalletDueVO> dueVos,String state) {
        WalletDueDTO dueVo = new WalletDueDTO();
        //数据类型装换
        List<WalletDueSettleDTO> settlementVos = dueVos.stream().map(d -> {
            WalletDueSettleDTO dto = new WalletDueSettleDTO();
            dto.setDueType(RepaymentPlanType.getText(d.getDueType()));
            dto.setIndexNo(d.getIndexNo());
            dto.setDueAmount(d.getAmount());
            if(d.getSettledAt() != null){
                dto.setSettledTime(DateUtils.parseDate(d.getSettledAt(),DATE_TIME_FORMAT));
            }
            dto.setSettledAmount(d.getSettledAmount());
            dto.setOutstandingAmount(d.getOutstandingAmount());
            //还款状态
            if (d.getAmount().compareTo(BigDecimal.ZERO) == 0
                    || (d.getAmount().subtract(d.getSettledAmount())).compareTo(BigDecimal.ZERO) == 0
                    || WalletLoanStatus.CLOSED.getText().equals(state)
                    || WalletLoanStatus.EARLYSETTLED.getText().equals(state)
                    || WalletLoanStatus.CANCELLED.getText().equals(state)) {
                //已还清
                dto.setState(DueState.SETTLED.getText());
            } else if (Math.max(0, (int) DateUtils.daysUntilCurrentDate(d.getDueDate())) > 0) {
                //逾期
                dto.setState(DueState.OVERDUE.getText());
            } else {
                //已还的期数
                dto.setState(DueState.OPEN.getText());
            }
            return dto;
        }).collect(Collectors.toList());
        //当前期数对应列表
        dueVo.setDetail(settlementVos);
        return dueVo;
    }

}
