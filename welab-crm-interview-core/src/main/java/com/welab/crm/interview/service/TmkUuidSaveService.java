package com.welab.crm.interview.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.welab.crm.interview.domain.TmkInterceptHistory;
import com.welab.crm.interview.domain.TmkUuid;

public interface TmkUuidSaveService extends IService<TmkUuid> {

    /**
     * 单条更新拦截历史表数据
     */
    void updateInterceptHistoryById(TmkInterceptHistory history);

    /**
     * 单条插入拦截历史表数据
     */
    void insertInterceptHistory(TmkInterceptHistory history);
}
