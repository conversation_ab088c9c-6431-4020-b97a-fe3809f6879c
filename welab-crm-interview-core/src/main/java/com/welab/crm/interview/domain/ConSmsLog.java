package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 短信发送历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_sms_log")
public class ConSmsLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 短信模板表id
     */
    private Long messageId;

    /**
     * 短信内容，冗余字段，便于查询
     */
    private String context;

    /**
     * 短信模板代码
     */
    private String smsCode;

    /**
     * 短信接收人的手机号
     */
    private String mobile;

    /**
     * 发送状态
     */
    private String sendStatus;

    /**
     * 期望发送时间
     */
    private Date sendTime;

    /**
     * 实际发送时间
     */
    private Date actualSendTime;

    /**
     * 短信内容中要替换的内容
     */
    private String replaceField;

    /**
     * 发送异常说明
     */
    private String failDescription;

    /**
     * 员工id，关联sys_auth_crm_staff表
     */
    private String createStaffId;

    /**
     * 客户是否收到短信
     */
    private Boolean received;

    /**
     * 发送短信接口返回的主键
     */
    private String rspMsgid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


    /**
     * 客户Id
     */
    private Long customerId;


    /**
     * 合同发送表id
     */
    private Long contractSendId;


}
