package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.welab.common.response.Response;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.interview.domain.SettleProofApplyRecord;
import com.welab.crm.interview.mapper.SettleProofApplyRecordMapper;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.service.LhSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LhSettlementServiceImpl implements LhSettlementService {


    @Value("${lhbank.http.get.url}")
    private String lhBankUrl;

    @Resource
    private SettleProofApplyRecordMapper settleProofApplyRecordMapper;

    @Resource
    private IUploadService uploadService;

    @Override
    public void getLhSettlement() {
        // 查询申请成功，但是文件路径为空的蓝海结清证明记录
        List<SettleProofApplyRecord> list = settleProofApplyRecordMapper.selectSuccessButNoFileLhRecord();
        if (CollectionUtils.isNotEmpty(list)) {
            for (SettleProofApplyRecord settleRecord : list) {
                String filePath = getLhFileByApplicationId(settleRecord.getApplicationId());
                if (StringUtils.isNotBlank(filePath)){
                    settleRecord.setFilePath(filePath);
                    settleProofApplyRecordMapper.updateById(settleRecord);
                }
            }
        }
    }

    /**
     * 获取文件下载路径
     *
     * @param applicationId
     * @return
     */
    private String getLhFileByApplicationId(String applicationId) {

        try {
            HashMap<String, Object> map = new HashMap<>();
            map.put("applicationId", applicationId);
            String result = HttpClientUtil.post(lhBankUrl, map);
            Response response = JSON.parseObject(result, Response.class);
            if (Response.isSuccess(response)) {
                log.info("获取蓝海结清证明成功,applicationId:{}", applicationId);
                byte[] bytes = new BASE64Decoder().decodeBuffer((String) response.getResult());
                // 上传到oss
                Response<String> uploadResponse = uploadService.uploadFile(bytes, applicationId + "结清证明.pdf");
                if (Response.isSuccess(uploadResponse)) {
                    log.info("蓝海结清证明上传oss成功, applicationId:{}", applicationId);
                    String fileName = uploadResponse.getResult();
                    Map<String, Object> filePathMap = uploadService.getUploadFile(Arrays.asList(fileName));
                    // 返回文件oss下载路径
                    return (String) filePathMap.get(fileName);
                }

            } else {
                log.warn("getLhFileByApplicationId 获取蓝海结清证明失败, applicationId:{}, response:{}", applicationId, JSON.toJSON(response));
            }

        } catch (Exception e) {
            log.error("getLhFileByApplicationId，异常, applicationId:" + applicationId, e);
        }
        return null;
    }
}
