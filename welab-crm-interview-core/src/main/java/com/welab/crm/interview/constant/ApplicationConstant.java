package com.welab.crm.interview.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @Title
 * @description
 * <AUTHOR>
 * @date 2021/3/9
 * @version v1.0
 */
public class ApplicationConstant {

	//数据源
	public static final String PRIMARY_DATA_SOURCE_PREFIX = "mysql.csp.";

	public static final String THUMB = "thumb"; //缩略图

	public static final String INIT_APPROVAL = "init_aip";    //初审

	public static final String APPLIED = "applied";            //已申请

	public static final String PRETRIAL_APPLIED = "PRETRIAL_APPLIED"; //预审

	public static final String ENCODING = "utf-8";

	//运营uuid外呼
	public static final String YYUU = "YYUU";

	/**
	 * 号码检测可推送到过河兵的状态
	 */
	public static final List<String> CHECK_NORMAL_STATUS = Arrays
		.asList("NORMAL", "SHUTDOWN", "POWER_OFF", "BUSY", "CALL_FORWARDING");

	public static final String SOURCE_CHANNEL = "kefu";

	/**
	 * 94AI外呼记录推送
	 */
	public static final String GET_AI_TASK_CALL = "/GetAiTaskCallList";

	/**
	 * 定时任务在zookeeper上的命名空间(可与elasticJob.xml的配置保持相同)
	 */
	public static final String JOB_NAMESPACE = "welab-crm-interview-job";

	/**
	 * ai外呼推送job名称前缀
	 */
	public static final String PREFIX_JOB = "AiPushJob-";


}
