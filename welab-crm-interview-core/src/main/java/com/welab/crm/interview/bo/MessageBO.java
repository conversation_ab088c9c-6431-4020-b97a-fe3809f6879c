package com.welab.crm.interview.bo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.service.IRepaymentForWebService;
import com.welab.collection.interview.vo.repay.ProxyInteractionRequestByCollection;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.constant.SmsKeyConstant;
import com.welab.crm.interview.domain.*;
import com.welab.crm.interview.dto.message.*;
import com.welab.crm.interview.enums.SmsSendStatusEnum;
import com.welab.crm.interview.enums.ValidFaceTypeEnum;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.*;
import com.welab.crm.interview.util.Md5Util;
import com.welab.crm.interview.util.SerialNoUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.message.MessageHistoryVO;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.crm.interview.vo.message.ShortLinkReturnVO;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.exception.FastRuntimeException;
import com.welab.message.sms.response.ResponsCodeTypeEnum;
import com.welab.message.sms.response.Response;
import com.welab.message.sms.service.MessageSendV4Service;
import com.welab.privacy.util.http.HttpClients;
import com.welab.security.util.MD5Util;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/10
 */
@Slf4j
@Component
public class MessageBO {

    @Resource
    private OpMessageConfMapper messageConfMapper;

    @Resource
    private MessageSendV4Service messageSendV4Service;

    @Resource
    private ConSmsLogMapper conSmsLogMapper;

    @Resource
    private ConWebotTokenMapper conWebotTokenMapper;

    @Resource
    private StaffService staffService;

    @Resource
    private JedisCommands jedisCommands;

    @Resource
    private ProfileService profileService;

    @Resource
    private DataCustomerMapper dataCustomerMapper;

    @Resource
    private IRepaymentForWebService iRepaymentForWebService;

    @Resource
    private CsVideoCheckMapper csVideoCheckMapper;
    
    @Resource
    private ContractSendRecordMapper contractSendRecordMapper;
    @Value("${sms.url}")
    private String smsUrl;

    /**
     * 客服短信useId
     */
    @Value("${sms.userId}")
    private String smsUserId;

    /**
     * 营销短信userId
     */
    @Value("${sms.yx.userId}")
    private String smsYxUserId;

    @Value("${sms.secretKey}")
    private String smsSecretKey;

    @Value("${webot.face.url}")
    private String faceUrl;

    @Value("${webot.face.userToken}")
    private String userToken;

    @Value("${webot.face.appKey}")
    private String appKey;

    /**
     * 客服视频URL
     */
    @Value("${video.sp.url}")
    private String spUrl;
    
    @Value("${user.modify.name.original.url}")
    private String userModifyNameOriginalUrl;
    
    @Value("${short.link.get.url}")
    private String shortLinkGetUrl;
    
    @Value("${short.link.expiration}")
    private Integer shortLinkExpiration;
    
    @Value("${kf.send.contract.url}")
    private String contactUrl;
    
    @Value("${contract.url.expiration.minute}")
    private Integer contactExpirationMinute;

    /**
     * 客服系统标识
     */
    private static final String APP_TAGS = "KF.kefu";
    /**
     * 营销短信类型
     */
    private static final String YX_SMS_TYPE = "smsType_yx";

    /**
     * 客服短信类型
     */
    private static final String KF_SMS_TYPE = "smsType_kf";

    /**
     * 用户修改姓名短信模板
     */
    private static final String USER_MODIFY_NAME_TEMPLATE_CODE= "xyfq_xg_xm";
    
    
    private static final String SEND_CONTRACT_TEMPLATE_CODE = "kf-fsjkht";

    /**
     * 短链有效时间。单位：分钟
     */
    private static final Integer SHORT_LINK_EXPIRATION_TIME = 720;

    public Long addMessageTemplate(MessageTemplateAddDTO dto) {
        OpMessageConf conf = new OpMessageConf();
        BeanUtils.copyProperties(dto, conf);
        Response<String> response = messageSendV4Service.getContentByTemplateName(dto.getSmsCode());
        if (Objects.isNull(response) || StringUtils.isBlank(response.getResult())) {
            throw new CrmInterviewException("短信模板代码不存在");
        }
        if (dto.getTitle().length() > 64) {
            throw new CrmInterviewException("模板标题过长");
        }
        conf.setContext(response.getResult());
        messageConfMapper.insert(conf);
        return conf.getId();
    }

    public void updateMessageTemplate(MessageTemplateUpdateDTO dto) {
        OpMessageConf conf = new OpMessageConf();
        BeanUtils.copyProperties(dto, conf);
        messageConfMapper.updateById(conf);
    }

    public void deleteMessageTemplate(Long id) {
        messageConfMapper.deleteById(id);
    }

    public com.welab.xdao.context.page.Page<MessageTemplateVO> getMessageTemplateList(MessageTemplateQueryDTO dto) {
        IPage<OpMessageConf> page = new Page<>(dto.getCurrentPage(), dto.getRowsPerPage());
        page = messageConfMapper.selectPage(page, buildWrapper(dto));
        List<MessageTemplateVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            MessageTemplateVO templateVO = null;
            for (OpMessageConf messageConf : page.getRecords()) {
                templateVO = new MessageTemplateVO();
                BeanUtils.copyProperties(messageConf, templateVO);
                list.add(templateVO);
            }
        }
        return com.welab.xdao.context.page.Page.initPage(list,
                (int) page.getTotal(),
                (int) page.getSize(),
                (int) page.getCurrent());
    }

    @Transactional(rollbackFor = Exception.class)
    public String sendMessage(MessageSendDTO dto) {
        if (Objects.isNull(dto)) {
            throw new CrmInterviewException("MessageSendDTO不能为空");
        }
        if (Objects.isNull(dto.getStaffId())) {
            throw new CrmInterviewException("员工id不能为空");
        }
        ConSmsLog smsLog = new ConSmsLog();
        smsLog.setMessageId(dto.getMessageId());
        smsLog.setMobile(dto.getMobile());
        OpMessageConf conf = messageConfMapper.selectById(dto.getMessageId());
        smsLog.setContext(conf.getContext());
        smsLog.setSmsCode(conf.getSmsCode());
        smsLog.setCreateStaffId(dto.getStaffId());
        smsLog.setSendTime(DateUtil.stringToDate(dto.getSendTime()));
        smsLog.setSendStatus(SmsSendStatusEnum.READY.getValue());
        smsLog.setCustomerId(dto.getCustomerId());

        if ("kf_tsz_sp".equals(conf.getSmsCode())) {
            // 生成随机token
            String token = getRandomUuid();
            smsLog.setReplaceField(fillTokenAndOthers(dto, token));
            // 插入短信发送记录
            conSmsLogMapper.insert(smsLog);
            // 插入视频验证记录
            try {
                addCsVideoCheck(token, smsLog, dto);
            } catch (Exception e){
                log.warn("addCsVideoCheck 异常", e);
                smsLog.setSendStatus(SmsSendStatusEnum.FAILURE.getValue());
                smsLog.setFailDescription("插入视频验证记录异常");
                conSmsLogMapper.updateById(smsLog);
                return e.getMessage();
            }
            return sendMessageToSms(smsLog, querySmsCodeTypeMap());
        }
        // 如果不是人脸验证短信模板固定的code，直接插入，然后等待定时器SmsSendJob的推送
        if (!"kf-rlsb-qrbr".equals(conf.getSmsCode())) {
            smsLog.setReplaceField(buildReplaceFieldJson(dto, null));
            if (MapUtils.isNotEmpty(dto.getOtherParams())) {
                String contractSendId = dto.getOtherParams().getOrDefault("contractSendId", null);
                if (Objects.nonNull(contractSendId)) {
                    smsLog.setContractSendId(Long.valueOf(contractSendId));
                }
            }
            conSmsLogMapper.insert(smsLog);
            return null;
        } else {
            String url;
            ConWebotToken conEntity = constructConWebotUrl(dto.getMobile(), dto.getLoginName());
            try {

                // 使用customerId对应的手机号获取验证链接，不使用接收短信的手机号
                DataCustomer dataCustomer = dataCustomerMapper.selectById(dto.getCustomerId());
                if (Objects.nonNull(dataCustomer) && StringUtils.isNotBlank(dataCustomer.getMobile())) {
                    url = getValidUrl(dataCustomer.getMobile());
                } else {
                    // 如果是人脸验证短信模板则先从创研获取验证url并保存下来，然后发短信
                    url = getValidUrl(dto.getMobile());
                }

                conEntity.setToken(url.split("token=")[1]);
            } catch (Exception e) {
                conEntity.setResultCode("1");
                conEntity.setResultMessage(e.getMessage());
                // 记录短信发送失败历史
                smsLog.setSendStatus(SmsSendStatusEnum.FAILURE.getValue());
                smsLog.setFailDescription("获取人脸验证链接失败");
                conSmsLogMapper.insert(smsLog);
                return e.getMessage();
            } finally {
                // 记录获取token的过程
                conWebotTokenMapper.insert(conEntity);
            }
            smsLog.setReplaceField(buildReplaceFieldJson(dto, url));
            conSmsLogMapper.insert(smsLog);
            Map<String, String> smsCodeTypeMap = querySmsCodeTypeMap();
            return sendMessageToSms(smsLog, smsCodeTypeMap);
        }
    }

    private void addCsVideoCheck(String token, ConSmsLog smsLog, MessageSendDTO dto) {
        CsVideoCheck csVideoCheck = new CsVideoCheck();
        csVideoCheck.setCustomerId(smsLog.getCustomerId());
        csVideoCheck.setMobile(smsLog.getMobile());
        csVideoCheck.setCustomerName(dto.getName());
        csVideoCheck.setStaffId(Long.valueOf(dto.getStaffId()));
        csVideoCheck.setToken(token);
        csVideoCheck.setSmsLogId(smsLog.getId());
        DataCustomer dataCustomer = dataCustomerMapper.selectById(dto.getCustomerId());
        if (Objects.isNull(dataCustomer)){
            log.warn("addCsVideoCheck 客服系统该客户不存在,customerId:{},mobile:{}", dto.getCustomerId(), dto.getMobile());
            throw new FastRuntimeException("客服系统不存在该客户");
        }
        csVideoCheck.setUserId(Math.toIntExact(dataCustomer.getUserId()));
        csVideoCheck.setUuid(dataCustomer.getUuid());
        Profile profile = profileService.getProfileByUserId(Math.toIntExact(dataCustomer.getUserId()));
        if (Objects.isNull(profile)){
            log.warn("addCsVideoCheck 客服profile不存在,customerId:{},mobile:{},userId:{}", dto.getCustomerId(), dto.getMobile(),dataCustomer.getUserId());
            throw new FastRuntimeException("客户信息在消金不存在");

        }
        Map<String, String> map = new HashMap<>();
        map.put("name",dto.getName());
        map.put("cnid",profile.getCnid());
        map.put("mobile", smsLog.getMobile());
        csVideoCheck.setReadMsg(StringUtil.replacePlaceholders(dto.getReadMsg(),map));
        csVideoCheckMapper.insert(csVideoCheck);
    }

    private String fillTokenAndOthers(MessageSendDTO dto, String token) {
        // token存入redis
        jedisCommands.set(SmsKeyConstant.REDIS_PRE_TOKEN_KEY + token,SmsKeyConstant.LINK_COUNT);
        jedisCommands.expire(SmsKeyConstant.REDIS_PRE_TOKEN_KEY + token,SmsKeyConstant.TOKEN_VALID_TIME);

        jedisCommands.set(SmsKeyConstant.REDIS_PRE_UPLOAD_KEY + token, SmsKeyConstant.LINK_COUNT);
        jedisCommands.expire(SmsKeyConstant.REDIS_PRE_UPLOAD_KEY + token,SmsKeyConstant.TOKEN_VALID_TIME);

        return buildReplaceFieldJson(dto, spUrl, token);
    }

    private String getRandomUuid() {
        // 生成随机的 UUID
        UUID uuid = UUID.randomUUID();
        String token = uuid.toString().replace("-", "");

        // 截取指定长度的字符串作为 token
        if (token.length() > SmsKeyConstant.TOKEN_LENGTH) {
            token = token.substring(0, SmsKeyConstant.TOKEN_LENGTH);
        }

        return token;
    }

    private String getValidUrl(String mobile) {
        String responseStr;
        try {
            Map<String, Object> paramMap = getValidateParamMap(mobile);
//            responseStr = HttpClients.create().setUrl(faceUrl + "/link")
//                    .addHeader("Content-Type", "application/json; charset=utf-8")
//                    .addURLParams(paramMap).doGet();
            responseStr = HttpClientUtil.get(faceUrl + "/link", paramMap);
        } catch (Exception e) {
            log.error("sendMessage.getValidUrl error, mobile-{}, error: {}", mobile, e.getMessage(), e);
            throw new CrmInterviewException("获取验证链接:接口调用发生错误");
        }
        ResponseVO<String> response = JSONObject.parseObject(responseStr, ResponseVO.class);
        if (response == null || response.getRet() != 0) {
            log.warn("获取验证链接接口返回错误: mobile-{}, {}", mobile, response == null ? "response空" : response);
            throw new CrmInterviewException("获取验证链接接口返回错误:" + (response == null ? "response null" : response.getMsg()));
        } else if (StringUtils.isBlank(response.getData())) {
            log.warn("获取验证链接接口返回的data字段是空值: mobile-{}, {}", mobile, response);
            throw new CrmInterviewException("获取验证链接接口返回的data字段是空值");
        } else {
            return response.getData();
        }
    }

    private ConWebotToken constructConWebotUrl(String mobile, String staffId) {
        ConWebotToken url = new ConWebotToken();
        url.setCreateUser(staffId);
        url.setGmtCreate(new Date());
        url.setResultCode("0");
        url.setResultMessage("获取成功");
        url.setValidateType(ValidFaceTypeEnum.SMS.getValue());
        url.setMobile(mobile);
        return url;
    }

    /**
     * 找到待发送的短信，并发送出去
     */
    public void sendMessage() {
        List<ConSmsLog> logs = conSmsLogMapper.selectList(Wrappers.lambdaQuery(ConSmsLog.class)
                .eq(ConSmsLog::getSendStatus, SmsSendStatusEnum.READY.getValue()));
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        Map<String, String> smsCodeTypeMap = querySmsCodeTypeMap();
        for (ConSmsLog smsLog : logs) {
            if (isSendTime(smsLog.getSendTime())) {
                sendMessageToSms(smsLog, smsCodeTypeMap);
            }
        }
    }

    /**
     * 组装参数向短信系统发短信
     */
    private String sendMessageToSms(ConSmsLog smsLog, Map<String, String> smsCodeTypeMap) {
        try {
            SmsReqDTO smsReqDTO = new SmsReqDTO();
            String mobile = smsLog.getMobile();
            String reqTime = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = mobile + smsSecretKey + reqTime;
            sign = Md5Util.md5(sign).toUpperCase();
            smsReqDTO.setMobile(mobile);
            smsReqDTO.setAppTags(APP_TAGS);
            smsReqDTO.setReqTime(reqTime);
            smsReqDTO.setSign(sign);
            if (YX_SMS_TYPE.equals(smsCodeTypeMap.get(smsLog.getSmsCode()))) {
                smsReqDTO.setUserId(smsYxUserId);
            } else {
                smsReqDTO.setUserId(smsUserId);
            }
            smsReqDTO.setTemplateName(smsLog.getSmsCode());
            String params = smsLog.getReplaceField();
            Map<String, String> map = JSONObject.parseObject(params, Map.class);
            //h5还款链接因为时效原因改为发送时获取url
            if (!org.springframework.util.CollectionUtils.isEmpty(map) && map.containsKey("repayLink")) {
                ProxyInteractionRequestByCollection request = new ProxyInteractionRequestByCollection();
                request.setAmount(new BigDecimal(map.get("amount")));
                request.setApplicationId(map.get("applicationId"));
                request.setRepaymentType(map.get("repaymentType"));
                request.setRepayMethod(map.get("repayMethod"));
                String result = iRepaymentForWebService.getProxyRepayLinkV2(request).getResult();
                String[] urlSplit = result.split("/");
                map.put("repayLink", result);
                map.put("repayCmd", urlSplit[urlSplit.length - 1]);
                //补全替换参数
                smsLog.setReplaceField(JSONObject.toJSONString(map));
                log.info("repayLink detail:{}", JSONObject.toJSONString(map));
            }
            if (USER_MODIFY_NAME_TEMPLATE_CODE.equals(smsLog.getSmsCode())){
                map.put("url", getShortLink(userModifyNameOriginalUrl, shortLinkExpiration));
                smsLog.setReplaceField(JSON.toJSONString(map));
            }
            
            if (SEND_CONTRACT_TEMPLATE_CODE.equals(smsLog.getSmsCode())){
                map.put("contract_url", getShortLink(contactUrl + "?id=" + smsLog.getContractSendId(), contactExpirationMinute));
                smsLog.setReplaceField(JSON.toJSONString(map));
            }
            smsReqDTO.setParams(map);
//            String responseStr = HttpClients.create().setUrl(smsUrl + "/open/sms/signSend")
//                    .addHeader("Content-Type", "application/json; charset=utf-8")
//                    .setRequestBody(JSONObject.toJSONString(smsReqDTO)).doPost();
            String responseStr = HttpClientUtil
                    .postJson(smsUrl + "/open/sms/signSend", JSONObject.toJSONString(smsReqDTO));
            Response<String> response = JSONObject.parseObject(responseStr, Response.class);
            if (Objects.isNull(response)) {
                smsLog.setSendStatus(SmsSendStatusEnum.FAILURE.getValue());
                smsLog.setFailDescription("短信发送接口异常，返回为null");
            } else if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                smsLog.setActualSendTime(new Date());
                smsLog.setRspMsgid(response.getResult());
                smsLog.setSendStatus(SmsSendStatusEnum.SUCCESS.getValue());
                smsLog.setFailDescription("短信发送成功");
            } else {
                smsLog.setSendStatus(SmsSendStatusEnum.FAILURE.getValue());
                smsLog.setFailDescription(response.getMessage());
            }
        } catch (Exception e) {
            log.error("sendMessage error: {}", smsLog.toString(), e);
            smsLog.setSendStatus(SmsSendStatusEnum.FAILURE.getValue());
            smsLog.setFailDescription(e.getMessage());
        }
        smsLog.setGmtModify(new Date());
        conSmsLogMapper.updateById(smsLog);
        if (Objects.nonNull(smsLog.getContractSendId())){
            updateContractSendStatus(smsLog.getContractSendId(), smsLog.getSendStatus());
        }

        if (!SmsSendStatusEnum.SUCCESS.getValue().equals(smsLog.getSendStatus())) {
            return smsLog.getFailDescription();
        } else {
            return null;
        }
    }

    private void updateContractSendStatus(Long contractSendId, String sendStatus) {
        ContractSendRecord updateRecord = new ContractSendRecord();
        if (SmsSendStatusEnum.SUCCESS.getValue().equals(sendStatus)){
            updateRecord.setStatus(3);
        } else {
            updateRecord.setStatus(4);
        }
        updateRecord.setGmtModify(new Date());
        updateRecord.setSendTime(new Date());
        contractSendRecordMapper.update(updateRecord, Wrappers.lambdaUpdate(ContractSendRecord.class).eq(ContractSendRecord::getId, contractSendId));
    }

    private String getShortLink(String userModifyNameOriginalUrl, Integer expirationMinutes) {
        try {
            JSONObject params = new JSONObject();
            params.put("url", userModifyNameOriginalUrl);
            params.put("expiration", expirationMinutes);
            String res = HttpClientUtil.postJson(shortLinkGetUrl, JSON.toJSONString(params));
            ShortLinkReturnVO shortLinkReturnVO = JSON.parseObject(res, new TypeReference<ShortLinkReturnVO>() {
            });
            if (Objects.nonNull(shortLinkReturnVO) && shortLinkReturnVO.getSuccess()) {
                return shortLinkReturnVO.getResult().getUrl();
            } else {
                log.warn("getShortLink fail, res:{}", res);
                throw new FastRuntimeException("获取短链失败");
            }
        } catch (Exception e) {
            log.warn("getShortLink error", e);
            throw new FastRuntimeException("获取短链失败");
        }
    }

    public void sendSms(String mobile,String smsCode,Map<String,String> params){
        try {
            SmsReqDTO smsReqDTO = new SmsReqDTO();
            String reqTime = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = mobile + smsSecretKey + reqTime;
            sign = Md5Util.md5(sign).toUpperCase();
            smsReqDTO.setMobile(mobile);
            smsReqDTO.setAppTags(APP_TAGS);
            smsReqDTO.setReqTime(reqTime);
            smsReqDTO.setSign(sign);
            smsReqDTO.setUserId(smsUserId);
            smsReqDTO.setTemplateName(smsCode);
            smsReqDTO.setParams(params);
//            String responseStr = HttpClients.create().setUrl(smsUrl + "/open/sms/signSend")
//                    .addHeader("Content-Type", "application/json; charset=utf-8")
//                    .setRequestBody(JSONObject.toJSONString(smsReqDTO)).doPost();
            String responseStr = HttpClientUtil.postJson(smsUrl + "/open/sms/signSend", JSONObject.toJSONString(smsReqDTO));
            Response<String> response = JSONObject.parseObject(responseStr, Response.class);
            if (Objects.isNull(response)) {
                log.warn("sendSms 短信发送接口异常，返回为null");
                throw new FastRuntimeException("短信发送失败");
            } else if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                log.info("sendSms 短信发送成功");
            } else {
                log.warn("sendSms 短信发送失败,res:{}", JSON.toJSONString(response));
                throw new FastRuntimeException("短信发送失败");
            }
        } catch (Exception e) {
            log.error("sendSms error " + mobile + " " + smsCode, e);
            throw new FastRuntimeException("短信发送失败");
        }
    }

    /**
     * 获取 短板模板编码:短信类型 map
     */
    public Map<String, String> querySmsCodeTypeMap() {
        List<OpMessageConf> confList = messageConfMapper.selectList(
                Wrappers.lambdaQuery(OpMessageConf.class).select(OpMessageConf::getSmsCode, OpMessageConf::getSmsType));
        return confList.stream().collect(Collectors.toMap(OpMessageConf::getSmsCode, OpMessageConf::getSmsType));
    }

    /**
     * 确认客户是否收到短信
     */
    public void confirmReceived() {
        Date now = new Date();
        List<ConSmsLog> logs = conSmsLogMapper.selectList(Wrappers.lambdaQuery(ConSmsLog.class)
                .eq(ConSmsLog::getSendStatus, SmsSendStatusEnum.SUCCESS.getValue())
                .eq(ConSmsLog::getReceived, false)
            .between(ConSmsLog::getSendTime, DateUtil.plusDays(now, -5), now));
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        Map<String, String> smsCodeTypeMap = querySmsCodeTypeMap();

        for (ConSmsLog smsLog : logs) {
            try {
                SmsQueryReportDTO smsQueryReportDTO = new SmsQueryReportDTO();
                String mobile = smsLog.getMobile();
                String reqTime = String.valueOf(smsLog.getSendTime().getTime() / 1000);
                String sign = mobile + smsSecretKey + reqTime;
                sign = Md5Util.md5(sign).toUpperCase();
                smsQueryReportDTO.setMobile(mobile);
                smsQueryReportDTO.setReqTime(reqTime);
                smsQueryReportDTO.setRspMsgid(smsLog.getRspMsgid());
                if (YX_SMS_TYPE.equals(smsCodeTypeMap.get(smsLog.getSmsCode()))) {
                    smsQueryReportDTO.setUserId(smsYxUserId);
                } else {
                    smsQueryReportDTO.setUserId(smsUserId);
                }
                smsQueryReportDTO.setSign(sign);
//                String responseStr = HttpClients.create().setUrl(smsUrl + "/open/sms/kfdxQueryReport")
//                        .addHeader("Content-Type", "application/json; charset=utf-8")
//                        .setRequestBody(JSONObject.toJSONString(smsQueryReportDTO)).doPost();
                String responseStr = HttpClientUtil.postJson(smsUrl + "/open/sms/kfdxQueryReport", JSONObject.toJSONString(smsQueryReportDTO));
                Response<Integer> response = JSONObject.parseObject(responseStr, Response.class);
                if (Objects.nonNull(response)) {
                    if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode())) {
                        if (response.getResult() > 0) {
                            smsLog.setReceived(true);
                            smsLog.setGmtModify(new Date());
                            conSmsLogMapper.updateById(smsLog);
                        }
                    } else {
                        throw new CrmInterviewException(response.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("confirmReceived error. {}", smsLog.toString(), e);
            }
        }
    }

    public List<MessageHistoryVO> getMessageHistory(String mobile, Long customerId) {
        if (StringUtils.isNotBlank(mobile)) {
            List<ConSmsLog> logs = conSmsLogMapper.listLogs(mobile, customerId);
            if (CollectionUtils.isNotEmpty(logs)) {
                List<String> staffIds = logs.stream().map(ConSmsLog::getCreateStaffId).collect(Collectors.toList());
                List<StaffVO> staffs = staffService.getStaffById(staffIds);
                Map<String, String> staffNames = staffs.stream()
                        .collect(Collectors.toMap(k -> k.getId().toString(), StaffVO::getStaffName, (k1, k2) -> k2));
                return logs.stream().map(log -> {
                    MessageHistoryVO vo = new MessageHistoryVO();
                    BeanUtils.copyProperties(log, vo);
                    vo.setSendStatus(SmsSendStatusEnum.getText(log.getSendStatus()));
                    vo.setStaffName(staffNames.get(log.getCreateStaffId()));
                    vo.setId(SerialNoUtil.serialNo());
                    vo.setReceived(log.getReceived() ? "是" : "否");
                    if (StringUtils.isNotBlank(log.getReplaceField())) {
                        Map<String, String> map = JSONObject.parseObject(log.getReplaceField(), Map.class);
                        StringSubstitutor sub = new StringSubstitutor(map);
                        vo.setContext(sub.replace(log.getContext()));
                        if (!org.springframework.util.CollectionUtils.isEmpty(map) && map.containsKey("repayMethod")) {
                            vo.setRepayMethod(map.get("repayMethod"));
                        }
                    } else {
                        vo.setContext(log.getContext());
                    }
                    return vo;
                }).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    public ResponseVO<List<ResponseData>> listFaceValidateRecords(String mobile) {
        String responseStr;
        try {
            Map<String, Object> paramMap = getValidateParamMap(mobile);
//            responseStr = HttpClients.create().setUrl(faceUrl + "/list")
//                    .addHeader("Content-Type", "application/json; charset=utf-8")
//                    .addURLParams(paramMap).doGet();
            responseStr = HttpClientUtil.get(faceUrl + "/list", paramMap);
        } catch (Exception e) {
            log.error("listFaceValidateRecords error, mobile-{}, error: {}", mobile, e.getMessage(), e);
            throw new CrmInterviewException("核验记录查询接口调用发生错误");
        }
        ResponseVO<List<ResponseData>> response = JSON.parseObject(responseStr,
                new TypeReference<ResponseVO<List<ResponseData>>>() {
                });

        if (response == null || response.getRet() != 0) {
            log.warn("核验记录查询接口返回错误: mobile-{}, {}", mobile, response == null ? "response空" : response);
            throw new CrmInterviewException("核验记录查询接口返回错误");
        } else {
            return response;
        }
    }

    private Map<String, Object> getValidateParamMap(String mobile) {
        Map<String, Object> paramMap = new HashMap<>();
        // 授权认证参数封装
        String flowCode = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis();
        paramMap.put("flowCode", flowCode);
        paramMap.put("timestamp", timestamp + "");
        paramMap.put("sign", MD5Util.md5(appKey + timestamp + flowCode));
        paramMap.put("userToken", userToken);
        // 业务参数
        paramMap.put("mobile", mobile);
        paramMap.put("source", "telephone");
        return paramMap;
    }

    /**
     * 是否到了期望发送时间，当前时间应大于等于期望发送时间
     */
    private boolean isSendTime(Date date) {
        return System.currentTimeMillis() >= date.getTime();
    }

    private Wrapper<OpMessageConf> buildWrapper(MessageTemplateQueryDTO dto) {
        return Wrappers.lambdaQuery(OpMessageConf.class)
                .likeRight(StringUtils.isNotBlank(dto.getTitle()), OpMessageConf::getTitle, dto.getTitle())
                .likeRight(StringUtils.isNotBlank(dto.getSmsCode()), OpMessageConf::getSmsCode, dto.getSmsCode())
                .eq(StringUtils.isNotBlank(dto.getDirectory()), OpMessageConf::getDirectory, dto.getDirectory())
                .eq(StringUtils.isNotBlank(dto.getSmsType()), OpMessageConf::getSmsType, dto.getSmsType())
                .orderByAsc(OpMessageConf::getSort);
    }

    private String buildReplaceFieldJson(MessageSendDTO dto, String url) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SmsKeyConstant.NAME, dto.getName());
        if (StringUtils.isNotBlank(url)) {
            jsonObject.put(SmsKeyConstant.URL, url);
        }
        if (MapUtils.isNotEmpty(dto.getOtherParams())){
            jsonObject.putAll(dto.getOtherParams());
        }
        return jsonObject.toJSONString();
    }


    private String buildReplaceFieldJson(MessageSendDTO dto, String url, String token) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SmsKeyConstant.NAME, dto.getName());
        if (StringUtils.isNotBlank(url)) {
            jsonObject.put(SmsKeyConstant.URL, url);
        }
        if (StringUtils.isNotBlank(token)) {
            jsonObject.put(SmsKeyConstant.TOKEN, token);
        }
        return jsonObject.toJSONString();
    }


    /**
     * 置顶某条短信模板
     * @param templateId 主键id
     */
    public void topTemplate(Long templateId){
        // 查询当前最高sort
        Integer maxSort = messageConfMapper.selectMaxSort();
        OpMessageConf conf = new OpMessageConf();
        conf.setId(templateId);
        conf.setSort(maxSort + 1);
        messageConfMapper.updateById(conf);
    }
}
