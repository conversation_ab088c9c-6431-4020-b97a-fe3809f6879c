package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("contract_send_record")
public class ContractSendRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合同号
     */
    private String appNo;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 0-待审核；1-审核通过；2-审核失败；3-发送成功；4-发送失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 发送组
     */
    private String groupCode;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 资金方编码
     */
    private String partnerCode;

    /**
     * 资金方名称
     */
    private String partnerName;

    /**
     * 1-查看且下载；2-仅查看
     */
    private Integer sendType;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 客户表id
     */
    private Long customerId;

    /**
     * 短信模板id
     */
    private Long messageId;

    /**
     * 发送时间
     */
    private Date sendTime;


}
