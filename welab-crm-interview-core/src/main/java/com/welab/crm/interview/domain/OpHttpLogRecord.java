package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 接口请求记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_http_log_record")
public class OpHttpLogRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户登陆名称
     */
    private String loginName;

    /**
     * 查询用户uuid
     */
    private String uuid;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 请求路径
     */
    private String requestPath;


}
