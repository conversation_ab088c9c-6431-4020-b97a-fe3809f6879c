package com.welab.crm.interview.service.impl;

import com.welab.crm.interview.domain.CsRepayFile;
import com.welab.crm.interview.mapper.CsRepayFileMapper;
import com.welab.crm.interview.service.CsRepayFileService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class CsRepayFileServiceImpl implements CsRepayFileService {

    @Resource
    private CsRepayFileMapper csRepayFileMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepayDomain(CsRepayFile repayFile) {
        csRepayFileMapper.updateById(repayFile);
    }
}
