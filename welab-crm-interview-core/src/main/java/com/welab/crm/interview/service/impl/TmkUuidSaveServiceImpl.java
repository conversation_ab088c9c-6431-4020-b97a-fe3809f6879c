package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.welab.crm.interview.domain.TmkInterceptHistory;
import com.welab.crm.interview.domain.TmkUuid;
import com.welab.crm.interview.mapper.TmkInterceptHistoryMapper;
import com.welab.crm.interview.mapper.TmkUuidMapper;
import com.welab.crm.interview.service.TmkUuidSaveService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class TmkUuidSaveServiceImpl extends ServiceImpl<TmkUuidMapper, TmkUuid> implements TmkUuidSaveService {

    @Resource
    private TmkInterceptHistoryMapper interceptHistoryMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInterceptHistoryById(TmkInterceptHistory history) {
        interceptHistoryMapper.updateById(history);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertInterceptHistory(TmkInterceptHistory history) {
        interceptHistoryMapper.insert(history);
    }
}
