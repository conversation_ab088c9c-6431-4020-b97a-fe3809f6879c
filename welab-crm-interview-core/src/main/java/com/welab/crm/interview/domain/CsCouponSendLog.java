package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 卡券发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_coupon_send_log")
public class CsCouponSendLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 业务分类
     */
    private String business;

    /**
     * 卡券id
     */
    private Long couponId;

    /**
     * 活动名称
     */
    private String description;

    /**
     * 面值
     */
    private BigDecimal amount;

    /**
     * 卡劵类型
     */
    private String amountType;

    /**
     * 有效期(单位：天)
     */
    private Integer availableDays;

    /**
     * 客户id
     */
    private Integer userId;

    /**
     * 客户uuid
     */
    private Long uuid;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 发送卡券员工id
     */
    private String createStaffId;

    /**
     * 发送卡券员工id部门code
     */
    private String createStaffGroup;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
