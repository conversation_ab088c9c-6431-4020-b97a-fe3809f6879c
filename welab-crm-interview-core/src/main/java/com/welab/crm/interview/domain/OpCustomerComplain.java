package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户投诉反馈表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_customer_complain")
public class OpCustomerComplain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 逻辑主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 客户编号
     */
    private String custNo;

    /**
     * 机构号
     */
    private String org;

    /**
     * 催收组编码
     */
    private String groupCode;

    /**
     * 催收员工编码
     */
    private String staffId;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 投诉级别: aNormal-正常, highRisk-高危投诉, complain-投诉
     */
    private String complainLevel;

    /**
     * 需要核实结果: 0-不需要核实,1-需要核实
     */
    private Boolean mustReview;

    /**
     * 需要处理结果: 0-不需要处理,1-需要处理
     */
    private Boolean mustApprove;

    /**
     * 核实结果
     */
    private String reviewResult;

    /**
     * 处理结果
     */
    private String approveResult;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    private Integer approveStatus;

    /**
     * 紧急状态: 0-不紧急,1-紧急
     */
    private Boolean urgeStatus;

    /**
     * 客服工单编号
     */
    private String workOrderNo;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 核实时间
     */
    private Date reviewTime;

    /**
     * 处理时间
     */
    private Date approveTime;

    /**
     * 备注
     */
    private String remark;

}
