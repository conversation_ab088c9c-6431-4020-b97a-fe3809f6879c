package com.welab.crm.interview.service.impl;

import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.ContractSendRecord;
import com.welab.crm.interview.mapper.ContractSendRecordMapper;
import com.welab.crm.interview.service.ContractSendService;
import com.welab.crm.interview.vo.ContractSendVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class ContractSendServiceImpl implements ContractSendService {
	
	@Resource
	private ContractSendRecordMapper contractSendRecordMapper;
	@Override
	public ContractSendVO queryContractSendById(Long id) {
		ContractSendRecord record = contractSendRecordMapper.selectById(id);
		if (record == null) {
			return null;
		}
		
		ContractSendVO contractSendVO = new ContractSendVO();
		BeanUtils.copyProperties(record, contractSendVO);
		contractSendVO.setSendType(String.valueOf(record.getSendType()));
		contractSendVO.setSendTime(DateUtil.dateToString(record.getSendTime()));
		return contractSendVO;
	}
}
