package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 超过24小时未联系的工单通知定时任务
 */
@Slf4j
public class OverTwentyFourHourUnContactOrderNoticeJob implements SimpleJob {


	@Resource
	private WorkOrderService workOrderService;

	@Override
	public void execute(ShardingContext shardingContext) {
		long startTime = System.currentTimeMillis();
		log.info("超过24小时未联系的工单通知定时任务开始执行");
		workOrderService.queryOverTwentyFourHourUnContactOrderAndNotice();
		log.info("超过24小时未联系的工单通知定时任务执行结束, use time: {}ms", System.currentTimeMillis() - startTime);
	}
}
