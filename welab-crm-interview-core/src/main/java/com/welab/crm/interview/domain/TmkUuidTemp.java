package com.welab.crm.interview.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电销UUID临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_uuid_temp")
public class TmkUuidTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 合同号
     */
    private String applicationId;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 号码包定义
     */
    private String packageDefine;

    /**
     * 可用额度
     */
    private BigDecimal avlCredit;

    /**
     * 额度状态
     */
    private String creditStatus;

    /**
     * 是否进件
     */
    private Boolean isIncome;

    /**
     * 是否提现
     */
    private Boolean isWithdrawal;

    /**
     * 身份证
     */
    private String cnid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private String gender;

    /**
     * 信用额度
     */
    private BigDecimal creditLine;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 推送状态：1已推送、2未推送、3被屏蔽
     */
    private String pushFlag;

    /**
     * 屏蔽规则Id
     */
    private Long ruleId;

    /**
     * 号码包创建时间
     */
    private Date packageCreateTime;

    private Date appliedAt;

    private Date confirmedAt;

    /**
     * 名单类型
     * jj:进件类
     * tx:提现类
     */
    private String type;

    /**
     * 号码包id
     */
    private String numPackageId;

    /**
     * 外呼方式: ai-ai外呼, man-人工外呼
     */
    private String callType;
}
