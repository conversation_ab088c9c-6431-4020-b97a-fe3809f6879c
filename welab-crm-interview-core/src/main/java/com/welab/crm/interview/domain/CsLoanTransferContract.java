package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 债转结清关联合同号表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_loan_transfer_contract")
public class CsLoanTransferContract implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 关联债务结清id
     */
    private Long transferId;

    /**
     * 贷款合同号
     */
    private String contractNo;

    /**
     * 推送状态: 0-未推送,1-已推送
     */
    private Boolean pushState;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 处理状态: 0-处理失败 1-处理成功
     */
    private Integer processStatus;

    /**
     * 失败原因
     */
    private String failReason;


}
