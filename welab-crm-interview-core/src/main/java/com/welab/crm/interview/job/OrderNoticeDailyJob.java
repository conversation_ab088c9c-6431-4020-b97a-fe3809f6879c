package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 工单通知每日定时任务
 */
@Slf4j
public class OrderNoticeDailyJob implements SimpleJob {


	@Resource
	private WorkOrderService workOrderService;

	@Override
	public void execute(ShardingContext shardingContext) {
		long startTime = System.currentTimeMillis();
		log.info("工单通知每日定时任务开始执行");
		workOrderService.queryDailyOrderAndNotice();
		log.info("工单通知每日定时任务执行结束, use time: {}ms", System.currentTimeMillis() - startTime);
	}
}
