/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

import com.welab.common.StartServer;

/**
 * @description 项目启动类
 *
 * <AUTHOR> 请修改用户名，例如：dawn.deng
 * @date TODO 请修改时间，例如：2017-12-18 17:45:21
 * @version v1.0
 */

@SpringBootApplication
@ImportResource({ "classpath:/applicationContext.xml" })
public class CrmInterviewServer {
	private static final Logger LOG = LoggerFactory.getLogger(CrmInterviewServer.class);

	public static void main(String[] args) {
		try {
			SpringApplication.run(CrmInterviewServer.class, args);
			new StartServer() {
				@Override
				public void execute() {
				}
			}.start();
		} catch (Exception e) {
			LOG.error("Start FAIL.", e);
		}
	}
}
