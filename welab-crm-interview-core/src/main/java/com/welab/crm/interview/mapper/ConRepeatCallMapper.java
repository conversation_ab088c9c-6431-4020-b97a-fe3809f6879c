package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.ConRepeatCall;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 重复来电表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface ConRepeatCallMapper extends BaseMapper<ConRepeatCall> {

    List<ReportResolvedRateVO> selectCallList(@Param("cond") RepeatCallDTO dto, @Param("list") List<String> hotlines);
}
