package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.service.AiPushService;

import com.welab.crm.interview.service.RepayLinkService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;

/**
 * h5还款链接还款结果同步任务
 * <AUTHOR>
 */
@Slf4j
public class H5RepayLinkRepayResultSyncJob implements SimpleJob {

    @Resource
    private RepayLinkService repayLinkService;


    @Override
    public void execute(ShardingContext shardingContext) {
       repayLinkService.saveRepayResult(new Date());
    }
}
