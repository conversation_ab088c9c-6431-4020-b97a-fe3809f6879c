package com.welab.crm.interview.service.impl;

import com.welab.crm.interview.bo.MessageBO;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.dto.message.MessageTemplateAddDTO;
import com.welab.crm.interview.dto.message.MessageTemplateQueryDTO;
import com.welab.crm.interview.dto.message.MessageTemplateUpdateDTO;
import com.welab.crm.interview.service.MessageService;
import com.welab.crm.interview.vo.message.MessageHistoryVO;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.xdao.context.page.Page;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Service
public class MessageServiceImpl implements MessageService {

    @Resource
    private MessageBO messageBO;

    @Override
    public Long addMessageTemplate(MessageTemplateAddDTO dto) {
        return messageBO.addMessageTemplate(dto);
    }

    @Override
    public void updateMessageTemplate(MessageTemplateUpdateDTO dto) {
        messageBO.updateMessageTemplate(dto);
    }

    @Override
    public void deleteMessageTemplate(Long id) {
        messageBO.deleteMessageTemplate(id);
    }

    @Override
    public Page<MessageTemplateVO> getMessageTemplateList(MessageTemplateQueryDTO dto) {
        return messageBO.getMessageTemplateList(dto);
    }

    @Override
    public void topMessageTemplate(Long templateId) {
        messageBO.topTemplate(templateId);
    }

    @Override
    public String sendMessage(MessageSendDTO dto) {
        return messageBO.sendMessage(dto);
    }

    @Override
    public List<MessageHistoryVO> getMessageHistory(String mobile, Long customerId) {
        return messageBO.getMessageHistory(mobile, customerId);
    }

    @Override
    public ResponseVO<List<ResponseData>> listFaceValidateRecords(String mobile) {
        return messageBO.listFaceValidateRecords(mobile);
    }
}
