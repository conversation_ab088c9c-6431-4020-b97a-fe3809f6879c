package com.welab.crm.interview.constant;

/**
 * Created by Lyman on 2017/1/13.
 */
public class StateCode {

	public static final Integer OK = 0;                 //正常返回数(操作成功)
	public static final Integer NOUSER = 1;             //无效的用户
	public static final Integer ACTIVELOANS = 2;        //用户还有贷款
	public static final Integer ALREADYCANCEL = 3;      //用户已经被注销
	public static final Integer CNID_NOT_MATCHED = 4;   //身份证号不匹配
	public static final Integer NAME_NOT_MATCHED = 5;   //姓名不匹配
	public static final Integer PROFILE_NOT_EXISTS = 6; // 用户基本信息不存在
}
