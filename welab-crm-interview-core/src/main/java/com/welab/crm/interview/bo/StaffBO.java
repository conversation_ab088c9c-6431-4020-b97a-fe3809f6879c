package com.welab.crm.interview.bo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.InAuthCrmStaff;
import com.welab.crm.interview.mapper.InAuthCrmStaffMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class StaffBO {
	
	@Resource
	private InAuthCrmStaffMapper staffMapper;


	private List<InAuthCrmStaff> queryAllStaff() {
		return staffMapper.selectList(null);
	}

	public Map<Long, InAuthCrmStaff> getStaffIdMap() {
		List<InAuthCrmStaff> staffList = queryAllStaff();

		if (staffList.isEmpty()) {
			return Collections.emptyMap();
		}

		return staffList.stream().collect(Collectors.toMap(InAuthCrmStaff::getId, inAuthCrmStaff -> inAuthCrmStaff));

	}
}
