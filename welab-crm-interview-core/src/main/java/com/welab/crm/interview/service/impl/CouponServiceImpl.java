package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.CsCouponSendLog;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.dto.coupon.CouponCardDTO;
import com.welab.crm.interview.dto.coupon.CouponCardSendDTO;
import com.welab.crm.interview.dto.coupon.CouponSendLogDTO;
import com.welab.crm.interview.enums.coupon.CouponBusinessEnum;
import com.welab.crm.interview.enums.coupon.CouponSendingTypeEnum;
import com.welab.crm.interview.enums.coupon.CouponStatusEnum;
import com.welab.crm.interview.enums.coupon.CouponWithdrawStatusEnum;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.CsCouponSendLogMapper;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.CouponService;
import com.welab.crm.interview.util.NumberUtil;
import com.welab.crm.interview.vo.coupon.CouponCardVO;
import com.welab.crm.interview.vo.coupon.CouponSendLogVO;
import com.welab.crm.interview.vo.coupon.CouponVO;
import com.welab.crm.interview.vo.coupon.CouponWithdrawDetailVO;
import com.welab.crm.interview.vo.coupon.CouponWithdrawVO;
import com.welab.marketing.dto.*;
import com.welab.marketing.enums.CouponType;
import com.welab.marketing.enums.RedeemStatus;
import com.welab.marketing.service.CouponV2Service;
import com.welab.marketing.service.wallet.WalletCouponService;
import com.welab.marketing.vo.CouponParam;
import com.welab.marketing.vo.PagedResponse;
import com.welab.xdao.context.page.Page;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Slf4j
@Service
public class CouponServiceImpl implements CouponService {

    @Resource
    private CouponV2Service couponV2Service;

    @Resource
    private WalletCouponService walletCouponService;

    @Resource
    private CsCouponSendLogMapper csCouponSendLogMapper;

    @Resource
    private OpDictInfoMapper dictInfoMapper;

    /**
     * 卡券业务类型：现金贷
     */
    private static final String BUSINESS_TYPE_LOAN = "loan";

    /**
     * 卡券业务类型：钱包
     */
    private static final String BUSINESS_TYPE_WALLET = "wallet";

    /**
     * @see CouponService#getUserCouponList(Long)
     */
    @Override
    public List<CouponVO> getUserCouponList(Long uuid) {
        if (Objects.isNull(uuid)) {
            return new ArrayList<>();
        }
        List<CouponsUserDto> couponUsers = couponV2Service.listCouponsUser(uuid);
        if (CollectionUtils.isEmpty(couponUsers)) {
            return Collections.EMPTY_LIST;
        }
        return couponUsers.stream().map(this::buildCouponVO).collect(Collectors.toList());
    }

    @Override
    public PagedResponse<CouponCardVO> getUserCardPage(CouponCardDTO dto) {
        PagedResponse page = new PagedResponse(Collections.EMPTY_LIST);
        if (Objects.isNull(dto)) {
            return page;
        }
        List<CouponCardVO> list = getCouponInfoList(dto);
        List<CouponCardVO> resultList = getCouponCardVOList(list, true, dto);
        return getCouponCardVOPage(page, resultList, dto);
        
    }

    @Override
    public PagedResponse<CouponCardVO> couponGlobalMonitoring(CouponCardDTO dto) {
        PagedResponse page = new PagedResponse(Collections.EMPTY_LIST);
        if (Objects.isNull(dto)) {
            return page;
        }
        List<CouponCardVO> list = getCouponInfoList(dto);
        return getCouponCardVOPage(page, getCouponCardVOList(list, false, dto), dto);
    }

    @Override
    public List<CouponCardVO> getCouponGlobalMonitoringList(CouponCardDTO dto) {
        List<CouponCardVO> list = new ArrayList<>();
        if (Objects.isNull(dto)) {
            return list;
        }
        list = getCouponInfoList(dto);
        return getCouponCardVOList(list, false, dto);
    }

    @Override
    public List<Long> sendCoupon(Long couponId, List<Long> uuid) {
        if (Objects.isNull(couponId) || CollectionUtils.isEmpty(uuid)) {
            throw new CrmInterviewException("请求参数不能为空");
        }
        return couponV2Service.sendCoupon(couponId, uuid);
    }

    @Override
    public List<Long> sendCoupon(CouponCardSendDTO dto) {
        if (Objects.isNull(dto)) {
            throw new CrmInterviewException("请求参数不能为空");
        }
        Long couponId = dto.getCouponId();
        List<Long> uuidList = couponV2Service.sendCoupon(couponId, Collections.singletonList(dto.getUuid()));
        // 保存发送记录
        saveSendLog(dto);
        return uuidList;
    }

    @Override
    public Page<CouponSendLogVO> getSendLog(CouponSendLogDTO dto) {
        int currentPage = dto.getCurrentPage();
        int rowsPerPage = dto.getRowsPerPage();
        if (StringUtils.isNotBlank(dto.getGroupCode())) {
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CouponSendLogVO> page = csCouponSendLogMapper
            .selectLogVOPage(
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(currentPage, rowsPerPage), dto);
        convertToCouponVoList(page.getRecords());
        return Page.initPage(page.getRecords(), (int) page.getTotal(), rowsPerPage, currentPage);
    }

    @Override
    public List<CouponSendLogVO> getSendLogList(CouponSendLogDTO dto) {
        if (StringUtils.isNotBlank(dto.getGroupCode())) {
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        List<CouponSendLogVO> list = csCouponSendLogMapper.selectLogVOList(dto);
        convertToCouponVoList(list);
        return list;
    }

    @Override
    public CouponWithdrawVO getWithdrawCoupons(Long uuid) {
        CouponWithdrawVO detailVO = new CouponWithdrawVO();
        if (Objects.isNull(uuid)) {
            return detailVO;
        }
        WithdrawCouponDetailDto dto = couponV2Service.getWithdrawCoupons(uuid);
        if (Objects.nonNull(dto)) {
            List<CouponWithdrawDetailVO> list = new ArrayList<>();
            ActivityCashCouponDto<ActivityCashCouponHistoryDTO> availableCoupons = dto
                .getAvailableCoupons();
            List<ActivityCashCouponHistoryDTO> innerList = availableCoupons.getInnerSevenDaysData();
            //最近七天内过期卡券记录
            if (CollectionUtils.isNotEmpty(innerList)) {
                list.addAll(convertCouponWithdrawDetailVO(innerList));
            }
            //有效期七天以上卡券记录
            List<ActivityCashCouponHistoryDTO> overList = availableCoupons.getOverSevenDaysData();
            if (CollectionUtils.isNotEmpty(overList)) {
                list.addAll(convertCouponWithdrawDetailVO(overList));
            }
            if (CollectionUtils.isNotEmpty(dto.getCouponsHistory())) {
                list.addAll(convertCouponWithdrawDetailVO(dto.getCouponsHistory()));
            }
            list.sort(Comparator.comparing(CouponWithdrawDetailVO::getDate).reversed());
            detailVO.setUuid(dto.getUuid());
            detailVO.setAvailableAmount(dto.getAvailableAmount());
            detailVO.setCouponsHistory(list);
        }
        return detailVO;
    }

    /**
     * 将业务类型、卡券类型、操作类型等参数转换为中文方便页面显示
     */
    private void convertToCouponVoList(List<CouponSendLogVO> list) {
        LambdaQueryWrapper<OpDictInfo> wrapper = Wrappers.<OpDictInfo>lambdaQuery()
                .eq(OpDictInfo::getCategory, "couponSendGroup");
        List<OpDictInfo> infoList = dictInfoMapper.selectList(wrapper);
        Map<String, String> groupMap = infoList.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent));
        list.forEach(vo -> {
            vo.setBusinessType(CouponBusinessEnum.getText(vo.getBusinessType()));
            CouponType type = CouponType.getType(vo.getAmountType());
            vo.setAmountType(Objects.nonNull(type) ? type.getName() : "");
            vo.setOperationType(CouponSendingTypeEnum.getText(vo.getOperationType()));
            vo.setGroupName(groupMap.get(vo.getGroupCode()));
        });
    }

    private List<CouponCardVO> getCouponInfoList(CouponCardDTO dto) {
        CouponParam couponParam = new CouponParam();
        if (StringUtils.isNotBlank(dto.getStart())) {
            couponParam.setStart(dto.getStart());
        }else {
            couponParam.setStart("");
        }
        if (StringUtils.isNotBlank(dto.getEnd())) {
            couponParam.setEnd(dto.getEnd());
        }else {
            couponParam.setEnd("");
        }

        couponParam.setRemark(dto.getRemark());
        couponParam.setDept(dto.getDept());
        couponParam.setPageNo(1);
        couponParam.setPageSize(100000);

        String businessType = dto.getBusinessType();
        if (BUSINESS_TYPE_LOAN.equals(businessType)) {
            return couponV2Service.getCouponInfoList(couponParam).getList();
        } else if (BUSINESS_TYPE_WALLET.equals(businessType)) {
            return walletCouponService.getCouponInfoList(couponParam).getList();
        } else if (StringUtils.isBlank(businessType)) {
            // businessType为空则查询所有
            List<CouponCardVO> list = new ArrayList<>();
            PagedResponse page = couponV2Service.getCouponInfoList(couponParam);
            if (Objects.nonNull(page) && Objects.nonNull(page.getList())) {
                list.addAll(page.getList());
            }
            page = walletCouponService.getCouponInfoList(couponParam);
            if (Objects.nonNull(page) && Objects.nonNull(page.getList())) {
                list.addAll(page.getList());
            }
            
            return list;
        }
        return null;
    }

    private PagedResponse<CouponCardVO> getCouponCardVOPage(PagedResponse page, List<CouponCardVO> list,
        CouponCardDTO dto) {
        if (Objects.isNull(page)) {
            page = new PagedResponse(Collections.EMPTY_LIST);
            return page;
        }
        Integer currentPage = dto.getCurrentPage();
        Integer pageSize = dto.getRowsPerPage();
        int totalSize = list.size();
        page.setPageNo(currentPage);
        page.setPageSize(pageSize);
        page.setTotal(totalSize);
        if (totalSize % pageSize == 0) {
            page.setPages(totalSize / pageSize);
        } else {
            page.setPages(totalSize / pageSize + 1);
        }
        if (totalSize >= currentPage * pageSize) {
            page.setSize(pageSize);
        } else {
            page.setSize(totalSize % pageSize);
        }
        int endIndex = Math.min(totalSize, currentPage * pageSize);
        page.setList(list.subList((currentPage - 1) * pageSize, endIndex));
        return page;
    }

    private List<CouponCardVO> getCouponCardVOList(List<CouponCardVO> cardVOList, boolean filter, CouponCardDTO dto) {
        List<CouponCardVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(cardVOList)) {
            return list;
        }
        try {
            Long id = dto.getId();
            String amountType = dto.getAmountType();
            Float amount = dto.getAmount();
            for (Object o : cardVOList) {
                String json = JSONObject.toJSONString(o);
                CouponCardVO couponCardVO = JSONObject.parseObject(json, CouponCardVO.class);
                if (CouponType.PERCENT.getValue().equals(couponCardVO.getAmountType())) {
                    // 保留两位小数
                    couponCardVO.setAmount(Float.valueOf(String.format("%.2f", couponCardVO.getAmount())));
                }
                if (Objects.nonNull(couponCardVO.getTotal()) && Objects.nonNull(couponCardVO.getUsed())) {
                    couponCardVO.setAvailable(couponCardVO.getTotal() - couponCardVO.getUsed());
                }
                if (Objects.nonNull(id) && !id.equals(couponCardVO.getId())) {
                    continue;
                }
                if (Objects.nonNull(amountType) && !amountType.equals(couponCardVO.getAmountType())) {
                    continue;
                }
                if (Objects.nonNull(amount) && !amount.equals(couponCardVO.getAmount())) {
                    continue;
                }
                if (filter && (Objects.isNull(couponCardVO.getAvailable()) || couponCardVO.getAvailable() <= 0)) {
                    continue;
                }
                // 将原始值也传到前端，便于卡券发送的时候传回来，这么做可免于再次查询消金的接口
                couponCardVO.setAmountTypeOrigin(couponCardVO.getAmountType());
                CouponType type = CouponType.getType(couponCardVO.getAmountType());
                couponCardVO.setAmountType(Objects.nonNull(type) ? type.getName() : "");
                Integer total = couponCardVO.getTotal();
                String availableRate = null;
                if (Objects.isNull(total) || total == 0) {
                    availableRate = "0.00%";
                } else {
                    double rate = couponCardVO.getAvailable() / total.doubleValue() * 100;
                    // 保留两位小数
                    availableRate = String.format("%.2f", rate) + "%";
                }
                couponCardVO.setAvailableRate(availableRate);
                list.add(couponCardVO);
            }
        } catch (Exception e) {
            log.error("getCouponCardVOList json convert error. {}", JSONObject.toJSONString(cardVOList), e);
            throw new CrmInterviewException("数据转换异常");
        }
        return list;
    }

    /**
     * 保存卡券发送记录
     *
     * @param dto
     */
    private void saveSendLog(CouponCardSendDTO dto) {
        CsCouponSendLog log = new CsCouponSendLog();
        log.setCouponId(dto.getCouponId());
        log.setBusiness(dto.getBusinessType());
        log.setDescription(dto.getDescription());
        if (StringUtils.isNotBlank(dto.getAmount())) {
            log.setAmount(new BigDecimal(dto.getAmount()));
        }
        log.setAmountType(dto.getAmountType());
        log.setAvailableDays(dto.getAvailableDays());
        log.setUserId(dto.getUserId());
        log.setUuid(dto.getUuid());
        log.setOperationType(dto.getOperationType());
        log.setCreateStaffId(dto.getCreateStaffId());
        log.setCreateStaffGroup(dto.getCreateStaffGroup());
        csCouponSendLogMapper.insert(log);
    }

    private List<CouponWithdrawDetailVO> convertCouponWithdrawDetailVO(List<ActivityCashCouponHistoryDTO> list) {
        return list.stream().map(dto -> {
            CouponWithdrawDetailVO vo = new CouponWithdrawDetailVO();
            BeanUtils.copyProperties(dto, vo);
            vo.setStatus(CouponWithdrawStatusEnum.getText(vo.getStatus()));
            vo.setDesc(Optional.ofNullable(vo.getDesc()).orElse("").split("\\|")[0]);
            return vo;
        }).collect(Collectors.toList());
    }

    private CouponVO buildCouponVO(CouponsUserDto couponUser) {
        CouponVO couponVO = new CouponVO();
        CouponDto coupon = couponUser.getCoupon_dto();
        couponVO.setCouponUserId(couponUser.getCoupon_id().toString());
        CouponType type = CouponType.getType(couponUser.getAmount_type());
        couponVO.setAmountType(Objects.nonNull(type) ? type.getName() : "");
        couponVO.setAmount(couponUser.getAmount());
        couponVO.setBeginAt(couponUser.getBegin_at());
        couponVO.setExpiredTime(couponUser.getEnd_at());
        String conditions = "";
        if (CouponType.FREE_LIMIT.getValue().equals(couponUser.getAmount_type())) {
            conditions = String.format("免除最多%s借款的利息", couponUser.getAmount().intValue());
        } else {
            if (null != coupon) {
                if (NumberUtil.gtZero(coupon.getFloor_principal_amount())) {
                    conditions = String.format("放款金额满%s使用", coupon.getFloor_principal_amount());
                } else if (NumberUtil.gtZero(coupon.getCeil_principal_amount())) {
                    conditions = String.format("放款金额不超过%s使用", coupon.getCeil_principal_amount());
                } else {
                    conditions = "所有贷款通用";
                }
            }
        }
        couponVO.setUseCondition(conditions);
        if (couponUser.getEnd_at() != null && new Date().after(couponUser.getEnd_at())
            && !couponUser.getRedeemed()) {
            couponVO.setStatus(CouponStatusEnum.EXPIRED.getText());
        } else if (couponUser.getRedeemed()) {
            WithdrawCouponsUsersDto withdraw = couponUser.getWithdrawCouponsUsersDto();
            if (Objects.nonNull(withdraw)) {
                if (withdraw.getWithdrawStatus().equals(RedeemStatus.YES.name())) {
                    couponVO.setStatus(CouponStatusEnum.USED_WITHDRAWN.getText());
                } else if (withdraw.getWithdrawStatus().equals(RedeemStatus.NO.name())) {
                    couponVO.setStatus(CouponStatusEnum.USED_NOT_WITHDRAWN.getText());
                } else {
                    couponVO.setStatus(CouponStatusEnum.USED_LOCK.getText());
                }
            }
        } else {
            couponVO.setStatus(CouponStatusEnum.ENABLED.getText());
        }
        if (couponUser.getRedeemed() && couponUser.getRedeemed_at() != null) {
            couponVO.setUsedTime(couponUser.getRedeemed_at());
        }
        //活动名称
        if (Objects.nonNull(coupon)) {
            couponVO.setDescription(coupon.getDescription());
        }
        //优惠券贷款关联
        if (Objects.nonNull(couponUser.getCouponsLoanDto())) {
            CouponsLoanDto couponsLoanDto = couponUser.getCouponsLoanDto();
            couponVO.setCurrentTenor(couponsLoanDto.getCurrentTenor());
            couponVO.setApplicationId(couponsLoanDto.getApplicationId());
            couponVO.setLoanAmount(couponsLoanDto.getLoanAmount());
        }
        return couponVO;
    }
}
