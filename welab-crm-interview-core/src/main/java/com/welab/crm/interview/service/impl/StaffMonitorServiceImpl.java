package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.utils.DateUtils;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.CallInfoAiSummary;
import com.welab.crm.interview.domain.DataCustomer;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.domain.RobotWarningRecord;
import com.welab.crm.interview.dto.wechat.WeChatReqBaseDTO;
import com.welab.crm.interview.enums.StaffMonitorWarningTypeEnum;
import com.welab.crm.interview.mapper.*;
import com.welab.crm.interview.service.StaffMonitorService;
import com.welab.crm.interview.service.WeChatDubboService;
import com.welab.crm.interview.vo.monitor.OpHttpLogRecordByPhoneSummary;
import com.welab.crm.interview.vo.monitor.StaffMonitorReportVO;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class StaffMonitorServiceImpl implements StaffMonitorService {

    @Resource
    private OpHttpLogRecordMapper opHttpLogRecordMapper;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private WeChatDubboService weChatDubboService;

    @Resource
    private RobotWarningRecordMapper robotWarningRecordMapper;

    @Resource
    private DataCustomerMapper dataCustomerMapper;

    @Resource
    private CallInfoAiSummaryMapper callInfoAiSummaryMapper;

    @Value("${staff.monitor.warning.robot.key}")
    private String robotKey;

    private static final String WARNING_TEXT = "<font color=\"warning\">【日常监控警告】</font>\n" +
            "> **%s**、**%s**、(**%s**) 类型操作超过 **%d** 次,请注意是否满足业务作业场景！！";

    private static final String PHONE_SUMMARY_WARNING_TEXT = "<font color=\"warning\">【敏感小结通知】</font>\n" +
            "> 用户姓名：**%s**\n" +
            "**%s**\n" +
            "客服姓名：**%s**\n" +
            "命中敏感词：监控小结内容有敏感词(**%s**),请核实用户咨询问题是否有风险！！";

    @Override
    public void queryLogDetailAndWarning() throws Exception {
        List<OpDictInfo> warningConfigList = queryWarningConfig();
        if (CollectionUtils.isEmpty(warningConfigList)) {
            throw new FastRuntimeException("坐席监控告警配置为空");
        }
        // {告警接口+组别:阈值} map
        Map<String, Integer> warningMap = warningConfigList.stream().collect(
            Collectors.toMap(item -> item.getType() + item.getContent(), item -> Integer.valueOf(item.getDetail())));
        List<StaffMonitorReportVO> logList =
            opHttpLogRecordMapper.queryLogData(DateUtils.getStartOfToday(), DateUtils.getEndOfDate(new Date()));

        for (StaffMonitorReportVO vo : logList) {
            for (StaffMonitorWarningTypeEnum warningTypeEnum : StaffMonitorWarningTypeEnum.values()) {
                String key = warningTypeEnum.getDesc() + vo.getGroupCode();

                if (warningMap.containsKey(key)) {
                    Field field = vo.getClass().getDeclaredField(warningTypeEnum.getCode());
                    ReflectionUtils.makeAccessible(field);
                    int count = Integer.parseInt(field.get(vo).toString());
                    if (warningMap.get(key) <= count) {
                        // 发送企业微信告警
                        if (saveWaringRecord(vo, warningTypeEnum, count)) {
                            wechatWarning(vo.getGroupCode(), vo.getStaffName(), warningTypeEnum.getDesc(), count, WARNING_TEXT);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void queryLogByPhoneSummaryWarning() throws Exception {
        //小结敏感关键字监控告警
        List<OpDictInfo> phoneSummaryDictList = queryPhoneSummaryWarningConfig();
        if (CollectionUtils.isEmpty(phoneSummaryDictList)) {
            throw new FastRuntimeException("小结监控告警配置为空");
        }
        Map<String, String> phoneSummaryMap = phoneSummaryDictList.stream().collect(Collectors.toMap(OpDictInfo::getContent, OpDictInfo::getType));
        List<OpHttpLogRecordByPhoneSummary> list = opHttpLogRecordMapper.queryLogDataByPhoneSummary(DateUtil.plusMins(new Date(), -10), new Date());
        if (CollectionUtils.isNotEmpty(list)) {
            String url1 = "/telemarketing/summary/save";//电销小结
            String url2 = "/phone/savePhoneSummaryDetail";//外呼小结
            String url3 = "/callbackSummary/save";//呼入小结
            for (OpHttpLogRecordByPhoneSummary tmp : list) {
                //只有小结才做后续操作
                if (!tmp.getRequestPath().contains(url1) && !tmp.getRequestPath().contains(url2) && !tmp.getRequestPath().contains(url3)) {
                    continue;
                }
                //转换数据
                JSONObject jsonObject = JSON.parseObject(tmp.getRequestBody().substring(0, tmp.getRequestBody().lastIndexOf(",")));
                String uuid = jsonObject.getString("uuid");
                String customerId = jsonObject.getString("customerId");
                //无uuid和customerId不处理
                if (StringUtils.isBlank(uuid) && StringUtils.isBlank(customerId)) {
                    continue;
                }
                DataCustomer customer = dataCustomerMapper.queryCustomer(customerId, uuid);
                //优先匹配人工小结的关键词
                StringBuilder sb = new StringBuilder();
                for (Map.Entry<String, String> map : phoneSummaryMap.entrySet()) {
                    if (tmp.getRequestBody().contains(map.getKey())) {
                        //解决重复匹配关键词的问题
                        sb.append(map.getKey());
                        sb.append("、");
                    }
                }
                if (sb.length() > 0 && Objects.nonNull(customer)) {
                    String detail = "uuid：" + (StringUtils.isNotBlank(customer.getUuid()) ? customer.getUuid() : "无uuid");
                    wechatWarningByPhoneSummary(customer.getCustomerName(), detail, tmp.getStaffName(), sb.substring(0, sb.lastIndexOf("、")), PHONE_SUMMARY_WARNING_TEXT);
                    continue;
                }
                //人工小结没有敏感关键词，尝试从ai小结获取敏感关键词
                String cdrMainUniqueId = jsonObject.getString("cdrMainUniqueId");
                if (StringUtils.isNotBlank(cdrMainUniqueId)) {
                    CallInfoAiSummary aiSummary = callInfoAiSummaryMapper.selectOne(Wrappers.lambdaQuery(CallInfoAiSummary.class).eq(CallInfoAiSummary::getCdrMainUniqueId, cdrMainUniqueId));
                    StringBuilder sbAi = new StringBuilder();
                    if (Objects.nonNull(aiSummary)) {
                        for (Map.Entry<String, String> map : phoneSummaryMap.entrySet()) {
                            if (aiSummary.getSummary().contains(map.getKey())) {
                                //解决重复匹配关键词的问题
                                sbAi.append(map.getKey());
                                sbAi.append("、");
                            }
                        }
                    }
                    if (sbAi.length() > 0 && Objects.nonNull(customer)) {
                        String detail = "uuid：" + (StringUtils.isNotBlank(customer.getUuid()) ? customer.getUuid() : "无uuid");
                        wechatWarningByPhoneSummary(customer.getCustomerName(), detail, tmp.getStaffName() + "(AI小结)", sbAi.substring(0, sbAi.lastIndexOf("、")), PHONE_SUMMARY_WARNING_TEXT);
                    }
                }
            }
        }
    }

    private boolean saveWaringRecord(StaffMonitorReportVO vo, StaffMonitorWarningTypeEnum warningTypeEnum, int count) {
        List<RobotWarningRecord> records =
            robotWarningRecordMapper.selectList(Wrappers.lambdaQuery(RobotWarningRecord.class)
                .between(RobotWarningRecord::getGmtCreate, DateUtils.getStartOfToday(),
                    DateUtils.getEndOfDate(new Date()))
                .eq(RobotWarningRecord::getLoginName, vo.getLoginName())
                .eq(RobotWarningRecord::getType, warningTypeEnum.getCode()));

        if (CollectionUtils.isEmpty(records)) {
            RobotWarningRecord record = new RobotWarningRecord();
            record.setLoginName(vo.getLoginName());
            record.setType(warningTypeEnum.getCode());
            record.setContent(
                String.format(WARNING_TEXT, vo.getGroupCode(), vo.getStaffName(), warningTypeEnum.getDesc(), count));
            record.setGmtCreate(new Date());
            record.setGroupCode(vo.getGroupCode());
            robotWarningRecordMapper.insert(record);
            return true;
        }
        return false;
    }

    private void wechatWarning(String groupCode, String staffName, String type, int count, String txt) {
        WeChatReqBaseDTO dto = new WeChatReqBaseDTO();
        dto.setMsgType("markdown");
        dto.setContent(String.format(txt, groupCode, staffName, type, count));
        dto.setMentionedMobileList(Collections.singletonList("@all"));
        dto.setKey(robotKey);
        weChatDubboService.pushMsgToWeChatRobot(dto);
    }

    private void wechatWarningByPhoneSummary(String customerName, String uuid, String staffName, String type, String txt) {
        WeChatReqBaseDTO dto = new WeChatReqBaseDTO();
        dto.setMsgType("markdown");
        dto.setContent(String.format(txt, customerName, uuid, staffName, type));
        dto.setMentionedMobileList(Collections.singletonList("@all"));
        dto.setKey(robotKey);
        weChatDubboService.pushMsgToWeChatRobot(dto);
    }

    private List<OpDictInfo> queryWarningConfig() {
        return opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class)
            .eq(OpDictInfo::getCategory, "staff_monitor_warn").eq(OpDictInfo::getStatus, 1));
    }

    private List<OpDictInfo> queryPhoneSummaryWarningConfig() {
        return opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class)
                .eq(OpDictInfo::getCategory, "phoneSummary").eq(OpDictInfo::getStatus, 1));
    }
}
