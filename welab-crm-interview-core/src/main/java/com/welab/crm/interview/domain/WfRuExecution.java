package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 流程运行时实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wf_ru_execution")
public class WfRuExecution implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 唯一索引
     */
    private String executionId;

    /**
     * 业务关联编号
     */
    private String busiKey;

    /**
     * 绑定的流程定义号
     */
    private String processCode;

    /**
     * 流程状态；wf_de_transition表target_state配置
     */
    private String status;

    /**
     * 发起人
     */
    private String staffId;

    /**
     * 发起人所在组
     */
    private String groupCode;

    /**
     * 流程实例完成标识（1完成，0进行中）
     */
    private String completeFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
