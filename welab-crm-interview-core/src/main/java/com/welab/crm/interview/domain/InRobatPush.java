package com.welab.crm.interview.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 智能机器人推送表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_robat_push")
public class InRobatPush implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableField(fill = FieldFill.INSERT) 
    private Long id;

    /**
     * 客户号码
     */
    private String cdrCustomerNumber;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT) 
    private Date gmtCreate;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 最后修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE) 
    private Date gmtModify;

    /**
     * 公司名称
     */
    private String caCompany;

    /**
     * 是否推送成功
     */
    private String pushFlag;


}
