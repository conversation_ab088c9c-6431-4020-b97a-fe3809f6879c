package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.service.AiPushService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AiPush<PERSON>ob implements SimpleJob {

    private final AiPushService aiPushService;

    public AiPushJob(AiPushService aiPushService) {
        this.aiPushService = aiPushService;
    }

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("AiPushJob execute start, jobName: {}", shardingContext.getJobName());
        String jobName = shardingContext.getJobName();
        String id = jobName.replace(ApplicationConstant.PREFIX_JOB, "");
        try {
            aiPushService.pushDataToIvr(Long.parseLong(id));
            log.info("AiPushJob execute completed, jobName: {}", shardingContext.getJobName());
        } catch (Exception e) {
            log.error("AiPushJob execute occur exception, job id: {}, errorMsg: {}", id, e.getMessage(), e);
        }
    }
}
