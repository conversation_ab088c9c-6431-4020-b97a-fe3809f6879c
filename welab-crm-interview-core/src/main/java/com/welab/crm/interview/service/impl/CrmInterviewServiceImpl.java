package com.welab.crm.interview.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.welab.crm.interview.domain.InRobatPush;
import com.welab.crm.interview.mapper.InRobatPushMapper;
import com.welab.crm.interview.service.CrmInterviewService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CrmInterviewServiceImpl implements CrmInterviewService{

    @Resource
    private InRobatPushMapper inRobatPushMapper;
    
    public Long AddInRobatPushRecord(String cdrCustomerNumber) {
        Long id = null;
        log.info("开始订单处理{}", cdrCustomerNumber);
        try {
            InRobatPush inRobatPush=new InRobatPush();
            inRobatPush.setCaCompany("caCompany123");
            inRobatPush.setCdrCustomerNumber(cdrCustomerNumber);
            inRobatPush.setCreateUser("createUser123");
            inRobatPush.setLstUpdUser("lstUpdUser123");
            inRobatPush.setPushFlag("Y");
            inRobatPushMapper.insert(inRobatPush);
            id= inRobatPush.getId();
            log.info("订单处理{}", cdrCustomerNumber);
        } catch (Exception e) {
            log.error("业务处理异常{}", e.getMessage());
        }
        log.info("结束订单处理{}", cdrCustomerNumber);
        return id;
    }
    
}
