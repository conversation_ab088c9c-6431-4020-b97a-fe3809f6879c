package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.welab.crm.base.utils.DateUtils;
import com.welab.crm.interview.domain.CallInStatisticsHourReport;
import com.welab.crm.interview.domain.CallInStatisticsReport;
import com.welab.crm.interview.mapper.CallInStatisticsHourReportMapper;
import com.welab.crm.interview.mapper.CallInStatisticsReportMapper;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.util.DateUtil;
import com.welab.crm.interview.util.Md5Util;
import com.welab.crm.interview.vo.phone.ReportPhoneSummaryItem;
import com.welab.exception.FastRuntimeException;
import com.welab.privacy.util.StringUtil;
import com.welab.privacy.util.http.HttpClients;
import com.welab.voice.api.DepartmentDetailFacade;
import com.welab.voice.api.vo.DistinctionDepVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR> 天润token服务接口，对接voice-center
 */
@Service("trTokenService")
@Slf4j
public class TrTokenServiceImpl implements TrTokenService {

    @Value("${tr.kf.enterpriseId}")
    private String enterpriseId;

    /**
     * 热线号码
     */
    private static final List<String> HOTLINE_LIST =
        new ArrayList<>(Arrays.asList("21246446", "10100518", "4006000799", "4006040888", "4000666730"));

    /**
     * 天润请求URL前缀
     */
    private static final String TR_BASE_URL = "https://api-6.cticloud.cn/interface/v10/";


    private static final int MAX_ATTEMPTS = 5;
    private static final long RETRY_DELAY = 5000;

    @Resource
    private DepartmentDetailFacade departmentDetailFacade;

    @Resource
    private CallInStatisticsReportMapper callInStatisticsReportMapper;
    
    @Resource
    private CallInStatisticsHourReportMapper callInStatisticsHourReportMapper;

    @Override
    public String getTokenByEnterpriseId(String enterpriseId) {
        try {
            log.info("getTokenByEnterpriseId,enterpriseId:{}", enterpriseId);
            DistinctionDepVO vo = departmentDetailFacade.queryDepDetail(null, enterpriseId);
//            log.info("getTokenByEnterpriseId res:{}", JSON.toJSONString(vo));
            return vo.getToken();
        } catch (Exception e) {
            log.warn("getTokenByEnterpriseId 异常", e);
            throw new FastRuntimeException("获取天润token异常");
        }
    }

    @Override
    public void saveTrunkReportIbToDb(String countDate) {
        log.info("开始拉取天润中继来电报表数据");

        try {
            JSONArray jsonArray = queryTrunkReport("1", countDate);
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (Objects.nonNull(jsonObject)) {
                        ReportPhoneSummaryItem phoneSummaryItem = jsonObject.toJavaObject(ReportPhoneSummaryItem.class);
                        List<CallInStatisticsReport> callInStatisticsReports = callInStatisticsReportMapper.selectList(Wrappers.lambdaQuery(CallInStatisticsReport.class)
                                        .eq(CallInStatisticsReport::getCountDay, phoneSummaryItem.getDay()));
                        if (CollectionUtils.isNotEmpty(callInStatisticsReports)){
                            log.info("天润中继来电报表数据已存在,不再重复拉取");
                            continue;
                        }
                        
                        CallInStatisticsReport callInStatisticsReport = buildReportObject(phoneSummaryItem);
                        callInStatisticsReportMapper.insert(callInStatisticsReport);
                    }

                }
            }

        } catch (Exception e) {
            log.error("saveTrunkReportIbToDb 请求天润接口异常", e);
            throw new FastRuntimeException("天润获取中继报表来电分析异常");
        }

        log.info("结束拉取天润中继来电报表数据");

        log.info("开始拉取天润中继来电报分时表数据");
        JSONArray jsonArray = queryTrunkReport("0", countDate);
        
        try {
            if (CollectionUtils.isNotEmpty(jsonArray)){
                List<CallInStatisticsHourReport> callInStatisticsHourReports = callInStatisticsHourReportMapper.selectList(Wrappers.lambdaQuery(CallInStatisticsHourReport.class)
                        .eq(CallInStatisticsHourReport::getCountDay, StringUtil.isBlank(countDate) ? DateUtil.getYesterday() : countDate));
                if (CollectionUtils.isNotEmpty(callInStatisticsHourReports)){
                    log.info("天润中继来电报分时表数据已存在,不再重复拉取");
                    return;
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (Objects.nonNull(jsonObject)) {
                        ReportPhoneSummaryItem phoneSummaryItem = jsonObject.toJavaObject(ReportPhoneSummaryItem.class);
                        CallInStatisticsHourReport report = buildReportObjectForHour(phoneSummaryItem, countDate);
                        callInStatisticsHourReportMapper.insert(report);
                    }

                }
            }
        } catch (Exception e){
            log.error("查询天润分时报表异常", e);
            throw new FastRuntimeException("天润获取中继报表来电分析异常");
        }

        log.info("结束拉取天润中继来电报分时表数据");
    }

    @Override
    public String getWaveSoundUrl(String enterpriseId, String recordFile) {

        Map<String, String> params = getTrBaseParams(enterpriseId, getTokenByEnterpriseId(enterpriseId));
        String recordgetUrl = TR_BASE_URL + "/record/getUrl";
        params.put("recordType", "record");
        params.put("recordFile", recordFile);
        params.put("recordFormat", "1");
        int attempts = 0;
        while (attempts < MAX_ATTEMPTS) {
            attempts++;
            try {
                String response = HttpClients.create().setUrl(recordgetUrl).addURLParams(params).doGet();
                JSONObject json = JSONObject.parseObject(response);
                if ("0".equals(json.get("result"))) {
                    return json.getString("data");
                } else {
                    throw new FastRuntimeException("天润获取录音地址失败: " + response);
                }
            } catch (Exception e) {
                log.info("Attempt " + attempts + " failed. Retrying in " + RETRY_DELAY + "ms...");
	            try {
		            Thread.sleep(RETRY_DELAY);
	            } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
		            throw new RuntimeException(ex);
	            }
            }
        }

        log.info("Operation failed after " + MAX_ATTEMPTS + " attempts.");
        throw new FastRuntimeException("天润获取录音地址失败");
    }
    

    public Map<String, String> getTrBaseParams(String enterpriseId, String sign) {
        Map<String, String> params = new HashMap<>(16);
        params.put("validateType", "2");
        params.put("enterpriseId", enterpriseId);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        params.put("sign", Md5Util.md5(enterpriseId + timestamp + sign));
        params.put("timestamp", timestamp);
        return params;
    }

    /**
     * 查询中继报表
     * @param statisticMethod 统计方式 0-分时，1-分日
     * @return
     */
    JSONArray queryTrunkReport(String statisticMethod, String countDate) {
        Map<String, String> params = new HashMap<>();
        params.put("validateType", "2");
        params.put("enterpriseId", enterpriseId);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        params.put("sign", Md5Util.md5(enterpriseId + timestamp + getTokenByEnterpriseId(enterpriseId)));
        params.put("timestamp", timestamp);
        // 日报表
        params.put("timeRangeType", "1");
        // 汇总
        params.put("statisticMethod", statisticMethod);
        params.put("startTime", StringUtil.isBlank(countDate) ? DateUtil.getYesterday() : countDate);
        params.put("hotlines", Joiner.on(",").join(HOTLINE_LIST));
        params.put("limit","500");

        try {
            String response = HttpClients.create().setUrl(TR_BASE_URL + "trunkReport/ib").addURLParams(params).doGet();
            JSONObject json = JSON.parseObject(response);
            if (Objects.isNull(json)) {
                log.warn("返回结果解析成json对象失败");
                return new JSONArray();
            }

            if (!"0".equals(json.get("result"))) {
                log.warn("返回结果码异常,不为0");
                return new JSONArray();
            }
            String dataStr = json.getString("data");
            JSONObject data = JSON.parseObject(dataStr);
            return data.getJSONArray("list");

        } catch (Exception e) {
            log.error("saveTrunkReportIbToDb 请求天润接口异常", e);
            throw new FastRuntimeException("天润获取中继报表来电分析异常");
        }
    }

    private CallInStatisticsReport buildReportObject(ReportPhoneSummaryItem phoneSummaryItem) {
        CallInStatisticsReport report = new CallInStatisticsReport();
        report.setHotline(phoneSummaryItem.getHotline());
        report.setCallInNum(Objects.nonNull(phoneSummaryItem.getIbTotalCount())
            ? Integer.parseInt(phoneSummaryItem.getIbTotalCount()) : 0);
        report.setToManualNum(Objects.nonNull(phoneSummaryItem.getIbQueueCount())
            ? Integer.parseInt(phoneSummaryItem.getIbQueueCount()) : 0);
        report.setAnsweredNum(Objects.nonNull(phoneSummaryItem.getIbAnsweredCount())
            ? Integer.parseInt(phoneSummaryItem.getIbAnsweredCount()) : 0);
        report.setTotalBridgeTime(phoneSummaryItem.getTotalBridgeTime());
        report.setAvgBridgeTime(phoneSummaryItem.getAvgBridgeTime());
        report.setQueueGiveUpNum(Objects.nonNull(phoneSummaryItem.getIbQnoAbandonCount())
            ? Integer.parseInt(phoneSummaryItem.getIbQnoAbandonCount()) : 0);
        report.setCountDay(com.welab.common.utils.DateUtil.stringToDate(phoneSummaryItem.getDay()));
        return report;
    }

    private CallInStatisticsHourReport buildReportObjectForHour(ReportPhoneSummaryItem phoneSummaryItem, String countDate) {
        CallInStatisticsHourReport report = new CallInStatisticsHourReport();
        report.setHotline(phoneSummaryItem.getHotline());
        report.setCallInNum(Objects.nonNull(phoneSummaryItem.getIbTotalCount())
                ? Integer.parseInt(phoneSummaryItem.getIbTotalCount()) : 0);
        report.setToManualNum(Objects.nonNull(phoneSummaryItem.getIbQueueCount())
                ? Integer.parseInt(phoneSummaryItem.getIbQueueCount()) : 0);
        report.setAnsweredNum(Objects.nonNull(phoneSummaryItem.getIbAnsweredCount())
                ? Integer.parseInt(phoneSummaryItem.getIbAnsweredCount()) : 0);
        report.setTotalBridgeTime(phoneSummaryItem.getTotalBridgeTime());
        report.setAvgBridgeTime(phoneSummaryItem.getAvgBridgeTime());
        report.setQueueGiveUpNum(Objects.nonNull(phoneSummaryItem.getIbQnoAbandonCount())
                ? Integer.parseInt(phoneSummaryItem.getIbQnoAbandonCount()) : 0);
        report.setCountDay(StringUtil.isBlank(countDate) ? DateUtils.getStartOfYesterday() : com.welab.common.utils.DateUtil.stringToDate(countDate));
        report.setHour(Integer.valueOf(phoneSummaryItem.getHour()));
        return report;
    }
}
