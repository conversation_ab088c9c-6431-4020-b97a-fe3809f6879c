package com.welab.crm.interview.util;

import com.welab.document.interfaces.dto.UploadOssDTO;
import com.welab.document.interfaces.facade.OssClientServiceFacade;
import com.welab.document.util.FileUploader;
import java.io.IOException;
import java.io.InputStream;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;


import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云oss上传下载工具类
 */
@Slf4j
@Component(value = "aliyunOssUtil")
public class AliyunOssUtil {

    @Resource
    private OssClientServiceFacade ossClientServiceFacade;



    /**
     * Oss统一管理，调用消金接口进行上传
     *
     * @param filePath 文件路径
     */
    public boolean writeFileNew(String filePath, InputStream is) {

        boolean result = true;
        try {
            log.info("调用消金接口统一上传文件,filePath:{}", filePath);
            UploadOssDTO uploadOssParams = ossClientServiceFacade.getUploadOssParams(getParams(filePath));
            FileUploader.fileUpload(uploadOssParams, is);
        } catch (Exception e) {
            result = false;
            log.error("阿里云写文件异常:{}", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("关闭阿里云数据流时异常:{}", e);
                }
            }
        }
        return result;
    }
    /**
     * Oss统一管理，调用消金接口进行读取文件
     *
     * @param filePath 文件路径
     * @return
     */
    public byte[] readFileNew(String filePath) {
        log.info("调用消金接口统一读取文件,filePath:{}",filePath);
        byte[] bytes = null;
        try {
            String ossUrl = ossClientServiceFacade.getOssUrl(filePath);
            bytes = HttpClientUtil.getFile(ossUrl);
        } catch (Exception e) {
            log.error("阿里云获取文件不存在:{}", e);
        }
        return bytes;
    }

    /**
     * 拼接参数
     * @param filepath 文件路径
     */
    private Map<String, String> getParams(String filepath) {
        Map<String, String> params = new HashMap<>();
        params.put("path", filepath);
        // 不需要消金给我们拼接路径前缀
        params.put("addPrefix", "no");
        params.put("applicationName","welab-crm-interview");
        return params;
    }

}
