package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 工单未结案30-59天通知任务
 */
@Slf4j
public class OrderTaskWithinTwoMonthNoticeJob implements SimpleJob {


	@Resource
	private WorkOrderService workOrderService;

	@Override
	public void execute(ShardingContext shardingContext) {
		long startTime = System.currentTimeMillis();
		log.info("OrderTaskWithinTwoMonthNoticeJob start");
		workOrderService.queryNotCloseOrderAndNotice(30, 59);
		log.info("OrderTaskWithinTwoMonthNoticeJob end, use time: {}ms", System.currentTimeMillis() - startTime);
	}
}
