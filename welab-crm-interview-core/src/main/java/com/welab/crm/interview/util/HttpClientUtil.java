package com.welab.crm.interview.util;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Set;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

/**
 * @program: cs
 * @Date: 2019/10/28 13:12
 * @Author: hayder.lai
 * @Description:
 */
@Slf4j
public class HttpClientUtil {

    private static final RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000)
            .setConnectTimeout(10000).build();// 设置请求和传输超时时间

    public static String httpGet(String httpUrl, Map<String, String> params) throws Exception {
        String result = null ;
        CloseableHttpClient httpclient = createDefaultSSL();
        BufferedReader in = null ;
        //HttpPatch
        HttpGet get = new HttpGet(httpUrl);
        //setHeader,添加头文件
        Set<String> keys = params.keySet();
        for (String key : keys) {
            get.setHeader(key, params.get(key).toString());
        }
        get.setConfig(requestConfig);
        try {
            CloseableHttpResponse response = httpclient.execute(get);
            InputStream content = response.getEntity().getContent() ;
            in = new BufferedReader(new InputStreamReader(content, StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            String line = "" ;
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            result = sb.toString() ;
            return result ;
        } catch (Exception e) {
            e.printStackTrace() ;
        } finally {
            httpclient.close();
        }
        return null ;
    }

    /**
     * 同时支持https和http的post请求
     */
    public static String doPost(String url, String jsonStr) throws Exception {
        String result = null;
        CloseableHttpClient httpClient = null;
        try {
            if (url.startsWith("https://")) {
                httpClient = createDefaultSSL();
            } else {
                httpClient = HttpClients.createDefault();
            }
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
            StringEntity se = new StringEntity(jsonStr, "UTF-8");
            se.setContentType("text/json;charset=UTF-8");
            se.setContentEncoding(new BasicHeader("Content-Type", "application/json;charset=UTF-8"));
            httpPost.setEntity(se);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, "UTF-8");
                }
            }
        } catch (Exception ex) {
            log.error("doPost url: {}, error: {}", url, ex.getMessage(), ex);
            throw new Exception("HttpClientUtil Exception!");
        } finally {
            if (httpClient != null) {
                httpClient.close();
            }
        }
        return result;
    }

    public static String httpPatch(String httpUrl, Map<String, String> params) throws Exception {
        String result = null ;
        CloseableHttpClient httpclient = createDefaultSSL();
        BufferedReader in = null ;
        //HttpPatch
        HttpPatch patch = new HttpPatch(httpUrl);
        //setHeader,添加头文件
        Set<String> keys = params.keySet();
        for (String key : keys) {
            patch.setHeader(key, params.get(key).toString());
        }
        patch.setConfig(requestConfig);
        try {
            CloseableHttpResponse response = httpclient.execute(patch);
            InputStream content = response.getEntity().getContent() ;
            in = new BufferedReader(new InputStreamReader(content, "UTF-8"));
            StringBuilder sb = new StringBuilder();
            String line = "" ;
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            result = sb.toString() ;
            return result ;
        } catch (Exception e) {
            e.printStackTrace() ;
        } finally {
            httpclient.close();
        }
        return null ;
    }

//    public static CloseableHttpClient createSSLClientDefault(){
//        try {
//            X509TrustManager x509mgr = new X509TrustManager() {
//                @Override
//                public void checkClientTrusted(X509Certificate[] xcs, String string) {
//                }
//                @Override
//                public void checkServerTrusted(X509Certificate[] xcs, String string) {
//                }
//                //返回受信任的X509证书数组。
//                @Override
//                public X509Certificate[] getAcceptedIssuers() {
//                    return null;
//                }
//            };
//            SSLContext sslContext = SSLContext.getInstance("SSL","SunJSSE");
//            sslContext.init(null, new TrustManager[] { x509mgr }, new java.security.SecureRandom());
//            //设置其SSLSocketFactory对象
//            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
//            //连接HTTPS了，无论其证书是否经权威机构的验证，信任实现了X509TrustManager接口证书。
//            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
//        } catch (KeyManagementException e) {
//            e.printStackTrace();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        // 创建默认的httpClient实例.
//        return  HttpClients.createDefault();
//    }


    public static CloseableHttpClient createDefaultSSL() {
        try {
            //使用 loadTrustMaterial() 方法实现一个信任策略，信任所有证书
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            //关闭了主机名验证，它接受任何有效的SSL会话并匹配到目标主机。
            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

    /**
     * @param url
     * @return
     */
    public static byte[] getFile(String url) {

        InputStream inStream = null;
        ByteArrayOutputStream outStream = null;
        byte[] buffer = null;
        try {

            URL urlConet = new URL(url);
            HttpURLConnection con = (HttpURLConnection) urlConet.openConnection();
//			con.setRequestMethod("GET");
            con.setConnectTimeout(10 * 1000);
            con.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            con.connect();
            inStream = con.getInputStream(); // 通过输入流获取图片数据
            outStream = new ByteArrayOutputStream();
            buffer = new byte[2048];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            byte[] data = outStream.toByteArray();
            return data;
        } catch (Exception e) {
            log.warn("获取图片[url:{}]字节流数组失败，出现异常:{}", url, ExceptionUtils.getStackTrace(e));
        } finally {
            IOUtils.closeQuietly(inStream);
            IOUtils.closeQuietly(outStream);
            buffer = null;
        }

        return null;
    }
}
