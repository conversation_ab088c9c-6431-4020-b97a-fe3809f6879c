package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.constant.BusiConstant;
import com.welab.crm.interview.domain.InLenderWithholdRecord;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.mapper.InLenderWithholdRecordMapper;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.vo.AssetsBoardVO;
import com.welab.crm.interview.vo.AssetsPartnerVO;
import com.welab.crm.interview.vo.loan.LoanRecordVO;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.loanprocedure.dto.GenerateSettlementProofDTO;
import com.welab.finance.loanprocedure.dubbo.UserAgreementDubboService;
import com.welab.finance.loanprocedure.vo.GenerateSettlementProofVO;
import com.welab.finance.repayment.dto.RepayChannelReq;
import com.welab.finance.repayment.dto.UserRepaysDTO;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.finance.repayment.dubbo.FactorDubboService;
import com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService;
import com.welab.finance.repayment.dubbo.RepaymentDubboService;
import com.welab.finance.repayment.dubbo.RepaymentRecordDubboService;
import com.welab.finance.repayment.vo.RepayChannelVO;
import com.welab.finance.repayment.vo.RepaymentVo;
import com.welab.finance.repayment.vo.UserRepaysVO;
import com.welab.privacy.util.http.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 资金服务接口实现类
 * @date 2022/4/19 14:59
 */
@Service
@Slf4j
public class FinanceServiceImpl implements FinanceService {

    @Value("${lender.zckb.url}")
    private String zckbUrl;

    @Value("${lender.zckb.code.url}")
    private String zckbListUrl;

    @Resource
    private RepaymentRecordDubboService repaymentRecordDubboService;

    @Resource
    private RepaymentDubboService repaymentDubboService;

    @Resource
    private InLenderWithholdRecordMapper inLenderWithholdRecordMapper;

    @Resource
    private com.welab.wallet.repayment.dubbo.RepaymentDubboService walletRepaymentDubboService;

    @Resource
    private FactorDubboService factorDubboService;

    @Resource
    private UserAgreementDubboService userAgreementDubboService;
    
    @Resource
    private RepaymentCalculationDubboService repaymentCalculationDubboService;

    @Override
    public Response<List<UserRepaysVO>> queryUserPayByApplicationId(String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "合同号不能为空", null);
        }

        UserRepaysDTO dto = new UserRepaysDTO();
        dto.setApplicationId(applicationId);
        log.info("[queryUserPayByApplicationId]查询代扣记录,applicationId:{}", applicationId);
        Response<List<UserRepaysVO>> response = repaymentRecordDubboService.queryUserRepaysByCondition(dto);
        log.info("[queryUserPayByApplicationId]查询代扣记录完成,response:{}", JSON.toJSONString(response));
        return response;
    }

    @Override
    public Response<RepaymentVO> repayment(RepaymentDTO repaymentDTO) {
        com.welab.finance.repayment.dto.RepaymentDTO repayDTO = new com.welab.finance.repayment.dto.RepaymentDTO();
        BeanUtils.copyProperties(repaymentDTO, repayDTO);
        log.info("[repayment] 开始代扣, params:{}", JSON.toJSONString(repayDTO));
        Response<RepaymentVO> myResponse = new Response<>();
        Response<RepaymentVo> response;
        try {
            response = repaymentDubboService.repayment(repayDTO);
            log.info("[repayment] 结束代扣, response:{}", JSON.toJSONString(response));
        }catch (Exception e){
            log.warn("[repayment] 返回异常: {}", e.getMessage(), e);
            myResponse.setCode(ResponsCodeTypeEnum.FAILURE.getCode());
            myResponse.setMessage(e.getMessage());
            return myResponse;
        }

        myResponse.setCode(response.getCode());
        myResponse.setMessage(response.getMessage());
        if (Response.isSuccess(response)) {
            RepaymentVO vo = new RepaymentVO();
            vo.setDuctionChannel(response.getResult().getDuctionChannel());
            vo.setWxpayMap(response.getResult().getWxpayMap());
        }
        return myResponse;
    }


    @Override
    public void syncWithholdRecord(int count, Date date) {
        try {
            QueryWrapper<InLenderWithholdRecord> queryWrapper = buildQueryWrapper(count, date);
            List<InLenderWithholdRecord> recordList = inLenderWithholdRecordMapper.selectList(queryWrapper);
            for (InLenderWithholdRecord record : recordList) {
                UserRepaysVO repaysVO = null;
                UserRepaysDTO dto = new UserRepaysDTO();
                dto.setServiceNo(record.getServiceNo());
                if (record.getApplicationId().startsWith(BusiConstant.WL)) {
                    Response<com.welab.wallet.repayment.vo.UserRepaysVO> res = walletRepaymentDubboService.getUserRepayByServiceNo(dto.getServiceNo());
                    log.info("syncWithholdRecord 同步代扣数据,钱包 response:{}", JSON.toJSONString(res));
                    com.welab.wallet.repayment.vo.UserRepaysVO result = res.getResult();
                    if (Objects.nonNull(result)) {
                        repaysVO = new UserRepaysVO();
                        BeanUtils.copyProperties(result, repaysVO);
                    }
                } else {
                    Response<List<UserRepaysVO>> response = repaymentRecordDubboService.queryUserRepaysByCondition(dto);
                    log.info("syncWithholdRecord 同步代扣数据,现金贷 response:{}", JSON.toJSONString(response));
                    List<UserRepaysVO> resultList = response.getResult();
                    if (CollectionUtils.isNotEmpty(resultList)) {
                        repaysVO = resultList.get(0);
                    }
                }


                if (Objects.isNull(repaysVO)) {
                    log.warn("返回结果为空，流水号:{}", record.getServiceNo());
                } else {
                    inLenderWithholdRecordMapper
                            .update(new InLenderWithholdRecord().setPayCode(repaysVO.getDebitChannelCode())
                                    .setCallbackAmount(repaysVO.getDebitAmount())
                                    .setCallbackResult("succeed".equals(repaysVO.getStatus()) ? "success"
                                            : "init".equals(repaysVO.getStatus()) ? "processing" : "fail")
                                    .setGmtModify(new Date()).setFailReason(repaysVO.getRemark()), getUpdateWrapper(record.getServiceNo()));

                }
            }
        } catch (Exception e) {
            log.error("syncWithholdRecord 同步代扣数据异常", e);
        }


    }

    /**
     * 更新指定时间内的临时更新贷款记录方法
     */
    public void syncWithholdRecordTemp(String startTime, String endTime) {
        try {

            QueryWrapper<InLenderWithholdRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("withhold_result", "success");
            wrapper.eq("callback_result", "processing");
            wrapper.between("gmt_create", DateUtil.stringToDate(startTime), DateUtil.stringToDate(endTime));
            List<InLenderWithholdRecord> recordList = inLenderWithholdRecordMapper.selectList(wrapper);
            for (InLenderWithholdRecord record : recordList) {
                UserRepaysVO repaysVO = null;
                UserRepaysDTO dto = new UserRepaysDTO();
                dto.setServiceNo(record.getServiceNo());
                if (record.getApplicationId().startsWith(BusiConstant.WL)) {
                    Response<com.welab.wallet.repayment.vo.UserRepaysVO> res = walletRepaymentDubboService.getUserRepayByServiceNo(dto.getServiceNo());
                    log.info("syncWithholdRecord 同步代扣数据,钱包 response:{}", JSON.toJSONString(res));
                    com.welab.wallet.repayment.vo.UserRepaysVO result = res.getResult();
                    if (Objects.nonNull(result)) {
                        repaysVO = new UserRepaysVO();
                        BeanUtils.copyProperties(result, repaysVO);
                    }
                } else {
                    Response<List<UserRepaysVO>> response = repaymentRecordDubboService.queryUserRepaysByCondition(dto);
                    log.info("syncWithholdRecord 同步代扣数据,现金贷 response:{}", JSON.toJSONString(response));
                    List<UserRepaysVO> resultList = response.getResult();
                    if (CollectionUtils.isNotEmpty(resultList)) {
                        repaysVO = resultList.get(0);
                    }
                }


                if (Objects.isNull(repaysVO)) {
                    log.warn("返回结果为空，流水号:{}", record.getServiceNo());
                } else {
                    inLenderWithholdRecordMapper
                            .update(new InLenderWithholdRecord().setPayCode(repaysVO.getDebitChannelCode())
                                    .setCallbackAmount(repaysVO.getDebitAmount())
                                    .setCallbackResult("succeed".equals(repaysVO.getStatus()) ? "success"
                                            : "init".equals(repaysVO.getStatus()) ? "processing" : "fail")
                                    .setGmtModify(new Date()).setFailReason(repaysVO.getRemark()), getUpdateWrapper(record.getServiceNo()));

                }
            }
        } catch (Exception e) {
            log.error("syncWithholdRecord 同步代扣数据异常", e);
        }
    }


        @Override
    public List<AssetsBoardVO> getAssetsBoard() {
        try {
            // 该资金接口通过内部域名调用，本地无法调用，只能部署之后才能正常调用
            String response = HttpClients.create().setUrl(zckbUrl).addHeader("Content-Type", "application/json")
                    .setRequestBody("{}").doPost();
            JSONObject json = JSONObject.parseObject(response);
            List<AssetsBoardVO> voResult;
            if ("0".equals(json.get("code"))) {
                voResult = JSONObject.parseObject(json.getString("result"),
                        new TypeReference<List<AssetsBoardVO>>() {
                        });
            } else {
                log.warn("资金看板查询接口返回失败数据: {}", response);
                return Collections.emptyList();
            }
            // 查询资金方编号接口，用于在返回的资产看板数据上拼接资金方编号数据
            Map<String, String> partnerNoMap = getPartnerNo();
            ListIterator<AssetsBoardVO> iterator = voResult.listIterator();
            while (iterator.hasNext()) {
                AssetsBoardVO vo = iterator.next();
                if (vo.getLimitAmount().longValue() > 0) {
                    String partnerNo = partnerNoMap.get(vo.getCompanyCode());
                    if (StringUtils.isNotBlank(partnerNo) && partnerNo.length() > 2){
                        partnerNo = partnerNo.substring(2);
                    }
                    vo.setCompanyNo(partnerNo);
                    vo.setState("1".equals(vo.getState()) ? "是" : "否");
                } else {
                    iterator.remove();
                }
            }
            return voResult;
        } catch (Exception e) {
            log.error("资金看板查询接口异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public WriteoffDTO getLoanTransferByApplicationId(String applicationId) {
        try {
            Response<WriteoffDTO> response = factorDubboService.getWriteOffByApplicationId(applicationId);
            if (Response.isSuccess(response)) {
                log.info("getLoanTransferByApplicationId 查询债转信息成功:{}", JSON.toJSONString(response));
                return response.getResult();
            } else {
                log.warn("getLoanTransferByApplicationId 查询债转信息失败:{}", JSON.toJSONString(response));
            }
        } catch (Exception e){
            log.error("getLoanTransferByApplicationId 查询债转信息异常", e);
        }

        return null;
    }

    @Override
    public String getSettlementUrl(String appNo, String partnerCode) {
        try {
            if (StringUtils.isBlank(appNo)) {
                log.error("getSettlementUrl,合同号不能为空");
            }
            GenerateSettlementProofDTO dto = new GenerateSettlementProofDTO();
            dto.setApplicationId(appNo);
            dto.setPartnerCode(partnerCode);
            log.info("getSettlementUrl,params:{}", JSON.toJSONString(dto));
            Response<GenerateSettlementProofVO> res = userAgreementDubboService.applySettlementProof(dto);
            log.info("getSettlementUrl res:{}", JSON.toJSONString(res));
            if (Response.isSuccess(res)) {
                return res.getResult().getContractDownPath();
            } else {
                log.warn("getSettlementUrl 获取结清证明失败,appNo:{},partnerCode:{}", appNo, partnerCode);
            }
        } catch (Exception e) {
            log.warn("getSettlementUrl 异常,appNo:" + appNo + ",partnerCode:" + partnerCode, e);
        }
        return "";
    }


    @Override
    public Response<List<String>> getRepayMode(String appNo, String repayOrigin) {
        // 参数校验
        if (appNo == null || appNo.trim().isEmpty() || repayOrigin == null || repayOrigin.trim().isEmpty()) {
            log.warn("getRepayMode Invalid parameters: appNo={}, repayOrigin={}", appNo, repayOrigin);
            throw new IllegalArgumentException("Invalid parameters");
        }

        try {
            // 调用服务
            log.info("getRepayMode appNo:{}, repayOrigin:{}", appNo, repayOrigin);
            Response<List<String>> result = repaymentCalculationDubboService.getRepaymentModeForCSAndKF(appNo, repayOrigin);
            log.info("getRepayMode res:{}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("Error occurred while getting repayment mode for appNo={}, repayOrigin={}", appNo, repayOrigin, e);
            throw new RuntimeException("Failed to get repayment mode", e);
        }
    }

    @Override
    public Response<RepayChannelVO> getRepayChannel(RepayChannelReq repayChannelReq) {
        // 尝试调用远程服务，并处理可能发生的异常
        try {
            log.info("getRepayChannel params:{}", JSON.toJSONString(repayChannelReq));
            Response<RepayChannelVO> response = repaymentCalculationDubboService.getRepaymentChannel(repayChannelReq);
            log.info("getRepayChannel res:{}", JSON.toJSONString(response));

            return response;
        } catch (Exception e) {
            log.warn("getRepayChannel error", e);
            throw new FastRuntimeException(e.getMessage());
        }
    }


    private Map<String, String> getPartnerNo() {
        try {
            // 该资金接口通过内部域名调用，本地无法调用，只能部署之后才能正常调用
            String response = HttpClients.create().setUrl(zckbListUrl).addHeader("Content-Type", "application/json").doGet();
            JSONObject json = JSONObject.parseObject(response);
            List<AssetsPartnerVO> voResult;
            if ("0".equals(json.get("code"))) {
                voResult = JSONObject.parseObject(json.getString("result"),
                        new TypeReference<List<AssetsPartnerVO>>() {
                        });
            } else {
                log.warn("资金方编号查询接口返回失败数据: {}", response);
                return new HashMap<>();
            }

            if (CollectionUtils.isNotEmpty(voResult)) {
                return voResult.stream().collect(Collectors.toMap(AssetsPartnerVO::getPartnerCode,
                        AssetsPartnerVO::getPayCode, (k1, k2) -> k1));
            } else {
                log.info("资金方编号查询接口没有返回任何数据");
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.warn("资金方编号查询接口异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    private Wrapper<InLenderWithholdRecord> getUpdateWrapper(String serviceNo) {
        return new UpdateWrapper<InLenderWithholdRecord>().eq("service_no", serviceNo);
    }

    private QueryWrapper<InLenderWithholdRecord> buildQueryWrapper(int count, Date date) {
        QueryWrapper<InLenderWithholdRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("withhold_result", "success");
        wrapper.eq("callback_result", "processing");

        // 默认查询近20分钟的数据，每小时查询一次3小时内的数据，每24小时查询一次近3天的数据
        if (count % 288 != 0 && count % 12 != 0) {
            wrapper.between("gmt_create", DateUtil.plusMins(date, -20), date);
        } else if (count % 288 == 0) {
            wrapper.between("gmt_create", DateUtil.plusDays(date, -3), date);
        } else if (count % 12 == 0) {
            wrapper.between("gmt_create", DateUtil.plusHours(date, -3), date);
        }
        return wrapper;
    }
}
