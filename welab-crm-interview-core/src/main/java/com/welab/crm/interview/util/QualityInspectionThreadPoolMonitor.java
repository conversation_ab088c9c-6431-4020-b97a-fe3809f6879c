package com.welab.crm.interview.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 质检线程池监控工具类
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
@Component
@Slf4j
public class QualityInspectionThreadPoolMonitor {

    /**
     * 监控线程池状态
     * @param executor 线程池执行器
     * @param poolName 线程池名称
     */
    public void monitorThreadPool(ThreadPoolExecutor executor, String poolName) {
        if (executor == null) {
            return;
        }

        int activeCount = executor.getActiveCount();
        int corePoolSize = executor.getCorePoolSize();
        int maximumPoolSize = executor.getMaximumPoolSize();
        int poolSize = executor.getPoolSize();
        long taskCount = executor.getTaskCount();
        long completedTaskCount = executor.getCompletedTaskCount();
        int queueSize = executor.getQueue().size();

        log.info("线程池[{}]状态监控 - 活跃线程数:{}, 核心线程数:{}, 最大线程数:{}, 当前线程数:{}, " +
                "总任务数:{}, 已完成任务数:{}, 队列任务数:{}", 
                poolName, activeCount, corePoolSize, maximumPoolSize, poolSize, 
                taskCount, completedTaskCount, queueSize);

        // 告警逻辑
        if (queueSize > maximumPoolSize * 0.8) {
            log.warn("线程池[{}]队列任务数过多，当前队列大小:{}, 建议检查任务处理速度", poolName, queueSize);
        }

        if (activeCount >= maximumPoolSize * 0.9) {
            log.warn("线程池[{}]活跃线程数接近最大值，当前活跃数:{}, 最大线程数:{}", poolName, activeCount, maximumPoolSize);
        }
    }

    /**
     * 获取线程池详细状态信息
     * @param executor 线程池执行器
     * @return 状态信息字符串
     */
    public String getThreadPoolStatus(ThreadPoolExecutor executor) {
        if (executor == null) {
            return "线程池未初始化";
        }

        return String.format(
                "活跃线程:%d, 核心线程:%d, 最大线程:%d, 当前线程:%d, 总任务:%d, 已完成:%d, 队列任务:%d",
                executor.getActiveCount(),
                executor.getCorePoolSize(),
                executor.getMaximumPoolSize(),
                executor.getPoolSize(),
                executor.getTaskCount(),
                executor.getCompletedTaskCount(),
                executor.getQueue().size()
        );
    }
}
