package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wo_attachment")
public class WoAttachment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 绑定工单表主键
     */
    private Long woTaskId;

    /**
     * 附件路径
     */
    private String path;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 上传人Id
     */
    private Long staffId;

    /**
     * 状态，1正常，0已删除
     */
    private Boolean status;


}
