package com.welab.crm.interview.service.impl;

import com.welab.crm.interview.bo.ConRepeatCallBO;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.service.ConRepeatCallService;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.xdao.context.page.Page;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
@Service
public class ConRepeatCallServiceImpl implements ConRepeatCallService {

    @Resource
    private ConRepeatCallBO conRepeatCallBO;

    @Override
    public Page<ReportResolvedRateVO> queryRecord(RepeatCallDTO dto) {
        return conRepeatCallBO.queryRecord(dto);
    }

    @Override
    public List<ReportResolvedRateVO> queryRecordList(RepeatCallDTO dto) {
        return conRepeatCallBO.queryRecordList(dto);
    }
}
