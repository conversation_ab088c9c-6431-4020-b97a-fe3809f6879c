package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.QualityInspectionCallbackDTO;
import com.welab.crm.interview.dto.QualityInspectionRequestDTO;
import com.welab.crm.interview.dto.QualityInspectionResultDTO;

/**
 * 质检服务接口
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
public interface QualityInspectionService {

    /**
     * 提交质检任务
     * @param requestDTO 质检请求参数
     * @return 任务ID
     */
    String submitQualityInspection(QualityInspectionRequestDTO requestDTO);

    /**
     * 获取质检结果
     * @param taskId 任务ID
     * @return 质检结果
     */
    QualityInspectionResultDTO getQualityInspectionResult(String taskId);

    /**
     * 处理质检回调
     * @param callbackDTO 回调数据
     */
    void handleQualityInspectionCallback(QualityInspectionCallbackDTO callbackDTO);

    /**
     * 异步提交电话录音质检
     * @param cdrMainUniqueId 通话唯一标识
     * @param recordFile 录音文件名
     * @param callType 通话类型
     * @param customerNumber 客户号码
     * @param agentNumber 座席号码
     */
    void submitPhoneRecordQualityInspection(String cdrMainUniqueId, String recordFile, 
                                          String callType, String customerNumber, String agentNumber);
}
