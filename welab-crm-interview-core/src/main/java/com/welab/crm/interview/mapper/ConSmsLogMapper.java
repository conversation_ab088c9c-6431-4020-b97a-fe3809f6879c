package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.ConSmsLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 短信发送历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ConSmsLogMapper extends BaseMapper<ConSmsLog> {

    List<ConSmsLog> listLogs(@Param("mobile") String mobile, @Param("customerId")  Long customerId);
}
