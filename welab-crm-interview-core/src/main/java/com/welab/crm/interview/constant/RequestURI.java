package com.welab.crm.interview.constant;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2018-7-27 17:46
 */
public class RequestURI {

	/**
	 * 放款邀约名单推送
	 */
	public static final String GET_AS_SIGN_TASK_FK = "/GetAssignTaskFk";

	/**
	 * 商户贷放款名单任务推送
	 */
	public static final String GET_AS_SIGN_TASK_SHDFK = "/GetAssignTaskShdfFk";

	/**
	 * 商户贷进件名单任务推送
	 */
	public static final String GET_AS_SIGN_TSKSHENTRY = "/GetAssignTskShEntry";

	/**
	 * 进件邀约名单任务推送
	 */
	public static final String GET_AS_SIGN_TSKENTRY = "/GetAssignTskEntry";

	/**
	 * 车险分期
	 */
	public static final String GET_INSURANCE = "/GetVehicleInsurance";

	/**
	 * 手机租赁取消用户
	 */
	public static final String GET_LEASE_ORDER = "/GetMobileRentalList";

	/**
	 * 用户信息
	 */
	public static final String GET_USER_INFO = "/usercenter/api/users/{0}";

	/**
	 * 注销用户
	 */
	public static final String GET_USER_BLOCK = "/usercenter/api/users/{0}/block";

	/**
	 * 手机租赁取消用户
	 */
	public static final String GET_DATA_CONFIRM = "/GetDataListConfirm";

	/**
	 * uuid导入电销系统做外呼
	 */
	public static final String GET_UUID_FROM_LIST = "/GetUuidFromList";
}
