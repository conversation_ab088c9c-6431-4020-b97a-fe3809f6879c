package com.welab.crm.interview.mapper;

import com.welab.crm.interview.domain.OpImportInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.vo.loan.LoanImportLabelVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 导入文件信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
public interface OpImportInfoMapper extends BaseMapper<OpImportInfo> {

	LoanImportLabelVO getImportLabelInfo(@Param("applicationId") String applicationId);

}
