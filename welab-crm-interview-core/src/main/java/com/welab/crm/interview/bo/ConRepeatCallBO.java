package com.welab.crm.interview.bo;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.keygen.KeyGenerator;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.welab.crm.interview.domain.ConRepeatCall;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.mapper.ConRepeatCallMapper;
import com.welab.crm.interview.util.ListUtil;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.xdao.context.page.Page;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
@Component
public class ConRepeatCallBO {

    @Resource
    private ConRepeatCallMapper conRepeatCallMapper;

    @Resource
    private KeyGenerator keyGenerator;

    /**
     * 添加2小时重复重复来电记录
     *
     * @param info
     */
    public void addRecord(ConPhoneCallInfo info) {
        // 非呼入数据不保存
        if (!"1".equals(info.getCdrCallType())) {
            return;
        }
        // 如果没分配到坐席也不保存
        if (!"1".equals(info.getCdrStatus()) && !"2".equals(info.getCdrStatus())){
            return;
        }
        ConRepeatCall conRepeatCall = conRepeatCallMapper.selectOne(Wrappers.lambdaQuery(ConRepeatCall.class)
            .eq(ConRepeatCall::getHotline, info.getCdrHotline())
            .eq(ConRepeatCall::getMobile, info.getCdrCustomerNumber())
//            .eq(ConRepeatCall::getCno, info.getCdrCalleeCno())
            .orderByDesc(ConRepeatCall::getGmtCreate)
            .last("limit 1"));
        if (Objects.isNull(conRepeatCall)) {
            conRepeatCall = new ConRepeatCall();
            conRepeatCall.setHotline(info.getCdrHotline());
            conRepeatCall.setCno(info.getCdrCalleeCno());
            conRepeatCall.setStaffId(info.getStaffId());
            conRepeatCall.setMobile(info.getCdrCustomerNumber());
            conRepeatCall.setRepeatNumber(0);
            conRepeatCallMapper.insert(conRepeatCall);
        } else {
            Date now = new Date();
            long interval = DateUtil.timePre(conRepeatCall.getGmtCreate(), now);
            if (interval <= 2 * DateUtil.DAY_HOUR_TIMES) {
                conRepeatCall.setRepeatNumber(conRepeatCall.getRepeatNumber() + 1);
                conRepeatCall.setGmtModify(now);
                conRepeatCallMapper.updateById(conRepeatCall);
            } else {
                conRepeatCall.setId(keyGenerator.generateKey());
                conRepeatCall.setHotline(info.getCdrHotline());
                conRepeatCall.setCno(info.getCdrCalleeCno());
                conRepeatCall.setStaffId(info.getStaffId());
                conRepeatCall.setMobile(info.getCdrCustomerNumber());
                conRepeatCall.setRepeatNumber(0);
                conRepeatCall.setGmtCreate(now);
                conRepeatCall.setGmtModify(now);
                conRepeatCallMapper.insert(conRepeatCall);
            }
        }
    }

    public Page<ReportResolvedRateVO> queryRecord(RepeatCallDTO dto) {
        int currentPage = dto.getCurrentPage();
        int rowsPerPage = dto.getRowsPerPage();
        List<ReportResolvedRateVO> list = getList(dto);
        int totalRows = list.size();
        list = ListUtil.subList(list, totalRows, rowsPerPage, currentPage);
        return Page.initPage(list, totalRows, rowsPerPage, currentPage);
    }

    public List<ReportResolvedRateVO> queryRecordList(RepeatCallDTO dto) {
        return getList(dto);
    }

    private List<ReportResolvedRateVO> getList(RepeatCallDTO dto) {
        List<String> hotlines = Arrays.asList(dto.getHotline().split(","));
        List<ReportResolvedRateVO> list = conRepeatCallMapper.selectCallList(dto, hotlines);
        int totalRepeatNumber = 0;
        int totalCallInNumber = 0;
        for (ReportResolvedRateVO vo : list) {
            totalRepeatNumber += vo.getRepeatNumber();
            totalCallInNumber += vo.getCallInNumber();
        }
        // 添加合计
        ReportResolvedRateVO vo = new ReportResolvedRateVO();
        vo.setCno("团队合计");
        vo.setRepeatNumber(totalRepeatNumber);
        vo.setCallInNumber(totalCallInNumber);
        vo.setResolvedRate(calcPercentage((totalCallInNumber - totalRepeatNumber), totalCallInNumber));
        list.add(vo);
        return list;
    }

    /**
     * 计算百分比
     *
     * @param number
     * @param total
     * @return
     */
    private String calcPercentage(Integer number, Integer total) {
        if (total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }
}
