package com.welab.crm.interview.util;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/4/26 11:01
 */
public class DateUtil {

    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 秒转换为时分秒的形式
     *
     * @param seconds 待转换的秒
     * @return
     */
    public static String secondsToTime(Integer seconds) {
        StringBuilder time = new StringBuilder();
        int hour = seconds / 3600;
        if (hour < 10) {
            time.append("0" + hour);
        } else {
            time.append(hour);
        }
        time.append(":");
        int minute = (seconds % 3600) / 60;
        if (minute < 10) {
            time.append("0" + minute);
        } else {
            time.append(minute);
        }
        time.append(":");
        int second = (seconds % 3600) % 60;
        if (second < 10) {
            time.append("0" + second);
        } else {
            time.append(second);
        }
        return time.toString();
    }

    /**
     * @title:获取当前日期的前几天或者后几天0点的时间
     * @param: day 天数 负数代表前几天，正数代表后几天
     * @return
     */
    public static Date getDateByDay(int day) {
        // 获取当前日期
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DATE, date.get(Calendar.DATE) + day);
        date.set(Calendar.HOUR_OF_DAY, 0);//时
        date.set(Calendar.MINUTE, 0);//分
        date.set(Calendar.SECOND, 0);//秒
        return date.getTime();
    }


    /**
     * 获取昨天的日期，格式为:yyyy-MM-dd
     * @return
     */
    public static String getYesterday(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        return sdf.format(calendar.getTime());
    }


    /**
     *  获取指定时间的日期
     * @param hour 小时
     * @param minute 分钟
     * @param second 秒
     * @param diffDays 间隔天数 如果是昨天则-1， 如果是明天则+1 ,当前时间则为0天
     * @return
     */
    public static Date getDateAtTime(int hour, int minute, int second, int diffDays) {
        Calendar day = Calendar.getInstance();
        day.add(Calendar.DAY_OF_YEAR, diffDays);
        day.set(Calendar.HOUR_OF_DAY, hour);
        day.set(Calendar.MINUTE, minute);
        day.set(Calendar.SECOND, second);
        day.set(Calendar.MILLISECOND, 0);

        // 获取Date对象
        return day.getTime();
    }


    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 计算两个日期字符串之间的月份差
     *
     * @param startDateStr 开始日期字符串，格式为 yyyy-MM-dd
     * @param endDateStr   结束日期字符串，格式为 yyyy-MM-dd
     * @return 两个日期之间的月份数
     * @throws IllegalArgumentException 如果日期字符串格式不正确
     */
    public static long calculateMonthDifference(String startDateStr, String endDateStr) {
        try {
            // 将日期字符串转换为 LocalDate 对象
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);

            // 计算两个日期之间的月份差
            return ChronoUnit.MONTHS.between(startDate, endDate);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误，必须为 yyyy-MM-dd", e);
        }
    }


    /**
     * 计算给定 Date 对象与当前时间之间的小时差。
     *
     * @param date 给定的 Date 对象
     * @return 小时差（包括小数部分），例如 1.5 小时
     * @throws IllegalArgumentException 如果输入的 date 为 null
     */
    public static double getHoursDifference(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("输入的 Date 对象不能为空");
        }
        Instant inputInstant = date.toInstant();
        Instant now = Instant.now();
        Instant start = inputInstant.isBefore(now) ? inputInstant : now;
        Instant end = inputInstant.isBefore(now) ? now : inputInstant;
        Duration duration = Duration.between(start, end);
        return duration.toMillis() / 3600000.0;
    }


    

}
