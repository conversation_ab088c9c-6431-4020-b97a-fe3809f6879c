package com.welab.crm.interview.service;

import com.welab.collection.interview.vo.PhoneRecordVO;
import com.welab.crm.interview.dto.ConPhoneCallInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/20 17:34
 */
public interface ConphoneCallInfoService {


    /**
     * 保存软电话回调记录
     * @param conPhoneCallInfoDTO
     * @return
     */
    String saveConPhoneCallBackInfo(ConPhoneCallInfoDTO conPhoneCallInfoDTO);

    /**
     * 查询软电话通话记录
     * @param queryDate
     * @return
     */
    List<PhoneRecordVO> queryRecord(String queryDate);
}
