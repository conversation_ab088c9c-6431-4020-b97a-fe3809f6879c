package com.welab.crm.interview.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/30
 */
public class LoanConstant {

    /**
     * 手机租赁产品
     */
    public static final List<String> RENTAL_PRODUCTS = Arrays.asList(
        "LOAN-RENT-WLD",
        "LOAN-RENT-XIANXIA",
        "LOAN-RENT-APP",
        "LOAN-RENT-ZFB",
        "LOAN-RENT-JD",
        "LOAN-RENT-JXZ"
    );

    /**
     * 审批终审码
     */
    public static final List<String> APPROVAL_STATE = Arrays.asList("A995-M-2", "A994");

    /**
     * 贷款状态：已放款
     */
    public final static String DISBURSED = "disbursed";

    /**
     * 提前结清来源
     */
    public final static String REPAY_ORIGIN = "kefu";
}
