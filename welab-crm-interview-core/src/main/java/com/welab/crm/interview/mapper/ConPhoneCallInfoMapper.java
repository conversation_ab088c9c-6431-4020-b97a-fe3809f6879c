package com.welab.crm.interview.mapper;

import com.welab.collection.interview.vo.PhoneRecordVO;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 软电话通话记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface ConPhoneCallInfoMapper extends BaseMapper<ConPhoneCallInfo> {

    /**
     * 查询通话记录
     * @param startDate
     * @param endDate
     * @return
     */
    List<PhoneRecordVO> queryPhoneRecord(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
