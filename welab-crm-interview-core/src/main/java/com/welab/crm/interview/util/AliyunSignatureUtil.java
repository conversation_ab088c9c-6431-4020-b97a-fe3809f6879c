package com.welab.crm.interview.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 阿里云质检回调签名验证工具类
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Slf4j
public class AliyunSignatureUtil {

    /**
     * 验证阿里云质检回调签名
     * @param taskId 任务ID
     * @param timestamp 时间戳
     * @param aliUid 阿里云主账号UID
     * @param signature 签名
     * @return 验证结果
     */
    public static boolean verifySignature(String taskId, String timestamp, String aliUid, String signature) {
        try {
            if (StringUtils.isAnyBlank(taskId, timestamp, aliUid, signature)) {
                log.warn("签名验证参数不完整: taskId={}, timestamp={}, aliUid={}, signature={}", 
                        taskId, timestamp, aliUid, signature);
                return false;
            }

            String expectedSignature = generateSignature(taskId, timestamp, aliUid);
            boolean isValid = signature.equals(expectedSignature);
            
            if (!isValid) {
                log.warn("签名验证失败: expected={}, actual={}", expectedSignature, signature);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 生成签名
     * @param taskId 任务ID
     * @param timestamp 时间戳
     * @param aliUid 阿里云主账号UID
     * @return 签名
     */
    public static String generateSignature(String taskId, String timestamp, String aliUid) {
        try {
            String data = "taskId=" + taskId + "&timestamp=" + timestamp + "&aliUid=" + aliUid;
            String signature = md5Base64(data);
            return URLEncoder.encode(signature, "utf-8");
        } catch (Exception e) {
            log.error("生成签名异常", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * MD5 + Base64 加密
     * @param str 待加密字符串
     * @return 加密结果
     */
    public static String md5Base64(String str) throws NoSuchAlgorithmException {
        // string 编码必须为 utf-8
        byte[] utfBytes = str.getBytes(StandardCharsets.UTF_8);
        MessageDigest mdTemp = MessageDigest.getInstance("MD5");
        mdTemp.update(utfBytes);
        byte[] md5Bytes = mdTemp.digest();
        return Base64.encodeBase64String(md5Bytes);
    }

    /**
     * 验证时间戳是否在有效范围内（防止重放攻击）
     * @param timestamp 时间戳（毫秒）
     * @param maxAgeMillis 最大允许的时间差（毫秒）
     * @return 是否有效
     */
    public static boolean isTimestampValid(String timestamp, long maxAgeMillis) {
        try {
            long callbackTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - callbackTime);
            
            boolean isValid = timeDiff <= maxAgeMillis;
            if (!isValid) {
                log.warn("时间戳验证失败: callbackTime={}, currentTime={}, diff={}, maxAge={}", 
                        callbackTime, currentTime, timeDiff, maxAgeMillis);
            }
            
            return isValid;
        } catch (NumberFormatException e) {
            log.warn("时间戳格式错误: {}", timestamp);
            return false;
        }
    }
}
