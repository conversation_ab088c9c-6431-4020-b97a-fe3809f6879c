package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.DictInfoService;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description: 字典服务
 * @date 2022/4/27 14:00
 */

@Service
public class DictInfoServiceImpl implements DictInfoService {

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Override
    public Boolean queryJobSwitch(String dictType) {
        List<OpDictInfo> list = opDictInfoMapper.selectList(
                Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "scheduledTaskSwitch")
                        .eq(OpDictInfo::getType, dictType).eq(OpDictInfo::getStatus, 1));
        if (CollectionUtils.isEmpty(list)){
            return false;
        }
        return true;
    }
}
