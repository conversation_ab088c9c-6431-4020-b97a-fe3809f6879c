package com.welab.crm.interview.constant;

/**
 * 还款计划费用类型
 *
 * <AUTHOR>
 */

public enum DueTypeEnum {
    OVERDUE_PENALTY("overdue_penalty", 1, "逾期费", "5"),
    MANAGEMENT_FEE("management_fee", 2, "管理费", "3"),
    HANDLING_FEE("handling_fee", 3, "手续费", "4"),
    EARLY_SETTLE_FEE("early_settle_fee", 4, "提前结清费", "6"),
    INTEREST("interest", 11, "利息", "2"),
    OUTSOURCING_FEE("outsourcing_fee", 9, "委外费", "11"),
    INSURANCE_FEE("insurance_fee", 10, "保费", "7"),
    RISK_SERVICE_FEE("risk_service_fee", 5, "风险服务费", "7"),
    RISK_MARGIN("risk_margin", 5, "风险保证金", "7"),
    GUARANTEE_FEE("guarantee_fee", 5, "担保费", "7"),
    PRINCIPAL("principal", 12, "本金", "1"),
    ASSESS_SERVICE_FEE("assess_service_fee", 6, "评审服务费", "7"),
    GUARANTEE_CONSULTING_FEE("guarantee_consulting_fee", 4, "担保咨询费", "7");

    private String value;
    private Integer rank;
    private String name;
    private String feeType;

    DueTypeEnum(String value, Integer rank, String name, String feeType) {
        this.value = value;
        this.rank = rank;
        this.name = name;
        this.feeType = feeType;
    }

    public static Integer getDueRank(String value) {
        for (DueTypeEnum dueTypeEnum : values()) {
            if (dueTypeEnum.getValue().equalsIgnoreCase(value)) {
                return dueTypeEnum.rank;
            }
        }
        return 0;
    }

    public static String getDueName(String value) {
        for (DueTypeEnum dueTypeEnum : values()) {
            if (dueTypeEnum.getValue().equalsIgnoreCase(value)) {
                return dueTypeEnum.getName();
            }
        }
        return null;
    }

    public static String getFeeType(String dueType) {
        for (DueTypeEnum dueTypeEnum : values()) {
            if (dueTypeEnum.getValue().equalsIgnoreCase(dueType)) {
                return dueTypeEnum.feeType;
            }
        }
        return "0";
    }

    public final String getValue() {
        return this.value;
    }

    public final String getFeeType() {
        return this.feeType;
    }

    public Integer getRank() {
        return rank;
    }

    public String getName() {
        return name;
    }

}
