package com.welab.crm.interview.util;

import com.alibaba.fastjson.JSONObject;
import com.welab.common.response.enums.ResponsCodeTypeEnum;

/**
 * <AUTHOR>
 * @date 2022-08-21
 */
public final class IvrMessageUtil {

    /**
     * 解析welab_voice_ivr模块返回的消息结果获取真正的处理结果字符串
     */
    public static String getMessageResult(String returnMessage) {
        JSONObject jsonObject = JSONObject.parseObject(returnMessage);
        String errMsg = null;
        String successCode = ResponsCodeTypeEnum.SUCCESS.getCode();
        if (!successCode.equals(jsonObject.getString("code"))) {
            errMsg = jsonObject.getString("message");
        } else if (!successCode.equals(jsonObject.getJSONObject("result").getString("code"))) {
            errMsg = jsonObject.getJSONObject("result").getString("msg");
        }
        return errMsg;
    }
}
