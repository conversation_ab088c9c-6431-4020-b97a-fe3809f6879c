package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.TmkLoanInvite;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 电销放款邀约表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-25
 */
public interface TmkLoanInviteMapper extends BaseMapper<TmkLoanInvite> {

    List<TmkLoanInvite> selectPushData(@Param("products") List<String> productNames,
                                       @Param("channels") List<String> channels,
                                       @Param("labels") List<String> labels,
                                       @Param("approvedAtStar") Date approvedAtStar,
                                       @Param("approvedAtEnd") Date approvedAtEnd,
                                       @Param("minAmount") Integer minAmount,
                                       @Param("maxAmount") Integer maxAmount,
                                       @Param("flag") String flag);

    void updatePushData(@Param("tmkTaskIds") Set<String> taskIds);
}
