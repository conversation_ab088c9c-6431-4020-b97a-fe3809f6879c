package com.welab.crm.interview.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
public class ListUtil {

    public static <T> List<T> subList(List<T> list, int totalRows, int rowsPerPage, int currentPage) {
        int offset = (currentPage - 1) * rowsPerPage;
        if (offset >= totalRows) {
            return new ArrayList<>();
        } else if ((offset + rowsPerPage) >= totalRows) {
            return list.subList(offset, totalRows);
        } else {
            return list.subList(offset, rowsPerPage * currentPage);
        }
    }
}
