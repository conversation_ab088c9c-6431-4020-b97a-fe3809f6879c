package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.bo.MessageBO;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 确认客户是否收到短信定时任务
 * @date 2021/12/14
 */
@Slf4j
public class SmsConfirmJob implements SimpleJob {

    @Resource
    private MessageBO messageBO;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("SmsConfirmJob start.");
            doJob();
            log.info("SmsConfirmJob end, takes {} ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("SmsConfirmJob error. ", e);
        }
    }

    private void doJob() {
        messageBO.confirmReceived();
    }
}
