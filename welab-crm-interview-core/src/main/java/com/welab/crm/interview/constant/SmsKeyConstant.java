package com.welab.crm.interview.constant;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/9
 */
public class SmsKeyConstant {

    /**
     * 客户姓名
     */
    public static final String NAME = "name";

    /**
     * 人脸验证链接
     */
    public static final String URL = "url";



    /**
     * 视频客服token
     */
    public static final String TOKEN = "token";

    /**
     * token 长度
     */
    public static final int TOKEN_LENGTH = 8;

    /**
     * token 有效时间，单位 秒
     */
    public static final int TOKEN_VALID_TIME = 60 * 60 * 12;


    /**
     * redis key前缀,判断链接是否有效
     */
    public static final String REDIS_PRE_TOKEN_KEY = "kf_sp_token_";

    /**
     * 判断是否可以上传视频
     */
    public static final String REDIS_PRE_UPLOAD_KEY = "kf_sp_upload_";


    /**
     * 链接点击次数
     */
    public static final String LINK_COUNT = "3";
}
