package com.welab.crm.interview.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 */
public class NumberUtil {

    public static boolean gte(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) >= 0;
    }

    public static boolean gt(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) > 0;
    }

    public static boolean eq(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) == 0;
    }

    public static boolean neq(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) != 0;
    }

    public static boolean lt(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) < 0;
    }

    public static boolean lte(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) <= 0;
    }

    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) > 0 ? a : b;
    }

    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) < 0 ? a : b;
    }

    public static BigDecimal min(BigDecimal... nums) {
        Arrays.sort(nums);
        return nums[0];
    }

    public static BigDecimal nullAsZero(BigDecimal num) {
        return Objects.isNull(num) ? BigDecimal.ZERO : num;
    }

    public static boolean gtZero(BigDecimal number) {
        return Objects.isNull(number) ? false : gt(number, BigDecimal.ZERO);
    }

    public static boolean eqZero(BigDecimal number) {
        return Objects.isNull(number) ? true : eq(number, BigDecimal.ZERO);
    }

    public static boolean neqZero(BigDecimal number) {
        return Objects.isNull(number) ? false : neq(number, BigDecimal.ZERO);
    }


    public static String calcPercentage(Integer number, Integer total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }
}
