package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.OpDictInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2021-10-18
 */
public interface OpDictInfoMapper extends BaseMapper<OpDictInfo> {


    /**
     * 查询字典
     * @param category
     * @param type
     * @return
     */
    List<OpDictInfo> getAllByCategoryAndType(@Param("category") String category, @Param("type") String type);

}
