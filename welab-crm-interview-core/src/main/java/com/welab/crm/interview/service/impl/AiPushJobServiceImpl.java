package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.constant.ApplicationConstant;
import com.welab.crm.interview.domain.AiTmkConfig;
import com.welab.crm.interview.dto.ai.AiTmkConfigDTO;
import com.welab.crm.interview.handler.ElasticJobHandler;
import com.welab.crm.interview.job.AiPushJob;
import com.welab.crm.interview.mapper.AiTmkConfigMapper;
import com.welab.crm.interview.service.AiPushJobService;
import com.welab.crm.interview.service.AiPushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;

@Service
@Slf4j
public class AiPushJobServiceImpl implements AiPushJobService {

    @Resource
    private AiPushService aiPushService;

    @Resource
    private ElasticJobHandler elasticJobHandler;

    @Resource
    private AiTmkConfigMapper configMapper;

    /**
     * 初始化数据库中配置的推送任务,以应对应用服务器重启的问题
     */
    @PostConstruct
    public void init() {
        LambdaQueryWrapper<AiTmkConfig> wrapper = Wrappers.<AiTmkConfig>lambdaQuery()
                .eq(AiTmkConfig::getState, Boolean.TRUE)
                .eq(AiTmkConfig::getDeleteFlag, Boolean.FALSE);
        List<AiTmkConfig> configs = configMapper.selectList(wrapper);
        log.info("Init ai Job ids: {}", configs);
        for (AiTmkConfig config : configs) {
            String jobName = ApplicationConstant.PREFIX_JOB + config.getId();
            log.info("Init ai Job start, id: {}", config.getId());
            LocalTime startTime = config.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            LocalTime endTime = config.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            String cron = generateCron(startTime.getHour(), endTime.getHour(), config.getNumber());
            elasticJobHandler.addJob(jobName, new AiPushJob(aiPushService), cron, 1, config.getId() + "");
            log.info("Init ai Job end, id: {}", config.getId());
        }
    }

    @Override
    public void createPushJob(AiTmkConfigDTO addDTO) {
        log.info("createPushJob start, id: {}", addDTO.getId());
        String jobName = ApplicationConstant.PREFIX_JOB + addDTO.getId();
        String cron = generateCron(addDTO.getStartHour(), addDTO.getEndHour(), addDTO.getNumber());
        elasticJobHandler.addJob(jobName, new AiPushJob(aiPushService), cron, 1, addDTO.getId() + "");
        log.info("createPushJob end, Id: {}, cron: {}", addDTO.getId(), cron);
    }

    @Override
    public void batchCreatePushJob(List<AiTmkConfigDTO> addDTOList) {
        log.info("batchCreatePushJob start...");
        for (AiTmkConfigDTO addDTO : addDTOList) {
            log.info("batchCreatePushJob id: {}", addDTO.getId());
            createPushJob(addDTO);
        }
        log.info("batchCreatePushJob end.");
    }

    @Override
    public void updatePushJob(AiTmkConfigDTO updateDTO) {
        log.info("updatePushJob start, Id: {}", updateDTO.getId());
        String jobName = ApplicationConstant.PREFIX_JOB + updateDTO.getId();
        String cron = updateDTO.getCron();
        if (StringUtils.isBlank(cron)) {
            cron = generateCron(updateDTO.getStartHour(), updateDTO.getEndHour(), updateDTO.getNumber());
        }
        elasticJobHandler.updateJob(jobName, cron);
        log.info("updatePushJob end, Id: {}, cron: {}", updateDTO.getId(), cron);
    }

    @Override
    public void deletePushJob(Long id) {
        log.info("deletePushJob start, Id: {}", id);
        String jobName = ApplicationConstant.PREFIX_JOB + id;
        elasticJobHandler.removeJob(jobName);
        log.info("deletePushJob end, Id: {}", id);
    }

    @Override
    public void batchDeletePushJob(List<Long> taskIdList) {
        log.info("batchDeletePushJob start...");
        for (Long id : taskIdList) {
            log.info("batchDeletePushJob id: {}", id);
            deletePushJob(id);
        }
        log.info("batchDeletePushJob end.");
    }

    /**
     * 生成cron定时表达式
     *
     * @param startHour 开始时间节点
     * @param endHour   结束时间节点
     * @param period    间隔时间
     * @return 一个可用于定时器的cron表达式
     */
    private String generateCron(Integer startHour, Integer endHour, int period) {
        /*
         * 最终的格式形如: 0 0 9-18/2 * * ? * (含义: 每天9:00-18:00每隔两个小时触发一次任务)
         * 注意: 当间隔数字大于两个时间的区间值时将只在开始时间执行一次任务
         */
        return "0 0 " + startHour + "-" + endHour + "/" + period + " * * ? *";
    }
}
