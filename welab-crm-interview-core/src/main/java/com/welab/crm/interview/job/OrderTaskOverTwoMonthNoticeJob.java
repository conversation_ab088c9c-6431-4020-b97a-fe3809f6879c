package com.welab.crm.interview.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.interview.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 工单未结案60天通知任务
 */
@Slf4j
public class OrderTaskOverTwoMonthNoticeJob implements SimpleJob {


	@Resource
	private WorkOrderService workOrderService;

	@Override
	public void execute(ShardingContext shardingContext) {
		long startTime = System.currentTimeMillis();
		log.info("OrderTaskOverTwoMonthNoticeJob start");
		workOrderService.queryNotCloseOrderAndNotice(60, null);
		log.info("OrderTaskOverTwoMonthNoticeJob end, use time: {}ms", System.currentTimeMillis() - startTime);
	}
}
