package com.welab.crm.interview.service.impl;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.domain.OpTempFile;
import com.welab.crm.interview.mapper.OpTempFileMapper;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.util.AliyunOssUtil;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.ImageRemarkUtil;
import com.welab.document.interfaces.facade.OssClientServiceFacade;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
@Slf4j
@Service("uploadService")
public class UploadServiceImpl implements IUploadService {
	@Value("${oss.prefix}")
	private String aliyunPath;
	@Autowired
	private AliyunOssUtil aliyunOssUtil;
	@Resource
	private OssClientServiceFacade ossClientServiceFacade;
	
	@Resource
	private OpTempFileMapper opTempFileMapper;

	private static final List<String> ADD_WATERMARK_EXT = new ArrayList<>(
			Arrays.asList("pdf", "jpg", "png", "gif", "bmp", "jpeg"));
	@Override
	public Response<String> uploadFile(byte[] bytes,String fileName) {
		String result=null;
		String imageName = null;
			try{
				log.info("开始导入：{}", fileName);
				imageName = UUID.randomUUID()+fileName;
				InputStream in = new ByteArrayInputStream(bytes);
				boolean upRet= aliyunOssUtil.writeFileNew(aliyunPath+imageName,in);
				if (upRet) {
					result="上传阿里云文件服务器成功";
					saveToTempFile(imageName);
					log.info("{}：{}",result,imageName);
				}else {
					result="上传阿里云失败";
					log.error("{}：{}",result,imageName);
					throw new FastRuntimeException(result);
				}
			
			}catch (Exception e) {
				String msg = e.getMessage();
				log.error(msg);
				return new Response<String>(ResponsCodeTypeEnum.FAILURE.getCode(),result);
			}
		return new Response<String>(ResponsCodeTypeEnum.SUCCESS.getCode(),imageName);
	}

	private void saveToTempFile(String imageName) {
		OpTempFile opTempFile = new OpTempFile();
		opTempFile.setFilename(imageName);
		opTempFileMapper.insert(opTempFile);
	}

	@Override
	public Map<String, Object> getUploadFile(List<String> fileList) {
		Map<String, Object>  retMap = new HashMap<String, Object>(fileList.size());
		log.info("调用消金接口获取ossUrl");
		for (String imageName : fileList) {
			String result= ossClientServiceFacade.getOssUrl(aliyunPath+imageName,1000 * 60 * 60 * 12L );
			retMap.put(imageName,result);
		}
		return retMap;
	}

	@Override
	public Map<String, Object> getUploadFileSurvivalTime(List<String> fileList, long seconds) {
		Map<String, Object>  retMap = new HashMap<String, Object>(fileList.size());
		log.info("getUploadFileSurvivalTime,调用消金接口获取ossUrl");
		for (String imageName : fileList) {
			String path = "";
			if (imageName.startsWith(aliyunPath)){
				path = imageName;
			} else {
				path = aliyunPath + imageName;
			}
            String result = ossClientServiceFacade.getOssUrl(path, seconds * 1000);
			retMap.put(imageName,result);
		}
		return retMap;
	}

	@Override
	public Response<byte[]> getFile(String fileName) {
		Response<byte[]> response = new Response<byte[]>();
		byte[] bytes = null;
		try {
			bytes = aliyunOssUtil.readFileNew(aliyunPath + fileName);
		} catch (Exception e) {
			return new Response<byte[]>(ResponsCodeTypeEnum.FAILURE.getCode(),e.getMessage(), null);
		}
		response.setResult(bytes);
		return response;
	}
	public static byte[] toByteArray(InputStream input) throws IOException {
		ByteArrayOutputStream output = new ByteArrayOutputStream();
		byte[] buffer = new byte[4096];
		int n = 0;
		while (-1 != (n = input.read(buffer))) {
			output.write(buffer, 0, n);
		}
		return output.toByteArray();
	}

	@Override
	public Response<byte[]> getWatermarkFile(String path, String fileName,String watermarkContent) {
		Response<byte[]> response = new Response<>();
		byte[] watermarkFile = null;
		String ext = ImageRemarkUtil.getExt(fileName).toLowerCase();
		String truePath = (String) getUploadFile(Arrays.asList(fileName)).get(fileName);
		if (ADD_WATERMARK_EXT.contains(ext)) {
			watermarkFile = ImageRemarkUtil.getWatermarkFile(truePath, fileName, watermarkContent);
		} else {
			watermarkFile = HttpClientUtil.getFile(truePath);
		}
		response.setResult(watermarkFile);
		return response;
	}

	@Override
	public Response<byte[]> getWatermarkFileByAppNo(String path, String fileName, String watermarkContent) {
		Response<byte[]> response = new Response<>();
		byte[] watermarkFile = null;
		String ext = ImageRemarkUtil.getExt(fileName).toLowerCase();
		if (ADD_WATERMARK_EXT.contains(ext)) {
			watermarkFile = ImageRemarkUtil.getWatermarkFile(path, fileName, watermarkContent);
		} else {
			watermarkFile = HttpClientUtil.getFile(path);
		}
		response.setResult(watermarkFile);
		return response;
	}


}
