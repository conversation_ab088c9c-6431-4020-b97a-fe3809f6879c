package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.dto.complain.ComplainDTO;
import com.welab.collection.interview.dto.complain.ComplainResultDTO;
import com.welab.collection.interview.service.ComplainService;
import com.welab.collection.interview.vo.complain.CollectionComplainVO;
import com.welab.collection.interview.vo.complain.ComplainDictVO;
import com.welab.collection.interview.vo.complain.CustomerBlackListVO;
import com.welab.crm.interview.domain.OpCustomerComplain;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.mapper.OpCustomerComplainMapper;
import com.welab.crm.interview.service.ComplainOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ComplainOrderServiceImpl implements ComplainOrderService {

    /**
     * 催收系统投诉的服务
     */
    @Resource
    private ComplainService complainService;

    @Resource
    private OpCustomerComplainMapper complainMapper;

    @Override
    public void pushComplainOrder(ComplainDTO complainDTO) {
        try {
            log.info("pushComplainOrder to collection, param: {}", complainDTO);
            List<CollectionComplainVO> complainVOList = complainService.addComplainCase(complainDTO);
            // 保存合同号对应的客户投诉记录
            for (CollectionComplainVO complainVO : complainVOList) {
                OpCustomerComplain complain = new OpCustomerComplain();
                BeanUtils.copyProperties(complainDTO, complain);
                complain.setContractNo(complainVO.getContractNo());
                complain.setCustNo(complainVO.getCustNo());
                complain.setOrg(complainVO.getOrg());
                complain.setGroupCode(complainVO.getGroupCode());
                complain.setStaffId(complainVO.getStaffId());
                if (complainVO.getApproveStatus() != null) {
                    complain.setApproveStatus(complainVO.getApproveStatus());
                }
                complain.setReviewTime(complainVO.getReviewTime());
                complain.setReviewResult(complainVO.getReviewResult());
                complain.setApproveResult(complainVO.getApproveResult());
                complain.setApproveTime(complainVO.getApproveTime());
                complainMapper.insert(complain);
            }
        } catch (Exception e) {
            log.warn("pushComplainOrder exception: {}", e.getMessage(), e);
            throw new CrmInterviewException("推送工单投诉数据到催收系统异常");
        }
    }

    @Override
    public void returnResultForUpdate(ComplainResultDTO resultDTO) {
        OpCustomerComplain complain = new OpCustomerComplain();
        complain.setApproveResult(resultDTO.getApproveResult());
        complain.setReviewResult(resultDTO.getReviewResult());
        complain.setApproveStatus(resultDTO.getApproveStatus());
        complain.setGmtModify(new Date());
        complain.setUrgeStatus(resultDTO.getUrgeStatus());
        complain.setRemark(resultDTO.getRemark());
        complain.setApproveTime(resultDTO.getApproveTime());
        complain.setReviewTime(resultDTO.getReviewTime());
        LambdaUpdateWrapper<OpCustomerComplain> wrapper = Wrappers.<OpCustomerComplain>lambdaUpdate()
                .eq(OpCustomerComplain::getWorkOrderNo, resultDTO.getWorkOrderNo())
                .eq(StringUtils.isNotBlank(resultDTO.getContractNo()), OpCustomerComplain::getContractNo, resultDTO.getContractNo());
        int count = complainMapper.update(complain, wrapper);
        if (count == 0) {
            log.warn("催收要更新的工单号对应的投诉不存在，工单号为: {}, 开始数据补偿操作", resultDTO.getWorkOrderNo());
            BeanUtils.copyProperties(resultDTO, complain);
            complainMapper.insert(complain);
        }
    }

    @Override
    public void updateComplainUrgeStatus(List<String> orderNoList) {
        try {
            complainService.updateComplainUrgeStatus(orderNoList);
        } catch (Exception e) {
            log.warn("updateComplainUrgeStatus exception: {}", e.getMessage(), e);
            throw new CrmInterviewException("催促催收投诉状态发生异常");
        }
    }

    @Override
    public void updateComplainFileList(ComplainDTO complainDTO) {
        try {
            complainService.updateComplainFileList(complainDTO);
        } catch (Exception e) {
            log.warn("updateComplainFileList exception: {}", e.getMessage(), e);
            throw new CrmInterviewException("增量更新催收投诉文件列表发生异常");
        }
    }

    @Override
    public ComplainDictVO getComplainDictList() {
        return complainService.getComplainDictList();
    }

    @Override
    public List<CustomerBlackListVO> getUserBlackList(Integer userId, String mobile) {
       return complainService.getUserBlackList(userId, mobile);
    }
}
