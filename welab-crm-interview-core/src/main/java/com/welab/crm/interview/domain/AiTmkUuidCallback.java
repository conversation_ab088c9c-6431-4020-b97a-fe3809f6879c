package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_tmk_uuid_callback")
public class AiTmkUuidCallback implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * ai推送主表关联id
     */
    private Long configId;

    /**
     * 电销唯一任务Id
     */
    private String tmkTaskId;

    /**
     * 供应商名称
     */
    private String channelName;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 呼叫次数
     */
    private String callNum;

    /**
     * 外呼开始时间
     */
    private Date callTime;

    /**
     * 外呼接通时间
     */
    private Date answerTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 通话时长
     */
    private Integer billSec;

    /**
     * 号码状态
     */
    private String phoneStatus;

    /**
     * 外呼结果
     */
    private String callResult;

    /**
     * 还款意愿
     */
    private String willingCol;

    /**
     * 系统外呼执行状态(同盾)
     */
    private String sysCallStatus;

    /**
     * 回调日期
     */
    private Date callBackDate;

    /**
     * 录音地址(自研机器人)
     */
    private String recordUrl;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
