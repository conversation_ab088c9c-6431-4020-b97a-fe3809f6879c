package com.welab.crm.interview.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * h5还款链接发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_repay_link_record")
public class CsRepayLinkRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 发送号码
     */
    private String sendMobile;

    /**
     * 还款方式
     */
    private String repayMode;

    /**
     * 还款金额
     */
    private BigDecimal repayAmount;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送员工ID
     */
    private Long sendStaffId;

    /**
     * 发送人名称
     */
    private String sendStaffName;

    /**
     * 发送组
     */
    private String sendStaffGroup;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 还款完成时间
     */
    private Date repayTime;

    /**
     * 还款结果
     */
    private String repayResult;

    /**
     * 咨询状态；1-已逾期；2-待还款
     */
    private Integer consultationStatus;


    /**
     * 还款方式
     * helpPayment - 线上帮还
     * offlinePayment - 线下还款
     */
    private String repayMethod;


}
