package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.AiTmkPush;
import com.welab.crm.interview.domain.TmkLoanInvite;
import com.welab.crm.interview.mapper.AiTmkPushMapper;
import com.welab.crm.interview.mapper.TmkLoanInviteMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 具体保存推送过后用户的数据状态更新和推送历史数据的服务，为使得事务最小化占用时间而单独设置
 *
 * <AUTHOR>
 * @date 2022-03-18
 */
@Service
public class AiPushSaveService {

    @Resource
    private TmkLoanInviteMapper inviteMapper;

    @Resource
    private AiTmkPushMapper pushMapper;

    /**
     * 用电销唯一任务id做更新操作，更新已被打过电话的名单池中用户的数据状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePushDataState(List<TmkLoanInvite> invites) {
        Set<String> tmkTaskIds = invites.stream().map(TmkLoanInvite::getTmkTaskId).collect(Collectors.toSet());
        inviteMapper.updatePushData(tmkTaskIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePushHistory(Long id, int count) {
        Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<AiTmkPush> wrapper = Wrappers.<AiTmkPush>lambdaQuery()
                .eq(AiTmkPush::getConfigId, id).eq(AiTmkPush::getPushDay, date);
        AiTmkPush push = pushMapper.selectOne(wrapper);
        if (push == null) {
            // 做插入操作
            AiTmkPush insertPush = new AiTmkPush();
            insertPush.setCreateUser("AiPushJob");
            insertPush.setQuantities(count);
            insertPush.setPushDay(new Date());
            insertPush.setConfigId(id);
            pushMapper.insert(insertPush);
        } else {
            // 做更新操作
            push.setQuantities(push.getQuantities() + count);
            push.setLstUpdUser("AiPushJob");
            push.setGmtModify(new Date());
            pushMapper.updateById(push);
        }
    }
}
