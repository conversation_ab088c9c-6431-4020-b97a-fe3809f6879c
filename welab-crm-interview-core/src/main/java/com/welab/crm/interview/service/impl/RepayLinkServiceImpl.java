package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.utils.DateUtils;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.domain.CsRepayLinkRecord;
import com.welab.crm.interview.mapper.CsRepayLinkRecordMapper;
import com.welab.crm.interview.service.RepayLinkService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.UserBlackInfoVO;
import com.welab.finance.repayment.dto.UserRepaysDTO;
import com.welab.finance.repayment.dubbo.RepaymentRecordDubboService;
import com.welab.finance.repayment.enums.RepayOriginEnum;
import com.welab.finance.repayment.vo.UserRepaysVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * h5还款链接相关接口
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class RepayLinkServiceImpl implements RepayLinkService {

    @Resource
    private CsRepayLinkRecordMapper csRepayLinkRecordMapper;

    @Resource
    private UserInfoServiceImpl userInfoServiceimpl;

    @Resource
    private RepaymentRecordDubboService repaymentRecordDubboService;

    @Override
    public void saveRepayResult(Date queryDate) {
        Date startTime = DateUtils.getStartOfDate(queryDate);
        Date endTime = DateUtils.getEndOfDate(queryDate);
        List<CsRepayLinkRecord> repayLinkRecords = csRepayLinkRecordMapper.selectList(
            Wrappers.lambdaQuery(CsRepayLinkRecord.class).between(CsRepayLinkRecord::getSendTime, startTime, endTime));

        if (CollectionUtils.isEmpty(repayLinkRecords)) {
            return;
        }

        // 根据uuid分组
        Map<String, List<CsRepayLinkRecord>> uuidRepayLinkRecordMap =
            repayLinkRecords.stream().collect(Collectors.groupingBy(CsRepayLinkRecord::getUuid));

        uuidRepayLinkRecordMap.forEach((uuid, records) -> {
            Long userId = records.get(0).getUserId();
            if (Objects.isNull(userId)) {
                try {
                    UserBlackInfoVO userInfo = userInfoServiceimpl.getUserIdByUuid(uuid);
                    if (Objects.nonNull(userInfo)) {
                        userId = Long.valueOf(userInfo.getUserId());
                    }
                } catch (Exception e) {
                    log.warn("saveRepayResult getUserIdByUuid error", e);
                }
            }
            if (Objects.isNull(userId)) {
                log.info("saveRepayResult 没查询到userId，uuid:{}", uuid);
                return;
            }

            for (CsRepayLinkRecord record : records) {
                if (Objects.isNull(record.getUserId())){
                    record.setUserId(userId);
                    record.setGmtModify(new Date());
                    csRepayLinkRecordMapper.updateById(record);
                }
            }
            // 查询该客户当天全部的还款记录
            UserRepaysDTO userRepaysDTO = new UserRepaysDTO();
            userRepaysDTO.setUserId(Math.toIntExact(userId));
            userRepaysDTO.setSettledAtStartDate(startTime);
            userRepaysDTO.setSettledAtEndDate(endTime);
            Response<List<UserRepaysVO>> response =
                repaymentRecordDubboService.queryUserRepaysByCondition(userRepaysDTO);
            if (Response.isSuccess(response)) {
                List<UserRepaysVO> repaysVOList = response.getResult();
                if (CollectionUtils.isEmpty(repaysVOList)) {
                    return;
                }
                // 筛选出线上帮还的记录
                List<UserRepaysVO> helpRepayList = repaysVOList.stream()
                    .filter(repayVO -> RepayOriginEnum.HELPPAYMENT.getCode().equals(repayVO.getRepayOrigin()))
                    .collect(Collectors.toList());

                for (UserRepaysVO repaysVO : helpRepayList) {
                    // 再查询出跟帮还记录一样的贷款号，并且发送时间在还款时间之前，还款方式一样并且时间最接近的一笔还款链接，更新还款结果
                    List<CsRepayLinkRecord> filterList = records.stream()
                        .filter(record -> record.getApplicationId().equals(repaysVO.getApplicationId())
                            && record.getSendTime().before(repaysVO.getSettledAt())
                            && RepayOriginEnum.HELPPAYMENT.getCode().equals(record.getRepayMethod()))
                        .sorted(Comparator.comparing(CsRepayLinkRecord::getSendTime).reversed())
                        .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        CsRepayLinkRecord needUpdateRecord = filterList.get(0);
                        updateRepayResult(needUpdateRecord, repaysVO);
                    }
                }
                
                
                //筛选出线下帮还的记录
                List<UserRepaysVO> offlineRepayList = repaysVOList.stream()
                        .filter(repayVO -> RepayOriginEnum.OFFLINEPAYMENT.getCode().equals(repayVO.getRepayOrigin()))
                        .collect(Collectors.toList());

                for (UserRepaysVO repaysVO : offlineRepayList) {
                    {
                        // 再查询出跟帮还记录一样的贷款号，并且发送时间在还款时间之前，还款方式一样并且时间最接近的一笔还款链接，更新还款结果
                        List<CsRepayLinkRecord> filterList = records.stream()
                                .filter(record -> record.getApplicationId().equals(repaysVO.getApplicationId())
                                        && record.getSendTime().before(repaysVO.getSettledAt())
                                        && RepayOriginEnum.OFFLINEPAYMENT.getCode().equals(record.getRepayMethod()))
                                .sorted(Comparator.comparing(CsRepayLinkRecord::getSendTime).reversed())
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(filterList)) {
                            CsRepayLinkRecord needUpdateRecord = filterList.get(0);
                            updateRepayResult(needUpdateRecord, repaysVO);
                        }
                    }
                }
                
            }
        });
    }

    private void updateRepayResult(CsRepayLinkRecord needUpdateRecord, UserRepaysVO repaysVO) {
        if (Objects.nonNull(needUpdateRecord)) {
            if (StringUtils.isBlank(needUpdateRecord.getRepayResult())) {
                needUpdateRecord.setRepayResult(repaysVO.getStatus());
                needUpdateRecord.setPaymentChannel(repaysVO.getDebitChannelCode());
                needUpdateRecord.setRepayTime(repaysVO.getSettledAt());
                needUpdateRecord.setGmtModify(new Date());
                csRepayLinkRecordMapper.updateById(needUpdateRecord);
            }
        }
        
    }

}
