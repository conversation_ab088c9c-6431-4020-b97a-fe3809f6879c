package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单组合表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_dict_info_conf")
public class OpDictInfoConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeId;

    /**
     * 工单大类描述
     */
    private String woTypeDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeFirId;

    /**
     * 工单一类描述
     */
    private String woTypeFirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeSecId;

    /**
     * 工单二类描述
     */
    private String woTypeSecDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeThirId;

    /**
     * 工单三类描述
     */
    private String woTypeThirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeChildId;

    /**
     * 子工单描述
     */
    private String woTypeChildDetail;

    /**
     * 工单模板描述
     */
    private String description;

    /**
     * 状态；1-有效，2-无效
     */
    private Integer isStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 投诉级别: normal-无投诉, highRisk-高危投诉, complain-投诉
     */
    private String complainLevel;

    /**
     * 需要核实结果: 0-不需要核实,1-需要核实
     */
    private Boolean mustReview;

    /**
     * 需要处理结果: 0-不需要处理,1-需要处理
     */
    private Boolean mustApprove;

    /**
     * 排序属性
     */
    private Integer sort;

    /**
     * 快捷键描述
     */
    private String detail;

    /**
     * 快捷键标识
     */
    private String fastStatus;

    /**
     * 所属组code
     */
    private String groupCode;

    /**
     * 百融问题类型
     */
    private String bairongQuestionType;


}
