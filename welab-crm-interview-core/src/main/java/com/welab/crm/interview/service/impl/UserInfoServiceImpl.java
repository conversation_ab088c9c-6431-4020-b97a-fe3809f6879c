package com.welab.crm.interview.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.welab.bank.card.interfaces.facade.BankCardServiceFacade;
import com.welab.collection.interview.dto.UserOverdueObject;
import com.welab.collection.interview.service.BlackListService;
import com.welab.collection.interview.service.IUserCenterService;
import com.welab.collection.interview.vo.blacklist.BlackListVO;
import com.welab.common.response.Response;
import com.welab.common.utils.BeanUtil;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.IDCardUtils;
import com.welab.crm.interview.constant.BusiConstant;
import com.welab.crm.interview.constant.StateCode;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.UserInfoDTO;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.enums.DegreeEnum;
import com.welab.crm.interview.enums.QuotaStateEnum;
import com.welab.crm.interview.enums.RelationshipEnum;
import com.welab.crm.interview.exception.CrmInterviewException;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.util.HttpClientUtil;
import com.welab.crm.interview.util.IdCardUtil;
import com.welab.crm.interview.util.StringUtil;
import com.welab.crm.interview.vo.*;
import com.welab.crm.interview.vo.vip.VipTypeVO;
import com.welab.domain.vo.ResponseVo;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.bankcard.dubbo.BankCardDubboService;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.enums.OwnerTypeEnum;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.loanapplication.interfaces.dto.EcLoanOrdersDTO;
import com.welab.loanapplication.interfaces.dto.EcOrderDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.loancenter.interfaces.dto.MemberOrderQueryDTO;
import com.welab.loancenter.interfaces.dto.VipMemberResponseDTO;
import com.welab.loancenter.interfaces.enums.MemberCategoryEnum;
import com.welab.loancenter.interfaces.enums.VipCardTypeEnum;
import com.welab.loancenter.interfaces.facade.NewMemberServiceFacade;
import com.welab.support.credit.dto.GetAvalQuotaReq;
import com.welab.support.credit.dto.GetAvalQuotaResp;
import com.welab.support.credit.service.QuotaService;
import com.welab.user.interfaces.dto.NotesDTO;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.NotesServiceFacade;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.enums.EnumAddressType;
import com.welab.usercenter.model.UserInfo;
import com.welab.usercenter.model.UserProfileInfo;
import com.welab.usercenter.model.base.*;
import com.welab.usercenter.service.AdminService;
import com.welab.usercenter.service.EducationService;
import com.welab.usercenter.service.ProfileService;
import com.welab.usercenter.service.UserService;
import com.welab.wallet.app.dubbo.BillDubboService;
import com.welab.wallet.app.dubbo.WalletUserDubboService;
import com.welab.wallet.app.vo.WalletUserVo;
import com.welab.wallet.application.interfaces.dto.CreditApplicationDTO;
import com.welab.wallet.application.interfaces.dto.WalletCreditTransformBatchQueryDTO;
import com.welab.wallet.application.interfaces.facade.CreditApplicationServiceFacade;
import com.welab.wallet.application.interfaces.facade.WalletCreditTransformServiceFacade;
import com.welab.wallet.application.interfaces.vo.WalletCreditTransformPage;
import com.welab.wallet.application.interfaces.vo.WalletCreditTransformPageVO;
import com.welab.wallet.installment.dubbo.WalletLoanDubbboService;
import com.welab.wallet.installment.vo.WalletLoanVO;
import com.welab.wallet.repayment.dubbo.RepaymentDubboService;
import com.welab.wallet.user.dto.LiaisonDTO;
import com.welab.wallet.user.service.UserInfoServiceFacade;
import com.welab.wdfgateway.interfaces.dto.CreditAuthQueryDTO;
import com.welab.wdfgateway.interfaces.facade.WeDefendServiceFacade;
import com.wolaidai.metaspace.dto.AreaDTO;
import com.wolaidai.metaspace.service.AreaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/27 15:04
 */
@Service
@Slf4j
public class UserInfoServiceImpl implements UserInfoService {

    @Resource
    private UserService userService;
    @Resource
    private LoansApplicationService loansApplicationService;
    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;
    @Resource
    private UserServiceFacade userServiceFacade;
    @Resource
    private QuotaService quotaService;
    @Resource
    private NewMemberServiceFacade memberServiceFacade;
    @Resource
    private ProfileService profileService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private NotesServiceFacade notesServiceFacade;
    @Resource
    private WalletLoanDubbboService walletLoanDubbboService;
    @Resource
    private CreditApplicationServiceFacade walletCreditApplicationService;
    @Resource
    private BillDubboService billDubboService;
    @Resource
    private WalletCreditTransformServiceFacade walletCreditTransformServiceFacade;
    @Resource
    private AreaService areaService;
    @Resource
    private AdminService adminService;
    @Resource
    private EducationService educationService;
    @Resource
    private UserInfoServiceFacade userInfoServiceFacade;
    @Resource
    private WeDefendServiceFacade weDefendServiceFacade;
    @Resource
    private WalletUserDubboService walletUserDubboService;
    @Resource
    private BankCardDubboService bankCardDubboService;
    @Resource
    private LoanApplicationService loanApplicationService;
    @Resource
    private BankCardServiceFacade bankCardServiceFacade;
    
    @Resource
    private IUserCenterService userCenterService;

    @Resource
    private BlackListService interviewBlackListService;

    @Resource
    private RepaymentDubboService iRepaymentDubboService;
    
    @Resource
    private LoanDubboService loanDubboService;

    @Value("${usercenter.http.url}")
    private String userCenterHost;
    @Value("${elite.user.token}")
    private String token;
    @Value("${usercenter.operate.id}")
    private String operateId;
    @Value("${mobile.belongs.query.url}")
    private String mobileBelongQueryUrl;
    @Value("${pangu.verify.url}")
    private String mobileVerifyUrl;
    
    @Value("${lender.http.pre}")
    private String lenderPreUrl;


    private static final String APP_NAME = "welab-crm-interview";


    @Override
    public PersonalDetailsVoExpand queryUserInfo(UserDetailQueryDTO userDetailQueryDTO) {
        User user = new User();
        PersonalDetailsVoExpand personalDetailsVoExpand = new PersonalDetailsVoExpand();

        if (Objects.nonNull(userDetailQueryDTO)) {
            log.info("queryUserInfo 客户个人信息查询入参,uuid:{}", userDetailQueryDTO.getUuid());
            //获取用户状态
            Boolean block = getUserState(userDetailQueryDTO.getMobile());
            personalDetailsVoExpand.setBlock(block);

            try {
                user = queryUserByCondition(userDetailQueryDTO);
            }catch (Exception e){
                log.warn("queryUserInfo查询数据异常", e);
                return null;
            }

            //用户信息存在
//            log.info("客户个人信息[{}]", JSONObject.toJSONString(user));
            if (Objects.nonNull(user)) {
                // 屏蔽注销客户
                if (Boolean.TRUE.equals(user.getBlocked())){
                    return null;
                }
                PersonalBasicVo basicVo = loansApplicationService.getUserQuota(user.getUuid());
                PersonalDetailsVo personalDetailsVo = loansApplicationService
                        .selectPersionBackgroundInformation(user.getUuid());

                personalDetailsVoExpand.setPersonalDetailsVo(personalDetailsVo);
                personalDetailsVoExpand
                        .setLastSignInAt(user.getLastSignInAt() == null ? new Date() : user.getLastSignInAt());
                personalDetailsVoExpand.setOrigin(user.getOrigin());
                personalDetailsVoExpand.setAgent(user.getAgent());
                personalDetailsVoExpand.setBlock(user.getBlocked());
                personalDetailsVoExpand.setAvailableCredit(basicVo.getAvailableCredit());
                personalDetailsVoExpand.setCreditLine(basicVo.getCreditLine());
                personalDetailsVoExpand.setCreditState(QuotaStateEnum.getDesc(basicVo.getState()));
                personalDetailsVoExpand.setUuid(String.valueOf(user.getUuid()));
                personalDetailsVoExpand.setMobile(user.getMobile());
                personalDetailsVoExpand.setUserId(user.getId());
                personalDetailsVoExpand.setRegisterAt(user.getCreatedAt());
                personalDetailsVoExpand.setUpdateAt(user.getUpdatedAt());
                if (null != personalDetailsVo && null != personalDetailsVo.getProfile()) {
                    if (personalDetailsVo.getProfile().getCnid() != null && !personalDetailsVo.getProfile()
                            .getCnid()
                            .isEmpty()) {
                        String gender = "未知";
                        Integer result = IDCardUtils.getGenderFromCnid(personalDetailsVo.getProfile().getCnid());
                        if (result == 1) {
                            gender = "男";
                        } else if (result == 2) {
                            gender = "女";
                        }
                        personalDetailsVoExpand.setGender(gender);
                        personalDetailsVoExpand.getPersonalDetailsVo().getProfile().setCnid(
                                StringUtil.hideCnid(personalDetailsVo.getProfile().getCnid()));
                    }
                }
                try {
                    //查询闪电贷可用额度
                    GetAvalQuotaReq quotaReq = new GetAvalQuotaReq();
                    quotaReq.setUserId(user.getUuid());
                    quotaReq.setProductCode("H5-SDD");
                    Response<GetAvalQuotaResp> response = quotaService.getAvalQuota(quotaReq);
                    if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
                        GetAvalQuotaResp resp = response.getResult();
                        personalDetailsVoExpand.setSddAvailableCredit(resp.getAvlCreditLine());
                        personalDetailsVoExpand.setSddCreditLine(resp.getCreditLine());
                        //额度状态
                        personalDetailsVoExpand.setSddStatus(QuotaStateEnum.getDesc(resp.getState()));
                        personalDetailsVoExpand.setSddStatusCode(resp.getState());
                    }
                } catch (RuntimeException e) {
                    log.error("查询闪电贷可用额度报错,error:{}", e.getMessage());
                }

                try {

                    List<VipTypeVO> vipTypeVOList = queryNormalVipData(user.getId());
                    if (CollectionUtils.isNotEmpty(vipTypeVOList)){
                        personalDetailsVoExpand.setIsVip("VIP");
                        personalDetailsVoExpand.setVipList(vipTypeVOList);
                    }
                } catch (RuntimeException e) {
                    log.warn("用户uuid查询会员账户信息,error:{}", e.getMessage());
                }

                try {
                    // 是否逾期
                    personalDetailsVoExpand.setIsOverdue(isOverDue(user.getId()));
                    // 钱包是否逾期
                    personalDetailsVoExpand.setWalletIsOverdue(walletIsOverdue(user.getId()));
                } catch (Exception e) {
                    log.error("getLoanVOList 查询是否逾期异常", e);
                }
                
                // 查询成功贷款笔数
                setSucLoanCnt(personalDetailsVoExpand);
            } else {
                if (block == null) {
                    log.warn("queryUserInfo,该用户不存在");
                    return null;
                } else if (Boolean.TRUE.equals(block)) {
                    return null;
                }
            }

        }
        return personalDetailsVoExpand;

    }

    @Override
    public UserInfoForChat queryUserInfoSimple(UserDetailQueryDTO userDetailQueryDTO) {
        User user = queryUserByCondition(userDetailQueryDTO);
        if (Objects.nonNull(user)) {
            return queryUserInfoSimpleVersion(user.getUuid());
        }
        
        return null;
    }


    /**
     * 查询普通会员数据
     * 
     * @param userId 用户ID
     * @return 返回一个List，包含用户的VIP类型信息
     */
    private List<VipTypeVO> queryNormalVipData(Integer userId) {
        // 创建会员订单查询对象，并设置查询参数
        MemberOrderQueryDTO queryDTO = new MemberOrderQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setMember(true);
        queryDTO.setCategory(MemberCategoryEnum.VIP.name());
        queryDTO.setAppName(APP_NAME); // 使用常量设置应用名称
    
        // 调用服务获取会员订单列表
        List<VipMemberResponseDTO> orders = memberServiceFacade.getAllMemberOrders(queryDTO);
        // 对查询结果进行空值检查
        if (orders == null || orders.isEmpty()) {
            return Collections.emptyList();
        }
    
        // 初始化VIP列表
        List<VipTypeVO> vipList = new ArrayList<>();
    
        // 创建卡类型映射表
        Map<String, String> cardTypeMap = new HashMap<>();
        cardTypeMap.put(VipCardTypeEnum.WL12.getValue(), "year");
        cardTypeMap.put(VipCardTypeEnum.WE12.getValue(), "year");
        cardTypeMap.put(VipCardTypeEnum.WE06.getValue(), "half-year");
        cardTypeMap.put(VipCardTypeEnum.WE01.getValue(), "month");
        cardTypeMap.put(VipCardTypeEnum.WE02.getValue(), "month");
    
        // 遍历会员订单，转换并添加到VIP列表中
        for (VipMemberResponseDTO vipOrder : orders) {
            VipTypeVO vipTypeVO = new VipTypeVO();
            // 根据卡类型获取对应的类型字符串，如果找不到则默认为空字符串
            vipTypeVO.setCardType(cardTypeMap.getOrDefault(vipOrder.getCardType(), ""));
            vipTypeVO.setCardCreatedAt(DateUtil.dateToString(vipOrder.getCardCreatedAt()));
            vipTypeVO.setCardExpiryAt(DateUtil.dateToString(vipOrder.getCardExpiryAt()));
    
            // 将转换后的VIP类型信息添加到列表中
            vipList.add(vipTypeVO);
        }
    
        // 返回VIP类型信息列表
        return vipList;
    }


    private void setSucLoanCnt(PersonalDetailsVoExpand personalDetailsVoExpand) {
        try {
            // 查询已放款和已结清的贷款数量
            List<LoanApplicationDTO> loanList = loanApplicationServiceFacade.getLoanApplicationsByUserUuidAndStates(Long.valueOf(personalDetailsVoExpand.getUuid()), Arrays.asList("disbursed", "closed"));
            personalDetailsVoExpand.setSucLoanCount(loanList.size());
        } catch (Exception e){
            log.warn("setSucLoanCnt 异常,userId:" + personalDetailsVoExpand.getUserId(), e);
        }
    }

    private boolean isOverDue(Integer userId) {
        List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(userId);
        for (LoanVO loanVO : loanVOList) {
            if (loanVO.getOverdueDay() > 0) {
                return true;
            }
        }
        return false;
    }

    private boolean walletIsOverdue(Integer userId) {
        Response<Boolean> overdue = iRepaymentDubboService.isOverdue(userId);
        if (Objects.nonNull(overdue) && Objects.nonNull(overdue.getResult())) {
            return overdue.getResult();
        }
        return false;
    }

    private User queryUserByCondition(UserDetailQueryDTO userDetailQueryDTO) {
        User user = new User();
        //用户uuid查询
        if (null != userDetailQueryDTO.getUuid()) {
            user = userService.getUserByUuid(userDetailQueryDTO.getUuid());
        }//userId查询
        else if (userDetailQueryDTO.getUserId() != null) {
            UserInfo userInfo = userService.getUserInfo(userDetailQueryDTO.getUserId());
            if (null != userInfo) {
                user = userService.getUserByUuid(userInfo.getUuid());
            } else {
                throw new CrmInterviewException("该用户Id不存在");
            }
        }
        //身份证查询
        else if (StringUtils.isNotEmpty(userDetailQueryDTO.getCnid())) {
            UserInfo userInfo = userService.selectUserByCnid(userDetailQueryDTO.getCnid());
            if (null != userInfo) {
                user = userService.getUserByUuid(userInfo.getUuid());
            } else {
                throw new CrmInterviewException("该身份证不存在");
            }
        }//贷款号查询
        else if (StringUtils.isNotEmpty(userDetailQueryDTO.getApplicationId())) {
            LoanInfoVo loanInfoVo = loansApplicationService
                    .getLoansApplicationByMobileOrNameOrApplicationId(null, null,
                            userDetailQueryDTO.getApplicationId());
            if (null != loanInfoVo && Objects.nonNull(loanInfoVo.getLoanInfo())) {
                user = userService.getUserByUuid(Long.parseLong(loanInfoVo.getLoanInfo().get(0).getUuid()));
            } else {
                throw new CrmInterviewException("贷款号不存在");
            }
        }//订单号
        else if (StringUtils.isNotEmpty(userDetailQueryDTO.getOrderNumber())) {
            EcOrderDTO ecOrderDTO = null;
            EcLoanOrdersDTO ecLoanOrdersDTO = null;
            //如果是国美订单号
            if (userDetailQueryDTO.getOrderNumber().startsWith("GM")) {
                //查询国美易卡
                ecOrderDTO = loanApplicationServiceFacade
                        .getOrder("guomeiyika", userDetailQueryDTO.getOrderNumber());
                if (Objects.isNull(ecOrderDTO)) {
                    //国美易卡循环贷
                    ecOrderDTO = loanApplicationServiceFacade
                            .getOrder("gmyk_credit", userDetailQueryDTO.getOrderNumber());
                }
                if (Objects.isNull(ecOrderDTO)) {
                    throw new CrmInterviewException("该订单号不存在");
                }
                UserDTO userDTO = userServiceFacade.getUserById(ecOrderDTO.getUserId());
                if (Objects.isNull(userDTO)) {
                    throw new CrmInterviewException("该用户不存在");
                }
                user = userService.getUserByUuid(userDTO.getUuid());
            } else if (userDetailQueryDTO.getOrderNumber().startsWith("WLSKCREDIT")) {
                // 还呗额度订单号
                ecOrderDTO = loanApplicationServiceFacade
                        .getOrder("huanbei", userDetailQueryDTO.getOrderNumber());
                if (Objects.isNull(ecOrderDTO)) {
                    throw new CrmInterviewException("该订单号不存在");
                }
                UserDTO userDTO = userServiceFacade.getUserById(ecOrderDTO.getUserId());
                if (Objects.isNull(userDTO)) {
                    throw new CrmInterviewException("该用户不存在");
                }
                user = userService.getUserByUuid(userDTO.getUuid());
            } else if (userDetailQueryDTO.getOrderNumber().startsWith("WLSKLEND")) {
                // 还呗提现订单号
                ecLoanOrdersDTO = loanApplicationServiceFacade
                        .getPartnerLoanApplicationsDTOByLoanOrderIdAndSouce("",
                                userDetailQueryDTO.getOrderNumber());
                if (Objects.isNull(ecLoanOrdersDTO)) {
                    throw new CrmInterviewException("该订单号不存在");
                }
                UserDTO userDTO = userServiceFacade.getUserById(ecLoanOrdersDTO.getUserId());
                if (Objects.isNull(userDTO)) {
                    throw new CrmInterviewException("该用户不存在");
                }
                user = userService.getUserByUuid(userDTO.getUuid());
            }
        } else if (StringUtils.isNotBlank(userDetailQueryDTO.getAccountNo())) {
            // 银行卡号查询
            Long uuid = queryUserInfoByAcountNo(userDetailQueryDTO.getAccountNo());
            user = userService.getUserByUuid(uuid);
        } else if (StringUtils.isNotBlank(userDetailQueryDTO.getChannelOrderNo())){
            String applicationId = queryApplicationIdByExternalOrderNo(userDetailQueryDTO.getChannelOrderNo());
            if (StringUtils.isBlank(applicationId)){
                return null;
            }
            userDetailQueryDTO.setApplicationId(applicationId);
            return queryUserByCondition(userDetailQueryDTO);

        } else if (StringUtils.isNotBlank(userDetailQueryDTO.getExternalOrderNo())){
            Response<LoanVO> res = loanDubboService.findLoanByExternalOrder(userDetailQueryDTO.getExternalOrderNo());
            if (!Response.isSuccess(res) || res.getResult() == null) {
                log.warn("根据外部订单号查询贷款信息失败, externalOrderNo: {}", userDetailQueryDTO.getExternalOrderNo());
                return null;
            }
            userDetailQueryDTO.setUserId(Math.toIntExact(res.getResult().getUserId()));
            return queryUserByCondition(userDetailQueryDTO);
        }
        else {
            //手机号查询
            user = userService.selectUnblockedUserByMobile(userDetailQueryDTO.getMobile());
        }

        return user;
    }

    @Override
    public String queryApplicationIdByExternalOrderNo(String channelOrderNo) {
        JSONObject params = new JSONObject();
        params.put("externalOrderNo", channelOrderNo);

        try {
            String res = com.welab.common.utils.http.HttpClientUtil.postJson(
                    lenderPreUrl + "/withdrawal-service/v1/loan/getWithdrawalRecordByExternalUserId",
                    JSON.toJSONString(params)
            );

            log.info("queryApplicationIdByExternalOrderNo,channelOrderNo:{},res:{}", channelOrderNo, res);
            Response response = JSON.parseObject(res, Response.class);
            if (Response.isSuccess(response)) {
                Object result = response.getResult();
                if (result != null) {
                    JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(result));
                    if (jsonArray != null && !jsonArray.isEmpty()) {
                        return jsonArray.getJSONObject(0).getString("applicationId");
                    }
                }
            } else {
                log.warn("根据外部订单号查询贷款号失败: {}", JSON.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("HTTP请求失败: {}", e.getMessage(), e);
        }

        return "";
    }



    @Override
    public Map<String, String> updateUserInfo(String adminMobile, UserInfoVo userInfoVo, Integer userId) {
        Map<String, String> result = new HashMap<>();
        if (Objects.nonNull(userId) && StringUtils.isNotBlank(adminMobile)) {
            log.info("updateUserInfo params:{},adminMobile:{},userId:{}", JSON.toJSONString(userInfoVo), adminMobile,
                    userId);
//            List<Admin> admins = adminService.queryAdminByMobile(Arrays.asList(adminMobile));
//            if (CollectionUtils.isEmpty(admins)) {
//                throw new FastRuntimeException("该用户没有Admin权限");
//            }
            int flag = userInfoService.updateUserInfo(userId, userInfoVo, Integer.valueOf(operateId));
            log.info("update userinfo flag:{}", flag);
            result.put("code", Integer.toString(flag));
            if (flag == 0) {
                log.info("更新用户数据成功");
                result.put("msg", "更新用户数据成功");
            } else if (flag == 1) {
                result.put("msg", "用户不存在");
            } else if (flag == 2) {
                result.put("msg", "该手机号已注册,有贷款记录");
            } else if (flag == 4) {
                result.put("msg", "用户身份证不匹配或身份证为空");
            } else if (flag == 5) {
                result.put("msg", "用户姓名不匹配或姓名为空");
            } else if (flag == 6){
                result.put("msg","用户基本信息不存在，无法修改");
            }
        }
        return result;
    }

    /**
     * 更新用户信息 根据用户uuid更新用户信息（姓名、电话号码、身份证、身份）
     *
     * @param uId
     * @param userInfoVo
     */
    @Override
    public int updateUserInfo(Integer uId, UserInfoVo userInfoVo, Integer operatorId) {
        UserInfo userInfo = userService.getUserInfo(uId);
        User user = userService.getUnblockedUserByUuid(userInfo.getUuid());
        if (user == null) {
            return StateCode.NOUSER;
        }
        Integer userId = user.getId();              //用户ID
        String oldMobile = user.getMobile();        //原电话号码
        Profile profile = profileService.getProfileByUserId(userId);
        if(Objects.isNull(profile)){
            return StateCode.PROFILE_NOT_EXISTS;
        }
        Map<String, String> map = new HashMap<>();
        /**
         * 当手机号A换成B后，手机号所有信息换成了B，换完后根据B手机号可以查询之前历史数据！使用A查询不到任何数据！！手机A被注销，但不影响手机号A再次重新申请新客户账号！
         * 如果是用户更新的是身份证跟名字，不会注销用户！
         */
        if (StringUtils.isNotEmpty(userInfoVo.getMobile()) && !userInfoVo.getMobile().equals(oldMobile)
                && userInfoVo.getMobile().indexOf("*") < 0) {
            String content = String
                    .format("账户修改手机号码：旧手机号码为为[%s],新手机号码为[%s].", user.getMobile(), userInfoVo.getMobile());
            map.put("mobile", content);
            //验证新手机号是否已被注册，验证新手机号和身份证是否相匹配，验证新手机号是否有贷款
            int result = disableUserByMobile(profile, userInfoVo.getMobile());
            if (result != StateCode.OK) {
                return result;
            }
            user.setMobile(userInfoVo.getMobile());
        }
        if (userInfoVo.getRole() != null && !user.getRoleType().equals(userInfoVo.getRole())) {
            user.setRoleType(userInfoVo.getRole());
        }
        if (StringUtils.isNotEmpty(userInfoVo.getName())) {
            String content = String.format("账户更改, 姓名之前为[%s],修改之后为[%s].", profile.getName(), userInfoVo.getName());
            map.put("name", content);
            profile.setName(userInfoVo.getName());
        }
        if (StringUtils.isNotEmpty(userInfoVo.getCnid()) && userInfoVo.getCnid().indexOf("*") < 0) {
            String content = String.format("身份证修改之前为[%s],修改之后为[%s].", profile.getCnid(), userInfoVo.getCnid());
            map.put("cnid", content);
            profile.setCnid(userInfoVo.getCnid());
        }
        user.setUpdatedAt(new Date());
        userService.updateUserByUuid(user);
        profileService.updateProfile(profile);
        //更新redis缓存
        userService.updateUser(4, oldMobile, userId);
        insertOperationNote(map, userId, operatorId);
        return StateCode.OK;
    }

    @Override
    public JSONObject queryBlockUserInfo(String mobile) {
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isBlank(mobile)) {
            throw new CrmInterviewException("手机号不能为空");
        }
        User user = userService.selectUnblockedUserByMobile(mobile);
        if (Objects.isNull(user)) {
            throw new CrmInterviewException("用户不存在");
        }
        Response<Boolean> response = walletLoanDubbboService.hasUnpaidLoan(user.getId().longValue());
        if (Objects.nonNull(response) && response.getResult()) {
            throw new CrmInterviewException("用户名下有剩余未还款订单，暂时无法注销!");
        }

        try {
            BaseProfile baseProfile = userService.getBaseProfile(mobile);
            jsonObject = (JSONObject) JSON.toJSON(new ResponseVo<>(baseProfile));
        } catch (Exception e) {
            throw new CrmInterviewException("请求注销用户信息异常", e);
        }

        return jsonObject;
    }

    @Override
    public String blockUser(String userId) {
        String msg = "";
        if (StringUtils.isNotBlank(userId)) {
            log.info("blockUser，userId:{}", userId);
            try {

                Map<String, Object> resultMap =
                    userService.kefuBlockUser(Integer.parseInt(userId), Integer.parseInt(operateId));
                log.info("blockUser, userId:{}, res:{}", userId, JSON.toJSONString(resultMap));
                msg = resultMap.get("message").toString();
            } catch (Exception e) {
                throw new CrmInterviewException("注销用户异常", e);
            }
        }

        return msg;
    }

    @Override
    public WalletUserDTO queryWalletUser(UserDetailQueryDTO dto) {
        WalletUserDTO walletUserDTO = null;
        User user = null;
        String mobile = null;
        //根据uuid查询用户信息
        if (Objects.nonNull(dto.getUuid())) {
            user = userService.getUserByUuid(dto.getUuid());
            if (Objects.nonNull(user)) {
                mobile = user.getMobile();
            }
        } else if (null != dto.getUserId()) {
            UserInfo userInfo = userService.getUserInfo(dto.getUserId());
            if (Objects.nonNull(userInfo)) {
                user = userService.getUserByUuid(userInfo.getUuid());
                if (Objects.nonNull(user)) {
                    mobile = user.getMobile();
                }
            }
        }

        //根据手机号查询
        if (StringUtils.isNotEmpty(dto.getMobile())) {
            //手机号查询
            user = userService.selectUnblockedUserByMobile(dto.getMobile());
            mobile = dto.getMobile();
        }
        //根据额度申请号
        if (StringUtils.isNotEmpty(dto.getApplicationId())) {
            if (dto.getApplicationId().indexOf("CA") == 0) {
                //手机号查询
                CreditApplicationDTO credit = walletCreditApplicationService
                        .getApplicationsByApplicationId(dto.getApplicationId());
                if (Objects.nonNull(credit)) {
                    user = userService.getUserByUuid(credit.getUuid());
                    if (Objects.nonNull(user)) {
                        mobile = user.getMobile();
                    }
                }
                //根据交叉营销贷款号的查询用户信息
                WalletCreditTransformBatchQueryDTO query = new WalletCreditTransformBatchQueryDTO();
                query.setPage(1);
                query.setPageSize(1);
                query.setApplicationId(dto.getApplicationId());
                WalletCreditTransformPageVO pageVO = walletCreditTransformServiceFacade
                        .getWalletCreditTransformPage(query);
                if (Objects.nonNull(pageVO)) {
                    List<WalletCreditTransformPage> list = pageVO.getCreditTransforms();
                    if (CollectionUtils.isNotEmpty(list)) {
                        user = userService.getUserByUuid(list.get(0).getUuid());
                        if (Objects.nonNull(user)) {
                            mobile = user.getMobile();
                        }
                    }
                }
            } else if (dto.getApplicationId().indexOf("WL") == 0) {
                try {
                    Response<WalletLoanVO> response = walletLoanDubbboService
                            .getLoanDueByApplicationId(dto.getApplicationId());
                    if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
                        UserInfo userInfo = userService
                                .getUserInfo(response.getResult().getUserId());
                        if (Objects.nonNull(userInfo)) {
                            user = userService.getUserByUuid(userInfo.getUuid());
                            mobile = userInfo.getMobile();
                        }
                    }
                } catch (RuntimeException e) {
                    log.warn("根据贷款号查询钱包分期账号接口异常,error:{}", e.getMessage());
                }
            }
        }

        if (StringUtils.isNotBlank(dto.getAccountNo())){
            Long uuid = queryUserInfoByAcountNo(dto.getAccountNo());
            user = userService.getUserByUuid(uuid);
            if (Objects.nonNull(user)) {
                mobile = user.getMobile();
            }
        }
        if (user == null) {
            return walletUserDTO;
        }
        //用户信息查询
        walletUserDTO = buildUserInfo(user, mobile, dto.getType());
        return walletUserDTO;
    }


    @Override
    public List<UserInfoDTO> mobileQuery(UserDetailQueryDTO dto) {

        List<Long> uuidList = new ArrayList<>();
        List<UserInfoDTO> resultList = new ArrayList<>();
        if(StringUtils.isNotEmpty(dto.getMobile())){
            uuidList = userService.selectUuidByMobile(dto.getMobile());
        } //身份证查询
        else if(StringUtils.isNotEmpty(dto.getCnid())){
            List<UserProfileInfo> profiles = userService.selectUserProfilesByCnid(dto.getCnid());
            uuidList = profiles.stream().map(UserProfileInfo::getUuid).collect(Collectors.toList());
        } else if (StringUtils.isNotBlank(dto.getName())) {
            uuidList = userService.getUuidList(dto.getName(), null);
        }
        //用户未注册
        if (uuidList == null || uuidList.isEmpty()) {
            return null;
        }

        UserInfoDTO userInfoDTO = null;
        for (Long uuid : uuidList) {
            User userInfo = userService.getUserByUuid(uuid);
//            log.info("返回的用户信息:{}", JSONUtil.toJson(userInfo, SerializerFeature.WriteNullNumberAsZero));
            if (Objects.nonNull(userInfo)) {
                userInfoDTO = new UserInfoDTO();
                BeanUtils.copyProperties(userInfo, userInfoDTO);
                Profile profile = profileService.getProfileByUserId(userInfo.getId());
                if (profile != null) {
                    userInfoDTO.setName(profile.getName());
                    userInfoDTO.setCnid(profile.getCnid());
                }
                userInfoDTO.setUuid(String.valueOf(uuid));
                resultList.add(userInfoDTO);
            }
        }
        return resultList;
    }

    /**
     * 根据银行卡号，查询UUID
     * @param accountNo
     * @return
     */
    private Long queryUserInfoByAcountNo(String accountNo) {
        Long userId = bankCardServiceFacade.getUserIdByAccountNo(accountNo);
        if (Objects.nonNull(userId)){
            UserInfo userInfo = userService.getUserInfo(userId.intValue());
            return userInfo.getUuid();
        } else {
            throw new FastRuntimeException("根据银行卡号查询userID失败");
        }
    }

    private WalletUserDTO buildUserInfo(User user, String mobile, String type) {
        WalletUserDTO walletUserDTO = new WalletUserDTO();
        walletUserDTO.setBlock(getUserState(mobile));
        walletUserDTO.setMobile(mobile);
        walletUserDTO.setIsOverdue(isOverDue(user.getId()));
        walletUserDTO.setWalletIsOverdue(walletIsOverdue(user.getId()));
        //用户不为空
        if (Objects.nonNull(user)) {
            walletUserDTO.setAgent(user.getAgent());
            walletUserDTO.setUserId(user.getId());
            walletUserDTO.setUuid(Long.toString(user.getUuid()));
            walletUserDTO.setOrigin(user.getOrigin());
            walletUserDTO.setRefereeId(user.getRefereeId()); // 字段可能不对
            //注册时间
            if (null != user.getCreatedAt()) {
                walletUserDTO.setRegisterAt(DateUtil.dateToString(user.getCreatedAt()));
            }

            /**
             * 获取profile信息
             */
            ProfileVo profileVo = getProfile(user.getUuid());
            if (Objects.nonNull(profileVo)) {
                walletUserDTO.setAge(IdCardUtil.getAge(profileVo.getCnid()));
                walletUserDTO.setGender(IdCardUtil.getGender(profileVo.getCnid()));
                walletUserDTO.setCnid(StringUtil.hideCnid(profileVo.getCnid()));
                walletUserDTO.setName(profileVo.getName());
                walletUserDTO.setCurrentAddr(profileVo.getCurrentAddr());
            }

            /**
             * 获取学历信息
             */
            walletUserDTO.setDegree(getDegree(user.getId()));

            /**
             * 获取联系人列表
             */
            List<LiaisonVo> liaisons = getLiaisons(user.getUuid());
            walletUserDTO.setLiaisons(liaisons);

            /**
             * 查询用户授权信息
             */
            Map<String, Boolean> authMap = getAuth(mobile);
            if (null != authMap) {
                walletUserDTO.setIsAlipayAuth(authMap.get("alipayAuth"));
                walletUserDTO.setIsCarrierAuth(authMap.get("mobileAuth"));
            }

            /**
             * 银行开户信息
             */
            WalletUserVo walletUserVo = getWalletUser(user.getUuid());
            if (Objects.nonNull(walletUserVo)) {
                //绑卡时间
                walletUserDTO.setBankDate(DateUtil.dateToString(walletUserVo.getCreatedAt()));
                //二类户账户
                walletUserDTO.setElecCardNo(StringUtil.hideBankCard(walletUserVo.getElecCardNo()));
            }

            /**
             * 查询用户的可用额度
             */
            GetAvalQuotaResp quotaResp = getAvalQuota(user.getUuid(), type);
            if (Objects.nonNull(quotaResp)) {
                //授信额度
                walletUserDTO.setCreditLine(quotaResp.getCreditLine());
                //可用余额
                walletUserDTO.setAvlCreditLine(quotaResp.getAvlCreditLine());
                //额度状态
                walletUserDTO.setState(QuotaStateEnum.getDesc(quotaResp.getState()));
                walletUserDTO.setCreditStateCode(quotaResp.getState());
            }

            /**
             * 查询用户是否消费
             */
            try {
                Response<Boolean> response = billDubboService.queryUserTrade(user.getId().longValue());
                if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
                    walletUserDTO.setIsConsume(response.getResult());
                }
            } catch (RuntimeException e) {
                log.warn("查询用户是否消费异常,error:{}", e.getMessage());
            }
        }
        return walletUserDTO;
    }

    /**
     * 查询用户的可用额度
     * @param uuid
     * @return
     */
    private GetAvalQuotaResp getAvalQuota(Long uuid,String type){
        GetAvalQuotaResp quotaResp = null;
        try{
            GetAvalQuotaReq req = new GetAvalQuotaReq();
            req.setUserId(uuid);
            req.setProductCode(type);
            //获取用户额度数据
            Response<GetAvalQuotaResp> response = quotaService.getAvalQuota(req);
            log.info("钱包用户额度fan,response:{}",BeanUtil.toString(response));
            if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
                quotaResp = response.getResult();
            }
        }catch (RuntimeException e){
            log.error("查询用户可用额度,getAvalQuota error:{}",e.getMessage());
        }
        return quotaResp;
    }

    private ProfileVo getProfile(Long uuid) {
        ProfileVo profileVo = null;
        try {
            Profile profile = profileService.getProfileByUuid(uuid);
//            log.info("个人信息ucProfile:{}", BeanUtil.toString(profile));
            if (profile != null) {
                profileVo = new ProfileVo();
                profileVo.setName(profile.getName());
                profileVo.setCnid(profile.getCnid());
                //精确查找申请人信息
                ApplicantInfo applicantInfo = userService.selectApplicantInfoByUuid(uuid,
                        EnumAddressType.PROFILE_ADDRESSES_TYPE.getValue(),
                        EnumAddressType.RESIDENT_LOCATION_TYPE.getValue());
                if (applicantInfo != null) {
                    try {
                        if (null != applicantInfo.getDistrict()) {
                            AreaDTO area = areaService.getAreaByDistrictCode(applicantInfo.getDistrict().toString());
                            StringJoiner residentSj = new StringJoiner(",");
                            if (area != null) {
                                String residentStr = residentSj.add(area.getProvinceName()).add(area.getCityName())
                                        .add(area.getDistrictName())
                                        .add(applicantInfo.getStreet()).toString().replaceAll("\\s", "");
                                profileVo.setCurrentAddr(residentStr);    //当前住址
                            }
                        }
                    } catch (Exception e) {
                        log.error("钱包用户查询居住地址异常,error:{}", e.getMessage());
                    }
                }
            }
        } catch (RuntimeException e) {
            log.error("调查profileService接口异常，error:{}", e.getMessage());
        }
        return profileVo;
    }

    /**
     * 获取学历
     *
     * @param userId
     * @return
     */
    private String getDegree(Integer userId) {
        String degree = "";
        try {
            List<Education> ucEducation = educationService.getEducationByUserId(userId);
            if (CollectionUtils.isNotEmpty(ucEducation)) {
                degree = degreeApellation(ucEducation.get(0).getDegree_id());
            }
        } catch (RuntimeException e) {
            log.error("钱包获取教育信息接口异常,error:{}", e.getMessage());
        }
        return degree;
    }

    private String degreeApellation(Integer degreeId) {
        if (degreeId == null) {
            return null;
        }
        return DegreeEnum.getNameById(degreeId);
    }

    /**
     * 获取联系人列表
     *
     * @param uuid
     * @return
     */
    private List<LiaisonVo> getLiaisons(Long uuid) {
        List<LiaisonVo> result = new ArrayList<>();
        try {
            List<LiaisonDTO> liaisonList = userInfoServiceFacade.getLiaisons(uuid);
            if (CollectionUtils.isEmpty(liaisonList)) {
                return Collections.emptyList();
            }

            // 查询全部催收黑名单
            List<BlackListVO> contactBlackList = interviewBlackListService.getContactBlackList();
            Map<String, List<BlackListVO>> mobileMap =
                    contactBlackList.stream().collect(Collectors.groupingBy(BlackListVO::getMobile));
            for (LiaisonDTO ucLianison : liaisonList) {
                String mobile = ucLianison.getMobile();
                boolean isBlack = mobileMap.containsKey(mobile);
                if (isBlack) {
                    mobileMap.get(mobile).sort(Comparator.comparing(BlackListVO::getValidEndTime).reversed());
                }
                result.add(new LiaisonVo(ucLianison.getName(), transRelation(ucLianison.getRelationship()),
                        StringUtil.hideMobile(mobile), isBlack,
                        isBlack ? mobileMap.get(mobile).get(0).getStartTime() : null,
                        isBlack ? mobileMap.get(mobile).get(0).getEndTime() : null));
            }
        } catch (Exception e) {
            log.error("查询钱包用户联系人异常,error:{}", e.getMessage());
        }
        return result;
    }

    private String transRelation(String relation) {
        return RelationshipEnum.getDescByValue(relation);
    }

    /**
     * 查询用户信用授权信息
     */
    public Map<String, Boolean> getAuth(String mobile) {
        Map<String, Object> resultMap = new HashMap<>();
        //查询用户授权信息接口
        try {
            CreditAuthQueryDTO queryDTO = new CreditAuthQueryDTO();
            queryDTO.setAccount(mobile);
            String result = weDefendServiceFacade.getAuthInfo(queryDTO);
            log.info("Wedefend银行卡授权返回结果:" + result);
            //解析返回数据
            resultMap = (Map<String, Object>) JSON.parse(result);
            if (null != resultMap && resultMap.size() > 0) {
                return buildUserAuthVo(resultMap);
            }
        } catch (Exception e) {
            log.error("查询weDefend授权信息,error:{}", e.getMessage());
        }
        return null;
    }

    //处理授权返回信息
    private Map<String, Boolean> buildUserAuthVo(Map<String, Object> resultMap) {
        Map<String, Boolean> result = new HashMap<>();
        result.put("mobileAuth", Boolean.FALSE);
        result.put("alipayAuth", Boolean.FALSE);
        Map<String, Map<String, Object>> authMap = (Map<String, Map<String, Object>>) resultMap.get("auth");
        if (null != authMap && authMap.size() > 0) {
            //运营商授权
            Map<String, Object> mobileAuth = authMap.get("mobileAuth");
            if (null != mobileAuth && mobileAuth.size() > 0) {
                Boolean isAuth = (Boolean) mobileAuth.get("isAuth");
                String authStatus = (String) mobileAuth.get("authStatus");
                if ("0".equals(authStatus) && isAuth) {
                    result.put("mobileAuth", Boolean.TRUE);
                }
            }

            //支付宝授权信息
            Map<String, Object> alipayAuth = authMap.get("alipayAuth");
            if (null != alipayAuth && alipayAuth.size() > 0) {
                Boolean isAuth = (Boolean) alipayAuth.get("isAuth");
                String authStatus = (String) alipayAuth.get("authStatus");
                if ("0".equals(authStatus) && isAuth) {
                    result.put("alipayAuth", Boolean.TRUE);
                }
            }
        }
        return result;
    }

    /**
     * 上海银行开户信息
     * @param uuid
     * @return
     */
    private WalletUserVo getWalletUser(Long uuid) {
        WalletUserVo walletUserVo = null;
        try {
            //根据uuid查询用户信息
            User user = userService.getUserByUuid(uuid);
            if (Objects.nonNull(user)) {
                //查询银行开户信息
                Response<WalletUserVo> response = walletUserDubboService.getWalletUserByUserId(user.getId().longValue());
                log.info("查询开户信息,info = {}", BeanUtil.toString(response));
                if (Objects.nonNull(response) && Objects.nonNull(response.getResult())) {
                    walletUserVo = response.getResult();
                }
            }
        } catch (RuntimeException e) {
            log.error("查询开户异常,error:{}", e.getMessage());
        }
        return walletUserVo;
    }

    private void insertOperationNote(Map<String, String> map, Integer userId, Integer operatorId) {

        if (map.containsKey("name") && map.containsKey("cnid")) {
            StringBuffer sb = new StringBuffer();
            sb.append("[Robot]:");
            sb.append(map.get("name"));
            sb.append(map.get("cnid"));
            createNote(userId, operatorId, sb.toString(), BusiConstant.KEFU_MODIFY_NAME);
            map.remove("name");
            map.remove("cnid");
        }
        for (String val : map.values()) {
            StringBuffer sb2 = new StringBuffer();
            sb2.append("[Robot]:");
            sb2.append(val);
            createNote(userId, operatorId, sb2.toString(), BusiConstant.KEFU_MODIFY_MOBILE);
        }
    }

    private void createNote(Integer userId, Integer operatorId, String content, String noteType) {
        NotesDTO note = new NotesDTO();
        note.setAdminId(operatorId);             //操作员Id
        note.setUpdatedAt(new Date());           //修改时间
        note.setCreatedAt(new Date());           //创建时间
        note.setSubjectId(userId);
        note.setSubjectType(OwnerTypeEnum.USER.getKey());
        note.setContent(content);
        note.setNoteType(noteType);
        log.info("--------------------------------------------------------------" + note.toString());
        notesServiceFacade.saveNotes(note);
    }

    private int disableUserByMobile(Profile oldProfile, String newMobile) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("1:" + df.format(new Date()));
        /**
         * 换新的手机号需要先判断新的手机号是否有注册，已经贷款记录，有的话需要注销
         */
        User newUser = userService.selectUnblockedUserByMobile(newMobile);
        log.info("2:" + df.format(new Date()) + newUser);
        if (Objects.nonNull(newUser)) {
            Profile newProfile = profileService.getProfileByUserId(newUser.getId());
            boolean hasApplication = getLoanApplicationsCountByMobile(newUser.getUuid());
            log.info("disableUserByMobile hasApplication:{}", hasApplication);
            if (hasApplication) {
                return StateCode.ACTIVELOANS;
            }

            if (Objects.nonNull(oldProfile) && Objects.nonNull(newProfile)) {
                if (!Optional.ofNullable(newProfile.getName()).orElse("")
                        .equalsIgnoreCase(Optional.ofNullable(oldProfile.getName()).orElse(""))) {
                    return StateCode.NAME_NOT_MATCHED;
                }

                if (!Optional.ofNullable(newProfile.getCnid()).orElse("")
                        .equalsIgnoreCase(Optional.ofNullable(oldProfile.getCnid()).orElse(""))) {
                    return StateCode.CNID_NOT_MATCHED;
                }
            }
            log.info("4:" + df.format(new Date()));
            /**
             * 新用户已经注册也需要重新注销
             */
            userService.updateBlockById(newUser.getId(), true);
            //更新redis缓存
            userService.updateUser(4, newMobile, newUser.getId());
        }
        return StateCode.OK;
    }

    private boolean getLoanApplicationsCountByMobile(Long uuid) {
        boolean hasApplication = false;
        List<LoanApplicationDTO> list = loanApplicationServiceFacade.getLoanApplicationsByUserUuid(uuid);
        if (list != null && list.size() > 0) {
            hasApplication = true;
        }
        return hasApplication;
    }

    private Boolean getUserState(String mobile) {
        List<Long> uuidList = userService.selectUuidByMobile(mobile);
        //用户未注册
        if (uuidList == null || uuidList.isEmpty()) {
            return null;
        }
        User userInfo = null;
        for (Long uuid : uuidList) {
            userInfo = userService.getUserByUuid(uuid);
            if (!Objects.isNull(userInfo) && !Boolean.TRUE.equals(userInfo.getBlocked())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public PersonalDetailsVoExpand queryUserInfoPlus(UserDetailQueryDTO userDetailQueryDTO) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        PersonalDetailsVoExpand userInfo = queryUserInfo(userDetailQueryDTO);
        log.info("queryAllUserInfoByMobile 查询客服基本信息 use time:{}s",
                stopwatch.elapsed(TimeUnit.SECONDS));
        stopwatch.reset();
        if (Objects.nonNull(userDetailQueryDTO) && StringUtils.isNotBlank(userDetailQueryDTO.getMobile())) {
            String response = "";
            try {
                stopwatch.start();
                response = HttpClientUtil
                        .httpGet(mobileBelongQueryUrl + userDetailQueryDTO.getMobile(), new HashMap<>());
            } catch (Exception e) {
                log.error("调用盘古接口获取手机运营商异常,url:" + mobileBelongQueryUrl + userDetailQueryDTO.getMobile(), e);
            }
            log.info("queryAllUserInfoByMobile 查询手机运营商 use time:{}s",
                    stopwatch.stop().elapsed(TimeUnit.SECONDS));
            if (StringUtils.isNotBlank(response)){
                JSONObject resObject = JSON.parseObject(response);
                if (Objects.nonNull(resObject) && resObject.containsKey("ret") && 0 == resObject.getInteger("ret")) {
                    JSONObject address = resObject.getJSONObject("address");
                    if (Objects.nonNull(address)) {
                        userInfo.setOperator(address.getString("operator"));
                    }
                }
            }

        }
        return userInfo;
    }

    @Override
    public List<UserInfoForChat> queryAllUserInfoByMobile(UserDetailQueryDTO reqDto) {
        if (Objects.nonNull(reqDto) && StringUtils.isNotBlank(reqDto.getMobile())) {
            List<UserInfoForChat> userInfoList = new ArrayList<>();
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<Long> uuidList = userService.selectUuidByMobile(reqDto.getMobile());
            if (CollectionUtils.isEmpty(uuidList)){
                throw new CrmInterviewException("用户不存在");
            }
            String response = "";
            try {
                response = HttpClientUtil
                        .httpGet(mobileBelongQueryUrl + reqDto.getMobile(), new HashMap<>());
            } catch (Exception e) {
                log.error("调用盘古接口获取手机运营商异常,url:" + mobileBelongQueryUrl + reqDto.getMobile(), e);
            }
            String operator = "";
            try {
                if (StringUtils.isNotBlank(response)) {
                    JSONObject resObject = JSON.parseObject(response);
                    if (Objects.nonNull(resObject) && resObject.containsKey("ret") && 0 == resObject.getInteger("ret")) {
                        JSONObject address = resObject.getJSONObject("address");
                        if (Objects.nonNull(address)) {
                            operator = address.getString("operator");
                        }
                    }
                }
            } catch (Exception e){
                log.warn("调用盘古接口获取手机运营商，数据转化异常", e);
            }
            for (Long uuid : uuidList) {
                UserInfoForChat userInfoForChat = queryUserInfoSimpleVersion(uuid);
                userInfoForChat.setMobile(reqDto.getMobile());
                userInfoForChat.setOperator(operator);
                userInfoList.add(userInfoForChat);
            }
            // 按照注册时间倒序
            userInfoList.sort(Comparator.comparing(UserInfoForChat::getRegisterAt).reversed());

            log.info("queryAllUserInfoByMobile use time {}ms", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            return userInfoList;
        } else {
            log.warn("queryAllUserInfoByMobile，手机号不能为空");
            throw new CrmInterviewException("手机号不能为空");
        }
    }


    @Override
    public UserInfoForChat queryUserInfoSimpleVersion(Long uuid) {
        if (Objects.isNull(uuid)){
            throw new FastRuntimeException("用户不存在");
        }
        User user = userService.getUserByUuid(uuid);
        if (Objects.isNull(user)){
            throw new FastRuntimeException("用户不存在");
        }
        UserInfoForChat userInfoForChat = new UserInfoForChat();
        Integer userId = user.getId();
        userInfoForChat.setUserId(userId);
        userInfoForChat.setUuid(uuid.toString());
        Profile profile = profileService.getProfileByUserId(userId);
        if (Objects.nonNull(profile)){
            userInfoForChat.setCnid(profile.getCnid());
            userInfoForChat.setName(profile.getName());
            String gender = "未知";
            if (StringUtils.isNotBlank(profile.getCnid())){
                Integer result = IDCardUtils.getGenderFromCnid(profile.getCnid());
                if (result == 1) {
                    gender = "男";
                } else if (result == 2) {
                    gender = "女";
                }
            }
            userInfoForChat.setGender(gender);
        }
        userInfoForChat.setBlock(user.getBlocked());
        userInfoForChat.setRegisterAt(DateUtil.dateToString(user.getCreatedAt()));

        try {
            //用户uuid查询会员账户信息
            VipMemberResponseDTO dto = memberServiceFacade.getActiveVipMember(user.getUuid());
            if (Objects.nonNull(dto)) {
                if (Boolean.TRUE.equals(dto.getMember())) {
                    userInfoForChat.setIsVip("VIP");
                }
                if (null != dto.getCardCreatedAt()) {
                    userInfoForChat.setCardCreatedAt(DateUtil.dateToString(dto.getCardCreatedAt()));
                }
                if (null != dto.getCardExpiryAt()) {
                    userInfoForChat.setCardExpiryAt(DateUtil.dateToString(dto.getCardExpiryAt()));
                }
            }
        } catch (RuntimeException e) {
            log.warn("用户uuid查询会员账户信息,error:{}", e.getMessage());
        }

        // 查询逾期信息
        UserOverdueObject userOverdueObject = userCenterService.queryUserOverdueState(userId);
        userInfoForChat.setIsOverdue(userOverdueObject.getIsOverdue());
        userInfoForChat.setOverdueDays(userOverdueObject.getOverdueDays());

        return userInfoForChat;
    }

    @Override
    public String verifyUserInfo(Integer userId, UserInfoVo userInfoVo) {
        // 修改手机号之前先做三要素认证
        Profile profile = profileService.getProfileByBorroweId(userId);
        if (profile == null) {
            throw new CrmInterviewException("不存在的用户userId: " + userId);
        }
        //手机号码三要素验证入参
        JSONObject json = new JSONObject();
        json.put("account", userInfoVo.getMobile());
        json.put("name", profile.getName());
        json.put("cnid", profile.getCnid());
        json.put("apiOperator", "kefu");

        try {
            String response = HttpClientUtil.doPost(mobileVerifyUrl, JSONObject.toJSONString(json));
            log.info("mobileVerify userId: {}, response:{}", userId, response);
            JSONObject resultObject = JSONObject.parseObject(response);
            JSONObject mobileVerify = resultObject.getJSONObject("MobileVerify");
            if (mobileVerify != null) {
                JSONArray rsl = mobileVerify.getJSONArray("RSL");
                if (rsl != null && !rsl.isEmpty()) {
                    JSONObject rs = rsl.getJSONObject(0).getJSONObject("RS");
                    if ("0".equals(rs.get("code"))) {
                        return null;
                    } else {
                        return rs.getString("desc");
                    }
                } else {
                    throw new CrmInterviewException("盘古接口三要素认证返回错误的结构体");
                }
            } else {
                String errorMsg = resultObject.getString("msg");
                if (StringUtils.isBlank(errorMsg)) {
                    errorMsg = "三要素认证失败, 原因未知";
                }
                return errorMsg;
            }
        } catch (Exception e) {
            log.error("调用盘古接口做三要素认证发生错误: {}", e.getMessage(), e);
            throw new CrmInterviewException("调用盘古接口做三要素认证发生错误");
        }
    }

    @Override
    public UserBlackInfoVO getUserIdByUuid(String uuid) {
        UserInfo userInfo = userService.getUserInfoByUuid(Long.valueOf(uuid));
        if (Objects.isNull(userInfo)){
            throw new FastRuntimeException("getUserIdByUuid 用户不存在,uuid" + uuid);
        }
        Profile profile = profileService.getProfileByUserId(userInfo.getUserId());
        if (Objects.isNull(profile)){
            throw new FastRuntimeException("getUserIdByUuid 用户个人信息不存在,uuid" + uuid);
        }

        UserBlackInfoVO vo = new UserBlackInfoVO();
        vo.setUuid(uuid);
        vo.setUserId(userInfo.getUserId());
        vo.setCnid(profile.getCnid());
        vo.setName(profile.getName());
        vo.setMobile(userInfo.getMobile());

        return vo;
    }

    @Override
    public String getUuidByApplicationId(String applicationId) {
        if (StringUtils.isNotBlank(applicationId)) {
            LoanApplicationDTO loanApplication = loanApplicationServiceFacade.getLoanApplicationByApplicationId(applicationId);
            if (Objects.nonNull(loanApplication)) {
                UserDTO user = userServiceFacade.getUserByUserId(loanApplication.getBorrowerId());
                return Objects.nonNull(user) ? user.getUuid().toString() : null;
            }
        }
        return null;
    }

    @Override
    public String getUuidByUserId(Integer userId) {
        if (Objects.nonNull(userId)) {
            UserDTO user = userServiceFacade.getUserByUserId(userId);
            return Objects.nonNull(user) ? user.getUuid().toString() : null;
        }
        return null;
    }

    private UserInfoForChat convertVoExpandToChat(PersonalDetailsVoExpand personalDetailsVoExpand) {
        UserInfoForChat userInfoForChat = new UserInfoForChat();
        BeanUtils.copyProperties(personalDetailsVoExpand,userInfoForChat);
        userInfoForChat.setRegisterAt(DateUtil.dateToString(personalDetailsVoExpand.getRegisterAt()));
        if (Objects.nonNull(personalDetailsVoExpand.getPersonalDetailsVo())) {
            ProfileVo profile = personalDetailsVoExpand.getPersonalDetailsVo().getProfile();
            if (Objects.nonNull(profile)) {
                userInfoForChat.setName(profile.getName());
                userInfoForChat.setCnid(profile.getCnid());
            }
        }
        return userInfoForChat;
    }
}
