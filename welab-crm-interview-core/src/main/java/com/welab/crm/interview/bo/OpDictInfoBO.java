package com.welab.crm.interview.bo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class OpDictInfoBO {

	@Resource
	private OpDictInfoMapper opDictInfoMapper;


	/**
	 * 查询字典并转化为 map，category 不能为空，type 可以为空
	 */
	public Map<String, String> queryValidDictMap(String category, String type) {
		if (StringUtils.isBlank(category)) {
			throw new FastRuntimeException("category 不能为空");
		}
		List<OpDictInfo> opDictInfos = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category)
				.eq(StringUtils.isNotBlank(type), OpDictInfo::getType, type)
				.eq(OpDictInfo::getStatus, 1));
		if (CollectionUtils.isEmpty(opDictInfos)) {
			return Collections.emptyMap();
		}

		return opDictInfos.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent));
	}

}
