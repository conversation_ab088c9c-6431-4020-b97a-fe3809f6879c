package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.domain.CsCouponSendLog;
import com.welab.crm.interview.dto.coupon.CouponSendLogDTO;
import com.welab.crm.interview.vo.coupon.CouponSendLogVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 卡券发送记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface CsCouponSendLogMapper extends BaseMapper<CsCouponSendLog> {

    Page<CouponSendLogVO> selectLogVOPage(Page<CsCouponSendLog> page, @Param("filter") CouponSendLogDTO dto);

    List<CouponSendLogVO> selectLogVOList(@Param("filter") CouponSendLogDTO dto);

}
