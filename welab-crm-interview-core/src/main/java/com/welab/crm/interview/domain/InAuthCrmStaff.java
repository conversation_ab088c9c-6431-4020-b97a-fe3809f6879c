package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客服人员记录表（同步认证平台）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_auth_crm_staff")
public class InAuthCrmStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 姓名
     */
    private String staffName;

    /**
     * 手机
     */
    private String staffMobile;

    /**
     * 所属组code
     */
    private String groupCode;

    /**
     * 所属组名
     */
    private String groupName;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 员工状态，休息，在线
     */
    private String staffStatus;

    /**
     * 是否管理员
     */
    private Boolean isManager;

    /**
     * 记录状态，1：有效，0：无效
     */
    private Boolean isStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 邮箱地址
     */
    private String email;


}
