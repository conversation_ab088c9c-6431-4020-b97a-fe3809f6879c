package com.welab.crm.interview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.service.FaSuPushService;
import com.welab.collection.interview.vo.legal.FaSuPushVO;
import com.welab.crm.interview.domain.OpDictInfo;
import com.welab.crm.interview.mapper.OpDictInfoMapper;
import com.welab.crm.interview.service.FaSuService;
import com.welab.crm.interview.vo.fasu.FaSuVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("faSuService")
public class FaSuServiceImpl implements FaSuService {

    @Resource
    private FaSuPushService faSuPushService;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Override
    public Map<String, FaSuVO> listFaSuData(List<String> applicationIdList) {
        List<FaSuPushVO> voList = faSuPushService.listFaSuData(applicationIdList);
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyMap();
        } else {
            List<OpDictInfo> company = opDictInfoMapper.selectList(Wrappers.<OpDictInfo>lambdaQuery()
                    .eq(OpDictInfo::getCategory, "faSuCompanyTel"));
            Map<String, String> telMap = company.stream().collect(Collectors
                    .toMap(OpDictInfo::getType, OpDictInfo::getContent));
            return voList.stream().collect(Collectors.toMap(FaSuPushVO::getApplicationId, item -> {
                FaSuVO suVo = new FaSuVO();
                suVo.setCompanyName(item.getCompanyName());
                if (StringUtils.isEmpty(item.getEndTime())) {
                    suVo.setTimeRange(item.getBeginTime() + "~" + "永久");
                } else {
                    suVo.setTimeRange(item.getBeginTime() + "~" + item.getEndTime());
                }
                suVo.setCompanyTel(telMap.get(item.getCompany()));
                return suVo;
            }));
        }
    }
}
