package com.welab.crm.interview.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by Lyman on 2016/11/18.
 */
public class StringUtil {

    /**
     * 手机号替换规则
     */
    private static final String MOBILE_REGULAR = "(?<=\\d{3})\\d(?=\\d{4})";

    /**
     * 身份证号替换规则
     */
    private static final String CNID_REGULAR = "(?<=\\d{6})\\d(?=\\d{4})";

    /**
     * 银行卡替换规则
     */
    private static final String BANKCARD_REGULAR = "(?<=\\d{4})\\d(?=\\d{4})";

    /**
     * 手机号匹配规则
     */
    public static final Pattern MOBILE_PATTERN = Pattern.compile("^1(3|4|5|6|7|8|9)\\d{9}$");

    /**
     * 身份证号匹配规则，粗略的校验
     */
    public static final Pattern CNID_PATTERN = Pattern
        .compile("^(\\d{6})(19|20)(\\d{2})(1[0-2]|0[1-9])(0[1-9]|[1-2][0-9]|3[0-1])(\\d{3})(\\d|X|x)?$");

    public static String hideMobile(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(MOBILE_REGULAR, "*");
        }
        return "";
    }

    public static String hideCnid(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(CNID_REGULAR, "*");
        }
        return "";
    }

    public static String hideBankCard(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(BANKCARD_REGULAR, "*");
        }
        return "";
    }

    /**
     * 判断传入的字符串是否是手机号
     */
    public static boolean isMobile(String mobile) {
        Matcher m = MOBILE_PATTERN.matcher(mobile);
        return m.matches();
    }

    /**
     * 18位身份证校验,粗略的校验
     *
     * @param idCard
     * @return
     */
    public static boolean is18ByteIdCard(String idCard) {
        Matcher matcher = CNID_PATTERN.matcher(idCard);
        return matcher.matches();
    }

    /**
     * 校验银行卡卡号
     *
     * @param bankCard
     * @return
     */
    public static boolean checkBankCard(String bankCard) {
        if (bankCard.length() < 15 || bankCard.length() > 19) {
            return false;
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        if (bit == 'N') {
            return false;
        }
        return bankCard.charAt(bankCard.length() - 1) == bit;
    }

    /**
     * 从不含校验位的银行卡卡号采用 Luhm 校验算法获得校验位
     *
     * @param nonCheckCodeBankCard
     * @return
     */
    public static char getBankCardCheckCode(String nonCheckCodeBankCard) {
        if (nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
            || !nonCheckCodeBankCard.matches("\\d+")) {
            // 如果传的不是数据返回N
            return 'N';
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for (int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - '0';
            if (j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? '0' : (char) ((10 - luhmSum % 10) + '0');
    }


    /**
     * 用 paramMap 填充 str 中的 ${变量名} 占位符
     * @param str
     * @param paramMap
     * @return
     */
    public static String replacePlaceholders(String str, Map<String, String> paramMap) {
        // 定义正则表达式
        Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}");

        // 使用 Matcher 进行匹配和替换
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String placeholder = matcher.group(); // 获取占位符，如 ${变量名}
            String key = matcher.group(1); // 获取变量名
            String value = paramMap.get(key); // 获取变量值
            if (value == null) { // 如果变量值为 null，则置为空字符串
                value = "";
            }
            matcher.appendReplacement(sb, value); // 使用变量值替换占位符
        }

        System.out.println("sb = " + sb);
        matcher.appendTail(sb);

        return sb.toString();
    }

    public static void main(String[] args) {
        String s = "${name} 你好，请点击下方链接http:123.com?token=${token},xxxxxxxxxxxxxxx";

        Map<String, String> map = new HashMap<>();
        map.put("name","李廷伟");
        map.put("token","12331");
        String s1 = replacePlaceholders(s, map);
        System.out.println("s1 = " + s1);
    }

}
