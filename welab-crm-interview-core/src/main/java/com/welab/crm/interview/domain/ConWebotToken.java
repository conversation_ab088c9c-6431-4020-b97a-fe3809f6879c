package com.welab.crm.interview.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 获取创研人脸验证链接的保存记录
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_webot_token")
public class ConWebotToken implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 验证类型
     */
    private String validateType;

    /**
     * 创研返回的有效验证url
     */
    private String token;

    /**
     * 验证结果码: 0-成功, 1-失败
     */
    private String resultCode;

    /**
     * 验证结果文本
     */
    private String resultMessage;

    /**
     * 创建人(发送员工staffId)
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间(返回url的时间)
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
