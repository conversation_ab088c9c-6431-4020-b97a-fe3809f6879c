package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.DataCustomer;
import com.welab.crm.interview.vo.MobileBakVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface DataCustomerMapper extends BaseMapper<DataCustomer> {


    /**
     * 根据手机号查询客户备用号码
     * @param mobile
     * @return
     */
    List<MobileBakVO> queryMobileBakByMobile(@Param("mobile") String mobile);

    /**
     * 查询用户信息
     * @param customerId
     * @param uuid
     * @return
     */
    DataCustomer queryCustomer(@Param("customerId")String customerId, @Param("uuid")String uuid);
}
