package com.welab.crm.interview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.interview.domain.OpHttpLogRecord;
import com.welab.crm.interview.vo.monitor.OpHttpLogRecordByPhoneSummary;
import com.welab.crm.interview.vo.monitor.StaffMonitorReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 接口请求记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface OpHttpLogRecordMapper extends BaseMapper<OpHttpLogRecord> {

    /**
     * 查询指定时间内的日志监控数据
     * 
     * @param startTime
     * @param endTime
     * @return
     */
    List<StaffMonitorReportVO> queryLogData(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询指定时间内的日志监控数据
     * @param startTime
     * @param endTime
     * @return
     */
    List<OpHttpLogRecordByPhoneSummary> queryLogDataByPhoneSummary(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
