# 阿里云智能对话分析质检配置示例
aliyun:
  quality:
    check:
      # 是否启用质检功能
      enabled: true
      
      # 阿里云访问凭证
      access-key-id: YOUR_ACCESS_KEY_ID
      access-key-secret: YOUR_ACCESS_KEY_SECRET
      
      # 服务配置
      endpoint: qualitycheck.cn-hangzhou.aliyuncs.com
      region-id: cn-hangzhou
      protocol: HTTPS
      
      # 业务空间ID
      base-me-agent-id: 123456
      
      # 回调地址（需要公网可访问）
      callback-url: https://your-domain.com/api/v1/phone/quality/callback
      
      # 超时配置
      connect-timeout: 30000
      read-timeout: 60000
      
      # 录音配置
      auto-split: 1  # 是否自动分轨（单轨录音）
      service-channel: 0  # 客服轨道编号（双轨录音）
      client-channel: 1   # 客户轨道编号（双轨录音）
      sample-rate: 8      # 录音采样率（8：8000hz，16：16000hz）
      
      # 客服关键词（用于单轨录音角色分离）
      service-channel-keywords:
        - 客服
        - 您好
        - 请问
        - 为您
        - 帮您
        - 可以
        - 需要
      
      # 重试配置
      max-retry-times: 3
      retry-interval: 5000
      
      # 查询配置
      query-interval: 30000
      max-query-times: 120

# 线程池配置（可选，使用默认值）
quality:
  inspection:
    thread:
      pool:
        core-size: 10
        max-size: 20
        queue-capacity: 500
        keep-alive-seconds: 300
