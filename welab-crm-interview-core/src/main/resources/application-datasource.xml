<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:lang="http://www.springframework.org/schema/lang" xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:oxm="http://www.springframework.org/schema/oxm" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
		http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/oxm http://www.springframework.org/schema/oxm/spring-oxm.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<!-- slowSqlMillis:定义慢SQL的标准 logSlowSql:慢SQL记录 mergeSql:SQL合并配置 -->
	<bean id="statFilter" class="com.alibaba.druid.filter.stat.StatFilter">
		<property name="logSlowSql" value="true" />
		<property name="mergeSql" value="true" />
		<property name="slowSqlMillis" value="1000" />
	</bean>

	<!-- logViolation:怀疑攻击的SQL记录到日志 throwException:怀疑攻击的SQL抛异常 -->
	<bean id="wallFilter" class="com.alibaba.druid.wall.WallFilter">
		<property name="dbType" value="mysql" />
		<property name="logViolation" value="true" />
		<property name="throwException" value="false" />
	</bean>

	<!-- statementExecutableSqlLogEnable:记录所有可执行的SQL -->
	<bean id="logFilter" class="com.alibaba.druid.filter.logging.Slf4jLogFilter">
		<property name="statementExecutableSqlLogEnable" value="true" />
	</bean>

	<!-- 数据源 -->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />

		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="60000" />

		<!-- 配置间隔多久才进行一次检测,检测需要关闭的空闲连接,单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />

		<!-- 配置一个连接在池中最小生存的时间,单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />

		<property name="validationQuery" value="SELECT 'x'" />
		<!-- 在获取连接后，确定是否要进行连接空间时间的检查 -->
		<property name="testWhileIdle" value="true" />
		<!-- 获取连接检测 -->
		<property name="testOnBorrow" value="false" />
		<!-- 归还连接检测 -->
		<property name="testOnReturn" value="true" />
		<!-- 定期将监控数据输出到日志中 -->
		<property name="timeBetweenLogStatsMillis" value="300000" />

		<!-- 当程序存在缺陷时,申请的连接忘记关闭,这时候,就存在连接泄漏了;Druid提供了RemoveAbandanded相关配置,用来关闭长时间不使用的连接; -->
		<property name="removeAbandoned" value="true" /> <!-- 打开removeAbandoned功能 -->
		<property name="removeAbandonedTimeout" value="1800" /> <!-- 1800秒,也就是30分钟 -->
		<property name="logAbandoned" value="true" /> <!-- 关闭abanded连接时输出错误日志 -->

		<!-- 打开PSCache,并且指定每个连接上PSCache的大小;如果用Oracle,则把poolPreparedStatements配置为true,mysql可以配置为false;分库分表较多的数据库,建议配置为false; -->
		<property name="poolPreparedStatements" value="false" />
		<property name="maxPoolPreparedStatementPerConnectionSize" value="50" />

		<!-- 配置最大 -->
		<property name="maxActive" value="200" />
		<!-- 配置初始化大小、最小 -->
		<property name="initialSize" value="3" />
		<property name="minIdle" value="3" />

		<!-- 配置监控统计拦截的filters -->
		<property name="filters" value="${druid.filters}" />
		<property name="connectionProperties" value="${druid.connectionProperties}" />

	</bean>

	<!-- 异步Dao线程池配置 -->
<!-- 	<bean id="asyncDaoConfigurer" class="com.welab.xdao.async.AsyncDaoConfigurer">
		<property name="corePoolSize" value="${async.dao.core_pool_size}" />
		<property name="maxPoolSize" value="${async.dao.max_pool_size}" />
		<property name="queueSize" value="${async.dao.queue_size}" />
	</bean>

	<bean id="dao" class="com.welab.xdao.Dao">
		<property name="dataSource" ref="dataSource" />
		<property name="basePackage" value="${table.package}" />
		<property name="asyncDaoConfigurer" ref="asyncDaoConfigurer" />
		日志是否打印SQL
		<property name="showSql" value="false" />
	</bean> -->

	<!-- 弱XA事务 Best Efforts 1PC -->
	<!-- 完全支持非跨库事务. 例如:不分表分库及仅分表或分库但是路由的结果在单库中 -->
	<!-- 完全支持因逻辑异常导致的跨库事务. 例如:同一事务中,跨两个库更新;更新完毕后,抛出空指针,则两个库的内容都能回滚 -->
	<!-- 不支持因网络、硬件异常导致的跨库事务. 例如:同一事务中,跨两个库更新,更新完毕后;第一个提交成功,第二个宕机则第一个无法回滚 -->
	<!-- <bean id="chainedTransactionManager" class="com.welab.dds.transaction.ChainedTransactionManager" /> -->
	
	<!-- 定义事务通知 -->
<!-- 	<tx:advice id="txAdvice" transaction-manager="chainedTransactionManager">
		定义方法的过滤规则
		<tx:attributes>
			所有方法都使用事务
			<tx:method name="create*" propagation="REQUIRED" />
			<tx:method name="save*" propagation="REQUIRED" />
			<tx:method name="add*" propagation="REQUIRED" />
			<tx:method name="update*" propagation="REQUIRED" />
			<tx:method name="delete*" propagation="REQUIRED" />
			定义所有find开头的方法都是只读的
			<tx:method name="query*" read-only="true" />
			<tx:method name="find*" read-only="true" />
			<tx:method name="get*" read-only="true" />
		</tx:attributes>
	</tx:advice> -->

	<!-- 定义AOP配置 -->
	<!-- <aop:config>
		定义一个切入点
		<aop:pointcut id="services" expression="${aop.services}" />
		对切入点和事务的通知，进行适配
		<aop:advisor advice-ref="txAdvice" pointcut-ref="services" />
	</aop:config> -->

</beans>