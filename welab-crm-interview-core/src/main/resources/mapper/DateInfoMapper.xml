<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.DateInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.DateInfo">
        <id column="id" property="id" />
        <result column="year" property="year" />
        <result column="month" property="month" />
        <result column="date" property="date" />
        <result column="year_week" property="yearWeek" />
        <result column="year_day" property="yearDay" />
        <result column="lunar_year" property="lunarYear" />
        <result column="lunar_month" property="lunarMonth" />
        <result column="lunar_date" property="lunarDate" />
        <result column="lunar_yearday" property="lunarYearday" />
        <result column="week" property="week" />
        <result column="weekend" property="weekend" />
        <result column="workday" property="workday" />
        <result column="holiday" property="holiday" />
        <result column="holiday_or" property="holidayOr" />
        <result column="holiday_overtime" property="holidayOvertime" />
        <result column="holiday_today" property="holidayToday" />
        <result column="holiday_legal" property="holidayLegal" />
        <result column="holiday_recess" property="holidayRecess" />
        <result column="gmt_create" property="gmtCreate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, year, month, date, year_week, year_day, lunar_year, lunar_month, lunar_date, lunar_yearday, week, weekend, workday, holiday, holiday_or, holiday_overtime, holiday_today, holiday_legal, holiday_recess, gmt_create
    </sql>

</mapper>
