<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.AiTmkCallbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.AiTmkCallback">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="tmk_task_id" property="tmkTaskId" />
        <result column="channel_name" property="channelName" />
        <result column="phone_no" property="phoneNo" />
        <result column="call_num" property="callNum" />
        <result column="call_time" property="callTime" />
        <result column="answer_time" property="answerTime" />
        <result column="end_time" property="endTime" />
        <result column="bill_sec" property="billSec" />
        <result column="phone_status" property="phoneStatus" />
        <result column="call_result" property="callResult" />
        <result column="willing_col" property="willingCol" />
        <result column="sys_call_status" property="sysCallStatus" />
        <result column="call_back_date" property="callBackDate" />
        <result column="record_url" property="recordUrl" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, tmk_task_id, channel_name, phone_no, call_num, call_time, answer_time, end_time, bill_sec, phone_status, call_result, willing_col, sys_call_status, call_back_date, record_url, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>

</mapper>
