<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.TmkUuidMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.TmkUuid">
        <id column="id" property="id" />
        <result column="tmk_task_id" property="tmkTaskId" />
        <result column="username" property="username" />
        <result column="uuid" property="uuid" />
        <result column="application_id" property="applicationId" />
        <result column="user_id" property="userId" />
        <result column="package_define" property="packageDefine" />
        <result column="avl_credit" property="avlCredit" />
        <result column="credit_status" property="creditStatus" />
        <result column="is_income" property="isIncome" />
        <result column="is_withdrawal" property="isWithdrawal" />
        <result column="cnid" property="cnid" />
        <result column="mobile" property="mobile" />
        <result column="gender" property="gender" />
        <result column="credit_line" property="creditLine" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="package_create_time" property="packageCreateTime" />
        <result column="flag" property="flag" />
        <result column="staff_id" property="staffId" />
        <result column="summary_id" property="summaryId" />
        <result column="assign_date" property="assignDate" />
        <result column="applied_at" property="appliedAt" />
        <result column="confirmed_at" property="confirmedAt" />
        <result column="assign_rule_id" property="assignRuleId" />
        <result column="state" property="state" />
        <result column="type" property="type" />
        <result column="call_num" property="callNum" />
        <result column="ai_push_date" property="aiPushDate" />
        <result column="ai_push_flag" property="aiPushFlag" />
        <result column="ai_push_time" property="aiPushTime" />
        <result column="num_package_id" property="numPackageId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tmk_task_id, username, uuid, application_id, user_id, package_define, avl_credit, credit_status, is_income, is_withdrawal, cnid, mobile, gender, credit_line, gmt_create, gmt_modify, package_create_time, flag, staff_id, summary_id, assign_date, applied_at, confirmed_at, assign_rule_id, state, type, call_num, ai_push_date, ai_push_flag, ai_push_time, num_package_id
    </sql>

    <select id="selectNeedFilterUuid" resultType="com.welab.crm.interview.domain.TmkUuid">
        select tu.*
        from tmk_uuid tu
                 join tmk_uuid_config tuc on tu.call_type = tuc.call_type and tu.num_package_id = tuc.num_package_id and
                                             tuc.call_type = 'man'
        where (tuc.delete_flag = 0 or (tuc.delete_flag = 1 and tu.flag != '0'))
          and tu.user_id = #{userId}
    </select>

</mapper>
