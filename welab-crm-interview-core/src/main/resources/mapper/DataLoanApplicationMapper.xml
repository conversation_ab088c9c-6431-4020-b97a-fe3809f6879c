<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.DataLoanApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.DataLoanApplication">
        <id column="id" property="id" />
        <result column="wo_task_id" property="woTaskId" />
        <result column="application_id" property="applicationId" />
        <result column="product_name" property="productName" />
        <result column="apply_time" property="applyTime" />
        <result column="approval_time" property="approvalTime" />
        <result column="confirm_time" property="confirmTime" />
        <result column="loan_time" property="loanTime" />
        <result column="channel_code" property="channelCode" />
        <result column="partner_code" property="partnerCode" />
        <result column="apply_amount" property="applyAmount" />
        <result column="approval_amount" property="approvalAmount" />
        <result column="apply_tenor" property="applyTenor" />
        <result column="approval_tenor" property="approvalTenor" />
        <result column="status" property="status" />
        <result column="user_level" property="userLevel" />
        <result column="total_rate" property="totalRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wo_task_id, application_id, product_name, apply_time, approval_time, confirm_time, loan_time, channel_code, partner_code, apply_amount, approval_amount, apply_tenor, approval_tenor, status, user_level, total_rate
    </sql>

</mapper>
