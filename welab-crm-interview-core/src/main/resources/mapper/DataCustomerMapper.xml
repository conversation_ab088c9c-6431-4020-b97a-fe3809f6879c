<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.DataCustomerMapper">


    <select id="queryMobileBakByMobile" resultType="com.welab.crm.interview.vo.MobileBakVO">
        SELECT wt.mobile_bak, wt.mobile_baks, wt.gmt_create as mobileUpdateTime from wo_task wt
              join data_customer dc on wt.cust_id = dc.id
        where dc.mobile = #{mobile}
        group by wt.id
    </select>

    <select id="queryCustomer" resultType="com.welab.crm.interview.domain.DataCustomer">
        SELECT * FROM data_customer d WHERE 1=1
        <if test="customerId != null and customerId != ''">
            and d.id = #{customerId}
        </if>
        <if test="uuid != null and uuid != ''">
            and d.uuid = #{uuid}
        </if>
        limit 1
    </select>
</mapper>
