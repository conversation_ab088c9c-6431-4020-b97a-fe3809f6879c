<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.TmkLoanInviteMapper">


    <!-- 1=1 替换 approved_at between CURDATE() and DATE_ADD(CURDATE(), INTERVAL 23 HOUR) -->
    <select id="selectPushData" resultType="com.welab.crm.interview.domain.TmkLoanInvite">
        select
        username,
        gender,
        mobile,
        tmk_task_id
        from tmk_loan_invite
        where state != 'confirmed'
        <if test="products != null">
            and product_name in
            <foreach collection="products" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="channels != null">
            and apply_origin in
            <foreach collection="channels" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="labels != null">
            and (diversion_tag is null or diversion_tag not in
            <foreach collection="labels" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="minAmount != null">
            and amount &gt;= #{minAmount}
        </if>
        <if test="maxAmount != null">
            and amount &lt;= #{maxAmount}
        </if>
        <if test="flag != null and flag != ''">
            and flag = #{flag}
        </if>
        <if test="approvedAtStar != null">
            and approved_at between #{approvedAtStar} and #{approvedAtEnd}
        </if>
        and (ai_push_flag is null or to_days(ai_push_time)!=to_days(now()))
        group by mobile
    </select>

    <update id="updatePushData">
        update tmk_loan_invite set ai_push_flag=1, ai_push_time=now()
        where tmk_task_id in
        <foreach collection="tmkTaskIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

</mapper>
