<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.ContractSendRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.ContractSendRecord">
        <id column="id" property="id" />
        <result column="app_no" property="appNo" />
        <result column="uuid" property="uuid" />
        <result column="customer_name" property="customerName" />
        <result column="status" property="status" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="group_code" property="groupCode" />
        <result column="group_name" property="groupName" />
        <result column="staff_id" property="staffId" />
        <result column="staff_name" property="staffName" />
        <result column="partner_code" property="partnerCode" />
        <result column="partner_name" property="partnerName" />
        <result column="send_type" property="sendType" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="mobile" property="mobile" />
        <result column="customer_id" property="customerId" />
        <result column="message_id" property="messageId" />
        <result column="send_time" property="sendTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_no, uuid, customer_name, status, gmt_create, group_code, group_name, staff_id, staff_name, partner_code, partner_name, send_type, gmt_modify, mobile, customer_id, message_id, send_time
    </sql>

</mapper>
