<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpDictInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpDictInfo">
        <id column="id" property="id" />
        <result column="category" property="category" />
        <result column="type" property="type" />
        <result column="content" property="content" />
        <result column="detail" property="detail" />
        <result column="status" property="status" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category, type, content, detail, status, gmt_create, gmt_modify
    </sql>
    <select id="getAllByCategoryAndType" resultType="com.welab.crm.interview.domain.OpDictInfo">
        select *
        from op_dict_info
        where 1=1
        <if test="category != null and category != ''">
            and category = #{category}
        </if>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
    </select>

</mapper>
