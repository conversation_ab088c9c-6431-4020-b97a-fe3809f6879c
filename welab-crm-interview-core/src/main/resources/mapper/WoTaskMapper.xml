<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.WoTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.WoTask">
        <id column="id" property="id" />
        <result column="distribute_time" property="distributeTime" />
        <result column="submit_time" property="submitTime" />
        <result column="cust_id" property="custId" />
        <result column="cnid" property="cnid" />
        <result column="customer_name" property="customerName" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="mobile" property="mobile" />
        <result column="mobile_bak" property="mobileBak" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="order_one_class" property="orderOneClass" />
        <result column="order_two_class" property="orderTwoClass" />
        <result column="order_three_class" property="orderThreeClass" />
        <result column="order_case" property="orderCase" />
        <result column="urgent_flag" property="urgentFlag" />
        <result column="callback_flag" property="callbackFlag" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="create_group_code" property="createGroupCode" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="modify_staff_id" property="modifyStaffId" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="description" property="description" />
        <result column="opinion" property="opinion" />
        <result column="callback_note" property="callbackNote" />
        <result column="remark" property="remark" />
        <result column="reminder_flag" property="reminderFlag" />
        <result column="suc_loan_count" property="sucLoanCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, distribute_time, submit_time, cust_id, cnid, customer_name, age, gender, mobile, mobile_bak, order_no, type, status, order_one_class, order_two_class, order_three_class, order_case, urgent_flag, callback_flag, create_staff_id, create_group_code, gmt_create, modify_staff_id, gmt_modify, description, opinion, callback_note, remark, reminder_flag, suc_loan_count
    </sql>
    <select id="queryAllByType" resultType="com.welab.crm.interview.domain.WoTask">
        SELECT wt.* from wo_task wt
        left join op_dict_info odi on wt.type = odi.id
        where odi.content in
        <foreach collection="typeList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryNoticeOrder" resultType="com.welab.crm.interview.vo.workorder.WorkOrderNoticeVO">
        SELECT
        odi.content as orderType,
        odi2.content as orderThreeType,
        iacs.staff_name,
        dc.uuid,
        cs.gmt_create as lastContactTime,
        wt.distribute_time,
        wt.gmt_create as orderCreateTime,
        dc.customer_name,
        wt.id,
        wt.response_time_out
        from
        wf_ru_execution wre
        join wf_ru_task wrt on
        wrt.id = (
        select
        wrt2.id
        from
        wf_ru_task wrt2
        where
        wrt2.execution_id = wre.execution_id
        order by
        wrt2.id desc
        limit 1 )
        join wo_task wt on
        wt.order_no = wre.busi_key
        join data_customer dc on
        wt.cust_id = dc.id
        join op_dict_info odi on
        wt.`type` = odi.id
        join op_dict_info odi2 on
        wt.order_three_class = odi2.id
        join in_auth_crm_staff iacs on
        iacs.id = wrt.staff_id
        left join callback_summary cs on
        cs.id = (
        select
        cs2.id
        from
        callback_summary cs2
        where
        cs2.uuid = dc.uuid
        and cs2.staff_id = wrt.staff_id
        order by
        cs2.id desc
        limit 1 )
        where
        wre.status not like '%end'
        <if test="orderTypeList != null and orderTypeList.size() > 0">
            and odi.content in 
            <foreach collection="orderTypeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isFirstContact != null and isFirstContact == 1">
            and (cs.id is null or cs.gmt_create &lt; wrt.gmt_modify)
        </if>
        <if test="notOverStartDays != null">
            and TIMESTAMPDIFF(DAY ,wt.gmt_create,NOW()) &gt;= #{notOverStartDays} 
        </if>
        <if test="notOverEndDays != null">
            and TIMESTAMPDIFF(DAY ,wt.gmt_create,NOW()) &lt;= #{notOverEndDays}
        </if>
        <if test="startTime != null">
            and wt.gmt_create &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and wt.gmt_create &lt;= #{endTime}
        </if>
        <if test="woTypeId != null">
            and wt.type = #{woTypeId}
        </if>
        <if test="woTypeFirId != null">
            and wt.order_one_class = #{woTypeFirId}
        </if>
        <if test="woTypeSecId != null">
            and wt.order_two_class = #{woTypeSecId}
        </if>
        <if test="woTypeThirId != null">
            and wt.order_three_class = #{woTypeThirId}
        </if>
    </select>
    <select id="queryWorkOrderHistoryByCondition" resultType="com.welab.crm.interview.domain.WoTask">
        select wt.id
        from wo_task wt
        join wf_ru_execution wre on wre.busi_key = wt.order_no
        join data_customer dc on wt.cust_id = dc.id
        where 1=1
        <if test="reqDTO.orderType != null and reqDTO.orderType != ''">
            and wt.type = #{reqDTO.orderType}
        </if>
        <if test="reqDTO.orderOneClass != null">
            and wt.order_one_class = #{reqDTO.orderOneClass}
        </if>
        <if test="reqDTO.orderTwoClass != null">
            and wt.order_two_class = #{reqDTO.orderTwoClass}
        </if>
        <if test="reqDTO.orderThreeClass != null">
            and wt.order_three_class = #{reqDTO.orderThreeClass}
        </if>
        <if test="reqDTO.custId != null">
            and wt.cust_id = #{reqDTO.custId}
        </if>
        <if test="reqDTO.uuid != null and reqDTO.uuid != ''">
            and dc.uuid = #{reqDTO.uuid}
        </if>
        <if test="reqDTO.notInStatusList != null and reqDTO.notInStatusList.size() > 0">
            and wre.status not in
            <foreach collection="reqDTO.notInStatusList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
