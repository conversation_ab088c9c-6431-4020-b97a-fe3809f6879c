<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.WoAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.WoAttachment">
        <id column="id" property="id" />
        <result column="wo_task_id" property="woTaskId" />
        <result column="path" property="path" />
        <result column="filename" property="filename" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="staff_id" property="staffId" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wo_task_id, path, filename, gmt_create, gmt_modify, staff_id, status
    </sql>

</mapper>
