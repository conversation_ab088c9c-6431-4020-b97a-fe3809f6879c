<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.TmkUuidTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.TmkUuidTemp">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="uuid" property="uuid" />
        <result column="application_id" property="applicationId" />
        <result column="user_id" property="userId" />
        <result column="package_define" property="packageDefine" />
        <result column="avl_credit" property="avlCredit" />
        <result column="credit_status" property="creditStatus" />
        <result column="is_income" property="isIncome" />
        <result column="is_withdrawal" property="isWithdrawal" />
        <result column="cnid" property="cnid" />
        <result column="mobile" property="mobile" />
        <result column="gender" property="gender" />
        <result column="credit_line" property="creditLine" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="push_flag" property="pushFlag" />
        <result column="rule_id" property="ruleId" />
        <result column="num_package_id" property="numPackageId" />
        <result column="call_type" property="callType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, uuid, application_id, user_id, package_define, avl_credit, credit_status, is_income, is_withdrawal, cnid, mobile, gender, credit_line, gmt_create, gmt_modify, push_flag, rule_id, call_type, num_package_id
    </sql>

</mapper>
