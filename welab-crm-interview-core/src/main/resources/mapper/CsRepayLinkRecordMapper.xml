<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.CsRepayLinkRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.CsRepayLinkRecord">
        <id column="id" property="id" />
        <result column="customer_name" property="customerName" />
        <result column="uuid" property="uuid" />
        <result column="application_id" property="applicationId" />
        <result column="send_mobile" property="sendMobile" />
        <result column="repay_mode" property="repayMode" />
        <result column="repay_amount" property="repayAmount" />
        <result column="send_time" property="sendTime" />
        <result column="send_staff_id" property="sendStaffId" />
        <result column="send_staff_name" property="sendStaffName" />
        <result column="send_staff_group" property="sendStaffGroup" />
        <result column="customer_id" property="customerId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="user_id" property="userId" />
        <result column="payment_channel" property="paymentChannel" />
        <result column="repay_time" property="repayTime" />
        <result column="repay_result" property="repayResult" />
        <result column="consultation_status" property="consultationStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_name, uuid, application_id, send_mobile, repay_mode, repay_amount, send_time, send_staff_id, send_staff_name, send_staff_group, customer_id, gmt_create, gmt_modify, user_id, payment_channel, repay_time, repay_result, consultation_status
    </sql>

</mapper>
