<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.SensitiveWorkorderConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.SensitiveWorkorderConfig">
        <id column="id" property="id" />
        <result column="wo_type_id" property="woTypeId" />
        <result column="wo_type_detail" property="woTypeDetail" />
        <result column="wo_type_fir_id" property="woTypeFirId" />
        <result column="wo_type_fir_detail" property="woTypeFirDetail" />
        <result column="wo_type_sec_id" property="woTypeSecId" />
        <result column="wo_type_sec_detail" property="woTypeSecDetail" />
        <result column="wo_type_thir_id" property="woTypeThirId" />
        <result column="wo_type_thir_detail" property="woTypeThirDetail" />
        <result column="is_status" property="isStatus" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="partner_names" property="partnerNames" />
        <result column="create_staff" property="createStaff" />
        <result column="remark" property="remark" />
        <result column="follow_up_time" property="followUpTime" />
        <result column="warn_type" property="warnType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wo_type_id, wo_type_detail, wo_type_fir_id, wo_type_fir_detail, wo_type_sec_id, wo_type_sec_detail, wo_type_thir_id, wo_type_thir_detail, is_status, gmt_create, gmt_modify, partner_names, create_staff, remark, follow_up_time, warn_type
    </sql>

</mapper>
