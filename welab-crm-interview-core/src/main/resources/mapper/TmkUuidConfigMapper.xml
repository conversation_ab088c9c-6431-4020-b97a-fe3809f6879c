<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.TmkUuidConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.TmkUuidConfig">
        <id column="id" property="id" />
        <result column="project_name" property="projectName" />
        <result column="config_id" property="configId" />
        <result column="num_package_id" property="numPackageId" />
        <result column="num_package_def" property="numPackageDef" />
        <result column="call_instruction" property="callInstruction" />
        <result column="call_type" property="callType" />
        <result column="create_user" property="createUser" />
        <result column="gmt_create" property="gmtCreate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_name, config_id, num_package_id, num_package_def, call_instruction, call_type, create_user, gmt_create
    </sql>

</mapper>
