<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.InAuthCrmStaffMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.InAuthCrmStaff">
        <id column="id" property="id" />
        <result column="login_name" property="loginName" />
        <result column="staff_name" property="staffName" />
        <result column="staff_mobile" property="staffMobile" />
        <result column="group_code" property="groupCode" />
        <result column="group_name" property="groupName" />
        <result column="company" property="company" />
        <result column="staff_status" property="staffStatus" />
        <result column="is_manager" property="isManager" />
        <result column="is_status" property="isStatus" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="email" property="email" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, login_name, staff_name, staff_mobile, group_code, group_name, company, staff_status, is_manager, is_status, gmt_create, gmt_modify, email
    </sql>
    <select id="queryStaffByCno" resultType="com.welab.crm.interview.domain.InAuthCrmStaff">
        SELECT
            iacs.*
        from
            in_phone_login_info ipli
                join in_auth_crm_staff iacs on
                ipli.user_tel = iacs.login_name
        where ipli.id_no = #{cno}
    </select>

</mapper>
