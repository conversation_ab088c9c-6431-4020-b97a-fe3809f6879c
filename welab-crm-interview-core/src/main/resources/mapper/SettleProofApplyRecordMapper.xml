<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.SettleProofApplyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.SettleProofApplyRecord">
        <id column="id" property="id" />
        <result column="apply_time" property="applyTime" />
        <result column="staff_id" property="staffId" />
        <result column="application_id" property="applicationId" />
        <result column="partner_code" property="partnerCode" />
        <result column="state" property="state" />
        <result column="file_path" property="filePath" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, apply_time, staff_id, application_id, partner_code, state, file_path, gmt_create, gmt_modify
    </sql>
    <select id="selectSuccessButNoFileLhRecord"
            resultType="com.welab.crm.interview.domain.SettleProofApplyRecord">
        select *
        from settle_proof_apply_record
        where partner_code = 'lhbank'
        and state = 'success'
        and (file_path is null or file_path = '')
    </select>

</mapper>
