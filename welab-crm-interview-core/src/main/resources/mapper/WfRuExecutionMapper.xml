<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.WfRuExecutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.WfRuExecution">
        <id column="id" property="id" />
        <result column="execution_id" property="executionId" />
        <result column="busi_key" property="busiKey" />
        <result column="process_code" property="processCode" />
        <result column="status" property="status" />
        <result column="staff_id" property="staffId" />
        <result column="group_code" property="groupCode" />
        <result column="complete_flag" property="completeFlag" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, execution_id, busi_key, process_code, status, staff_id, group_code, complete_flag, gmt_create, gmt_modify
    </sql>

</mapper>
