<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.TmkInterceptRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.TmkInterceptRule">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="tmk_type" property="tmkType"/>
        <result column="intercept_type" property="interceptType"/>
        <result column="status" property="status"/>
        <result column="product_code" property="productCode"/>
        <result column="channel_code" property="channelCode"/>
        <result column="valid_days" property="validDays"/>
        <result column="remark" property="remark"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
        <result column="create_staff_id" property="createStaffId"/>
        <result column="modify_staff_id" property="modifyStaffId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, tmk_type, intercept_type, status, product_code, channel_code, valid_days, remark, gmt_create, gmt_modify, create_staff_id, modify_staff_id
    </sql>

</mapper>
