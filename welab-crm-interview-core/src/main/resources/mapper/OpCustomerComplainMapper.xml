<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpCustomerComplainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpCustomerComplain">
        <id column="id" property="id" />
        <result column="contract_no" property="contractNo" />
        <result column="cust_no" property="custNo" />
        <result column="org" property="org" />
        <result column="group_code" property="groupCode" />
        <result column="staff_id" property="staffId" />
        <result column="content" property="content" />
        <result column="complain_level" property="complainLevel" />
        <result column="must_review" property="mustReview" />
        <result column="must_approve" property="mustApprove" />
        <result column="review_result" property="reviewResult" />
        <result column="approve_result" property="approveResult" />
        <result column="approve_status" property="approveStatus" />
        <result column="urge_status" property="urgeStatus" />
        <result column="work_order_no" property="workOrderNo" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="review_time" property="reviewTime" />
        <result column="approve_time" property="approveTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_no, cust_no, org, group_code, staff_id, content, complain_level, must_review, must_approve, review_result, approve_result, approve_status, urge_status, work_order_no, gmt_create, gmt_modify, review_time, approve_time, remark
    </sql>

</mapper>
