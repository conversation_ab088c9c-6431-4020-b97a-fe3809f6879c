<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.CsLoanTransferContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.CsLoanTransferContract">
        <id column="id" property="id" />
        <result column="transfer_id" property="transferId" />
        <result column="contract_no" property="contractNo" />
        <result column="push_state" property="pushState" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="process_status" property="processStatus" />
        <result column="fail_reason" property="failReason" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transfer_id, contract_no, push_state, create_user, lst_upd_user, gmt_create, gmt_modify, process_status, fail_reason
    </sql>

</mapper>
