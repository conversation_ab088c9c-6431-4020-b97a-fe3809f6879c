<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.ConSatisfactionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.ConSatisfaction">
        <id column="id" property="id" />
        <result column="cdr_enterprise_id" property="cdrEnterpriseId" />
        <result column="cdr_number_trunk" property="cdrNumberTrunk" />
        <result column="cdr_hotline" property="cdrHotline" />
        <result column="cdr_main_unique_id" property="cdrMainUniqueId" />
        <result column="cdr_customer_number" property="cdrCustomerNumber" />
        <result column="cdr_call_type" property="cdrCallType" />
        <result column="sv_start_time" property="svStartTime" />
        <result column="sv_end_time" property="svEndTime" />
        <result column="bridged_cno" property="bridgedCno" />
        <result column="cdr_transfer" property="cdrTransfer" />
        <result column="sv_keys" property="svKeys" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cdr_enterprise_id, cdr_number_trunk, cdr_hotline, cdr_main_unique_id, cdr_customer_number, cdr_call_type, sv_start_time, sv_end_time, bridged_cno, cdr_transfer, sv_keys, gmt_create, gmt_modify
    </sql>

    <select id="selectSatisfactionReport" resultType="com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO">
        select t.date, t.cdr_callee_cno as cno, t.staff_id, t.staff_name,t.group_name,
            ifnull(t.call_in_num, 0) as call_in_num,
            ifnull(t2.no_part_num, 0) as no_part_num,
            ifnull(t2.part_num, 0) as part_num,
            ifnull(t2.vs_num, 0) as vs_num,
            ifnull(t2.s_num, 0) as s_num,
            ifnull(t2.common_num, 0) as common_num,
            ifnull(t2.u_s_num, 0) as u_s_num,
            ifnull(t2.u_vs_num, 0) as u_vs_num,
            ifnull(t2.num_1, 0) as num_1,
            ifnull(t2.num_2, 0) as num_2,
            ifnull(t2.num_3, 0) as num_3,
            ifnull(t2.num_4, 0) as num_4,
            ifnull(t.call_in_num - t2.no_part_num - t2.part_num, 0) as no_invite_num,
            ifnull(t2.no_part_num + t2.part_num, 0) as invite_num,
            concat(ifnull(round((t2.no_part_num + t2.part_num)/t.call_in_num * 100, 2), '0.00'), '%') as invite_rate,
            concat(ifnull(round(t2.part_num/(t2.no_part_num + t2.part_num) * 100, 2), '0.00'), '%') as part_rate,
            concat(ifnull(round((t2.s_num+t2.vs_num)/t2.part_num * 100, 2), '0.00'), '%') as s_rate,
            concat(ifnull(round((t2.common_num+t2.u_s_num+t2.u_vs_num)/t2.part_num * 100, 2), '0.00'), '%') as us_rate,
            concat(ifnull(round(t2.num_1/t2.part_num * 100, 2), '0.00'), '%') as num1_rate,
            concat(ifnull(round(t2.num_2/t2.part_num * 100, 2), '0.00'), '%') as num2_rate,
            concat(ifnull(round(t2.num_3/t2.part_num * 100, 2), '0.00'), '%') as num3_rate,
            concat(ifnull(round(t2.num_4/t2.part_num * 100, 2), '0.00'), '%') as num4_rate
        from (
            select
            <if test="cond.period == 'day'">
                date_format(cpci.cdr_start_time, '%Y-%m-%d') as date,
            </if>
            <if test="cond.period == 'range'">
                concat(date_format(#{cond.startTime},'%Y/%m/%d'), ' - ', date_format(#{cond.endTime},'%Y/%m/%d')) as date,
            </if>
             cpci.staff_id, iacs.staff_name,
            iacs.group_name,
                cpci.cdr_callee_cno,count(1) as call_in_num
            from con_phone_call_info cpci
            inner join in_auth_crm_staff iacs on iacs.id = cpci.staff_id
            where cpci.cdr_call_type = '1'
                and cpci.cdr_start_time &gt;= #{cond.startTime}
                and cpci.cdr_start_time &lt;= #{cond.endTime}
                and cpci.cdr_hotline in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="cond.cnos != null and cond.cnos.size() > 0">
                    and cpci.cdr_callee_cno in
                    <foreach collection="cond.cnos" item="item" open="(" close=")" separator="," index="index">
                        #{item}
                    </foreach>
                </if>
            group by date, cpci.cdr_callee_cno
        )t left join(
            select
            <if test="cond.period == 'day'">
                date_format(sv_start_time, '%Y-%m-%d') as date,
            </if>
            <if test="cond.period == 'range'">
                concat(date_format(#{cond.startTime},'%Y/%m/%d'), ' - ', date_format(#{cond.endTime},'%Y/%m/%d')) as date,
            </if>
                bridged_cno, count(1) as total_num,
                count(case when length(sv_keys) &lt; 1 or sv_keys is null then 1 else null end) as no_part_num,
                count(case when length(sv_keys) &gt;= 1 then 1 else null end) as part_num,
                count(case when substr(sv_keys,1) = '1' then 1 else null end) as vs_num,
                count(case when substr(sv_keys,1) = '2' then 1 else null end) as s_num,
                count(case when substr(sv_keys,1) = '3' then 1 else null end) as common_num,
                count(case when substr(sv_keys,1) = '4' then 1 else null end) as u_s_num,
                count(case when substr(sv_keys,1) = '5' then 1 else null end) as u_vs_num,
                count(case when substr(sv_keys,2,1) = '1' then 1 else null end) as num_1,
                count(case when substr(sv_keys,2,1) = '2' then 1 else null end) as num_2,
                count(case when substr(sv_keys,2,1) = '3' then 1 else null end) as num_3,
                count(case when substr(sv_keys,2,1) = '4' then 1 else null end) as num_4
            from con_satisfaction
            where 1=1
                and sv_start_time &gt;= #{cond.startTime}
                and sv_start_time &lt;= #{cond.endTime}
                and cdr_hotline in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="cond.cnos != null and cond.cnos.size() > 0">
                    and bridged_cno in
                    <foreach collection="cond.cnos" item="item" open="(" close=")" separator="," index="index">
                        #{item}
                    </foreach>
                </if>
            group by date, bridged_cno
        )t2 on t.cdr_callee_cno = t2.bridged_cno and t.date=t2.date
    </select>

    <select id="selectSatisfactionCount" resultType="int">
        select count(1)
        from (
            select
            <if test="cond.period == 'day'">
                date_format(cpci.cdr_start_time, '%Y-%m-%d') as date,
            </if>
            <if test="cond.period == 'range'">
                concat(date_format(#{cond.startTime},'%Y/%m/%d'), ' - ', date_format(#{cond.endTime},'%Y/%m/%d')) as date,
            </if>
                cpci.staff_id, iacs.staff_name,
                cpci.cdr_callee_cno,count(1) as call_in_num
            from con_phone_call_info cpci
            inner join in_auth_crm_staff iacs on iacs.id = cpci.staff_id
            where cpci.cdr_call_type = '1'
                and cpci.cdr_start_time &gt;= #{cond.startTime}
                and cpci.cdr_start_time &lt;= #{cond.endTime}
                and cpci.cdr_hotline in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            group by date, cpci.cdr_callee_cno
        )t left join(
            select
            <if test="cond.period == 'day'">
                date_format(sv_start_time, '%Y-%m-%d') as date,
            </if>
            <if test="cond.period == 'range'">
                concat(date_format(#{cond.startTime},'%Y/%m/%d'), ' - ', date_format(#{cond.endTime},'%Y/%m/%d')) as date,
            </if>
                bridged_cno,
                count(case when length(sv_keys) &lt; 1 then 1 else null end) as no_part_num,
                count(case when length(sv_keys) &gt;= 1 then 1 else null end) as part_num,
                count(case when substr(sv_keys,1) = '1' then 1 else null end) as vs_num,
                count(case when substr(sv_keys,1) = '2' then 1 else null end) as s_num,
                count(case when substr(sv_keys,1) = '3' then 1 else null end) as common_num,
                count(case when substr(sv_keys,1) = '4' then 1 else null end) as u_s_num,
                count(case when substr(sv_keys,1) = '5' then 1 else null end) as u_vs_num,
                count(case when substr(sv_keys,2,1) = '1' then 1 else null end) as num_1,
                count(case when substr(sv_keys,2,1) = '2' then 1 else null end) as num_2,
                count(case when substr(sv_keys,2,1) = '3' then 1 else null end) as num_3,
                count(case when substr(sv_keys,2,1) = '4' then 1 else null end) as num_4
            from con_satisfaction
            where 1=1
                and sv_start_time &gt;= #{cond.startTime}
                and sv_start_time &lt;= #{cond.endTime}
                and cdr_hotline in
                <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            group by date, bridged_cno
        )t2 on t.cdr_callee_cno = t2.bridged_cno and t.date=t2.date
    </select>

</mapper>
