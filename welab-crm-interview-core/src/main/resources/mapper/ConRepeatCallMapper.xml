<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.ConRepeatCallMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.ConRepeatCall">
        <id column="id" property="id" />
        <result column="hotline" property="hotline" />
        <result column="cno" property="cno" />
        <result column="staff_id" property="staffId" />
        <result column="mobile" property="mobile" />
        <result column="repeat_number" property="repeatNumber" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, hotline, cno, staff_id, mobile, repeat_number, gmt_create, gmt_modify
    </sql>

    <select id="selectCallList" parameterType="com.welab.crm.interview.dto.repeatcall.RepeatCallDTO"
      resultType="com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO">
      select cno, staff_name, ifnull(repeat_number,0) as repeat_number, ifnull(call_in_number,0) as call_in_number,
        concat(ifnull(round((call_in_number-repeat_number)/call_in_number*100,2), '0.00'),'%') as resolvedRate
      from (
        select sum(crc.repeat_number) as repeat_number, crc.cno,iacs.staff_name
        from con_repeat_call crc
        inner join in_phone_login_info ipli on ipli.id_no=crc.cno
        inner join in_auth_crm_staff iacs on iacs.login_name=ipli.user_tel
        where 1=1
          and crc.gmt_create &gt;= #{cond.startTime}
          and crc.gmt_create &lt;= #{cond.endTime}
          and crc.hotline in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="cond.cnos != null and cond.cnos.size() > 0">
            and crc.cno in
            <foreach collection="cond.cnos" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>
        </if>
        group by crc.cno
      ) t1
      left join (
        select count(1) as call_in_number,cdr_callee_cno
        from con_phone_call_info
        where 1=1
          and cdr_call_type='1'
          and cdr_start_time &gt;= #{cond.startTime}
          and cdr_start_time &lt;= #{cond.endTime}
          and cdr_bridge_time is not null
        <if test="cond.cnos != null and cond.cnos.size() > 0">
            and cdr_callee_cno in
            <foreach collection="cond.cnos" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>
        </if>
        group by cdr_callee_cno
      ) t2 on t2.cdr_callee_cno=t1.cno
    </select>

</mapper>
