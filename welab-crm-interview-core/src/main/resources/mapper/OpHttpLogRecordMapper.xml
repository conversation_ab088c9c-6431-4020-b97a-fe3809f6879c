<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpHttpLogRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpHttpLogRecord">
        <id column="id" property="id" />
        <result column="login_name" property="loginName" />
        <result column="uuid" property="uuid" />
        <result column="request_param" property="requestParam" />
        <result column="create_time" property="createTime" />
        <result column="request_body" property="requestBody" />
        <result column="request_path" property="requestPath" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, login_name, uuid, request_param, create_time, request_body, request_path
    </sql>
    <select id="queryLogData" resultType="com.welab.crm.interview.vo.monitor.StaffMonitorReportVO">

        select 
        s.group_name as groupCode,
        ifnull(s.staff_name,l.login_name) as staffName,
        l.login_name,
        ifnull(sum(case when l.uuid is not null then 1 end),0) as userDetailQueryCount,
        ifnull(count(distinct l.uuid),0) as queryUserCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/user/decode' then 1 end ),0) as mobileDecodeCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/loan/application/agreement' then 1 end ),0) as
        loanAgreementQueryCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/file/query-by-id' then 1 end ),0) as soundQueryCount
        from
        op_http_log_record l
        left join in_auth_crm_staff s on
        l.login_name = s.login_name and s.is_status = 1
        where
        l.create_time between #{startTime} and #{endTime}
        group by
        l.login_name
    </select>

    <select id="queryLogDataByPhoneSummary" resultType="com.welab.crm.interview.vo.monitor.OpHttpLogRecordByPhoneSummary">
        select
        s.group_name as groupCode,
        ifnull(s.staff_name,l.login_name) as staffName,
        l.*
        from op_http_log_record l left join in_auth_crm_staff s on l.login_name = s.login_name and s.is_status = 1
        where l.create_time between #{startTime} and #{endTime}
    </select>

</mapper>
