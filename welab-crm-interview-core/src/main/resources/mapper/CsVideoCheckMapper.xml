<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.CsVideoCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.CsVideoCheck">
        <id column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="mobile" property="mobile" />
        <result column="customer_name" property="customerName" />
        <result column="staff_id" property="staffId" />
        <result column="img_code" property="imgCode" />
        <result column="voice_code" property="voiceCode" />
        <result column="token" property="token" />
        <result column="video_name" property="videoName" />
        <result column="is_collect" property="isCollect" />
        <result column="sms_log_id" property="smsLogId" />
        <result column="result_msg" property="resultMsg" />
        <result column="img_score" property="imgScore" />
        <result column="voice_score" property="voiceScore" />
        <result column="start_record_time" property="startRecordTime" />
        <result column="end_record_time" property="endRecordTime" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="read_msg" property="readMsg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, mobile, customer_name, staff_id, img_code, voice_code, token, video_name, is_collect, sms_log_id, result_msg, img_score, voice_score, start_record_time, end_record_time, gmt_create, gmt_modify, read_msg
    </sql>

</mapper>
