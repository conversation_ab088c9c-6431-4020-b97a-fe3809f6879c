<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpReduceApplyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpReduceApplyRecord">
        <id column="id" property="id" />
        <result column="application_id" property="applicationId" />
        <result column="user_id" property="userId" />
        <result column="amount" property="amount" />
        <result column="reducible_amount" property="reducibleAmount" />
        <result column="rate_after_reduce" property="rateAfterReduce" />
        <result column="reduction_type" property="reductionType" />
        <result column="reduction_reason" property="reductionReason" />
        <result column="approval_status" property="approvalStatus" />
        <result column="push_flag" property="pushFlag" />
        <result column="staff_id" property="staffId" />
        <result column="request_no" property="requestNo" />
        <result column="result" property="result" />
        <result column="complaint_channel" property="complaintChannel" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="customer_name" property="customerName" />
        <result column="task_status" property="taskStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_id, user_id, amount, reducible_amount, rate_after_reduce, reduction_type, reduction_reason, approval_status, push_flag, staff_id, request_no, result, complaint_channel, gmt_create, gmt_modify, customer_name, task_status
    </sql>
    <select id="selectAllPushedCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            (
                select
                    1
                from
                    op_reduce_apply_record orar
                where
                    request_no = #{requestNo}
                  and push_flag = 'Y'
                union all
                select
                    1
                from
                    op_refund_apply_record orar
                where
                    request_no = #{requestNo}
                  and push_flag = 'Y'
            )t
    </select>

</mapper>
