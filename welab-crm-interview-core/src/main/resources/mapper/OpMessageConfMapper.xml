<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpMessageConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpMessageConf">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="sms_code" property="smsCode" />
        <result column="directory" property="directory" />
        <result column="description" property="description" />
        <result column="context" property="context" />
        <result column="sms_type" property="smsType" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, sms_code, directory, description, context, sms_type, gmt_create, gmt_modify
    </sql>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        select max(sort) from op_message_conf
    </select>

</mapper>
