<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.CsCouponSendLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.CsCouponSendLog">
        <id column="id" property="id" />
        <result column="business" property="business" />
        <result column="coupon_id" property="couponId" />
        <result column="description" property="description" />
        <result column="amount" property="amount" />
        <result column="amount_type" property="amountType" />
        <result column="available_days" property="availableDays" />
        <result column="uuid" property="uuid" />
        <result column="user_id" property="userId" />
        <result column="operation_type" property="operationType" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="create_staff_group" property="createStaffGroup" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business, coupon_id, description, amount, amount_type, available_days, uuid, user_id, operation_type, create_staff_id, create_staff_group, gmt_create, gmt_modify
    </sql>

    <select id="selectLogVOPage" resultType="com.welab.crm.interview.vo.coupon.CouponSendLogVO">

        select ccsl.business as businessType, ccsl.coupon_id, ccsl.description, ccsl.amount, ccsl.amount_type,
          ccsl.available_days, ccsl.user_id, ccsl.operation_type, ccsl.create_staff_id as staff_id,
          ccsl.create_staff_group as groupCode,
          date_format(ccsl.gmt_create,'%Y-%m-%d %T') as gmt_create,
          iacs.staff_name
        from cs_coupon_send_log ccsl
        left join in_auth_crm_staff iacs on iacs.id=ccsl.create_staff_id
        where 1=1
        <if test="filter.businessType!=null and filter.businessType!=''">
            and ccsl.business = #{filter.businessType}
        </if>
        <if test="filter.couponId!=null">
            and ccsl.coupon_id = #{filter.couponId}
        </if>
        <if test="filter.amountType!=null and filter.amountType!=''">
            and ccsl.amount_type = #{filter.amountType}
        </if>
        <if test="filter.description!=null and filter.description!=''">
            and ccsl.description = #{filter.description}
        </if>
        <if test="filter.operationType!=null and filter.operationType!=''">
            and ccsl.operation_type = #{filter.operationType}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and ccsl.create_staff_id = #{filter.staffId}
        </if>
        <if test="filter.groupCodeList!=null">
            and ccsl.create_staff_group in
            <foreach collection="filter.groupCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="filter.startTime!=null">
            and ccsl.gmt_create &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime!=null">
            and ccsl.gmt_create &lt;= #{filter.endTime}
        </if>
    </select>

    <select id="selectLogVOList" resultType="com.welab.crm.interview.vo.coupon.CouponSendLogVO">

        select ccsl.business as businessType, ccsl.coupon_id, ccsl.description, ccsl.amount, ccsl.amount_type,
        ccsl.available_days, ccsl.user_id, ccsl.operation_type, ccsl.create_staff_id as staff_id,
        ccsl.create_staff_group as groupCode,
        date_format(ccsl.gmt_create,'%Y-%m-%d %T') as gmt_create,
        iacs.staff_name
        from cs_coupon_send_log ccsl
        left join in_auth_crm_staff iacs on iacs.id=ccsl.create_staff_id
        where 1=1
        <if test="filter.businessType!=null and filter.businessType!=''">
            and ccsl.business = #{filter.businessType}
        </if>
        <if test="filter.couponId!=null">
            and ccsl.coupon_id = #{filter.couponId}
        </if>
        <if test="filter.amountType!=null and filter.amountType!=''">
            and ccsl.amount_type = #{filter.amountType}
        </if>
        <if test="filter.description!=null and filter.description!=''">
            and ccsl.description = #{filter.description}
        </if>
        <if test="filter.operationType!=null and filter.operationType!=''">
            and ccsl.operation_type = #{filter.operationType}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and ccsl.create_staff_id = #{filter.staffId}
        </if>
        <if test="filter.groupCodeList!=null">
            and ccsl.create_staff_group in
             <foreach collection="filter.groupCodeList" item="item" index="index" separator="," open="(" close=")">
                 #{item}
             </foreach>
        </if>
        <if test="filter.startTime!=null">
            and ccsl.gmt_create &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime!=null">
            and ccsl.gmt_create &lt;= #{filter.endTime}
        </if>
    </select>

</mapper>
