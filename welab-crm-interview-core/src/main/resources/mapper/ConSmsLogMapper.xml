<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.ConSmsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.ConSmsLog">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="context" property="context" />
        <result column="sms_code" property="smsCode" />
        <result column="mobile" property="mobile" />
        <result column="send_status" property="sendStatus" />
        <result column="send_time" property="sendTime" />
        <result column="actual_send_time" property="actualSendTime" />
        <result column="replace_field" property="replaceField" />
        <result column="fail_description" property="failDescription" />
        <result column="received" property="received" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, context, sms_code, mobile, send_status, send_time, actual_send_time, received,
        replace_field, fail_description, create_staff_id, gmt_create, gmt_modify
    </sql>

    <select id="listLogs" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from con_sms_log
        where mobile = #{mobile} or customer_id = #{customerId}
    </select>
</mapper>
