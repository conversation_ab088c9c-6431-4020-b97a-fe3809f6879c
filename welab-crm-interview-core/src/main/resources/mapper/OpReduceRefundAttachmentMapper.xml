<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpReduceRefundAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpReduceRefundAttachment">
        <id column="id" property="id" />
        <result column="request_no" property="requestNo" />
        <result column="file_name" property="fileName" />
        <result column="unique_file_name" property="uniqueFileName" />
        <result column="staff_id" property="staffId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="staff_name" property="staffName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, request_no, file_name, unique_file_name, staff_id, gmt_create, staff_name
    </sql>

</mapper>
