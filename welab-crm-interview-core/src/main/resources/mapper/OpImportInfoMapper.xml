<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.OpImportInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.OpImportInfo">
        <id column="id" property="id" />
        <result column="file_name" property="fileName" />
        <result column="type" property="type" />
        <result column="create_user" property="createUser" />
        <result column="count" property="count" />
        <result column="gmt_create" property="gmtCreate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_name, type, create_user, count, gmt_create
    </sql>
    <select id="getImportLabelInfo" resultType="com.welab.crm.interview.vo.loan.LoanImportLabelVO">
        select
            ooi.company_tel as companyTel,
            '0' as type,
            '' as companyName,
            null as deptDate
        from op_outcase_info ooi
        where ooi.application_id = #{applicationId}
            limit 1
    </select>

</mapper>
