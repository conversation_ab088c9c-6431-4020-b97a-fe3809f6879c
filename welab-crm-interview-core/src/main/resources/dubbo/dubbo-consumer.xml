<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:jee="http://www.springframework.org/schema/jee" xmlns:aop="http://www.springframework.org/schema/aop"
  xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
  xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd  
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd 	
	http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.0.xsd 
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd 
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd
	">

  <!-- 当前服务作为消费者调用远程RPC服务时，因为都是基于接口的调用，所以interface只用写远程服务接口的路径，不要写实现 -->
  <!-- <dubbo:reference id="ssoService" interface="com.welab.sso.service.SsoService" /> -->

  <!--  wallet-user-center-api -->
  <dubbo:reference id="userInfoServiceFacade" interface="com.welab.wallet.user.service.UserInfoServiceFacade" check="false" timeout="60000" retries="-1"/>

  <!--审批-->
  <dubbo:reference id="applicationService" interface="com.wolaidai.approval.service.IApplicationService" check="false" timeout="60000" retries="-1"/>

  <!--document-->
  <dubbo:reference id="documentServiceFacade" interface="com.welab.document.interfaces.facade.DocumentServiceFacade"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="ossClientServiceFacade" interface="com.welab.document.interfaces.facade.OssClientServiceFacade"
    check="false" timeout="60000" retries="-1"/>

  <!-- user-center-api -->
  <dubbo:reference id="userService" interface="com.welab.usercenter.service.UserService" timeout="60000" check="false" retries="-1"/>
  <dubbo:reference id="profileService" interface="com.welab.usercenter.service.ProfileService" timeout="60000" check="false" retries="-1"/>
  <dubbo:reference id="userServiceFacade" interface="com.welab.user.interfaces.facade.UserServiceFacade" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="adminService" interface="com.welab.usercenter.service.AdminService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="companyService" interface="com.welab.usercenter.service.CompanyService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="partnerService" interface="com.welab.usercenter.service.PartnerService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="notesServiceFacade" interface="com.welab.user.interfaces.facade.NotesServiceFacade"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="educationService" interface="com.welab.usercenter.service.EducationService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="liaisonService" interface="com.welab.usercenter.service.LiaisonService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="documentService" interface="com.welab.usercenter.service.DocumentService" check="false" timeout="60000" retries="-1"/>

  <!-- welab-authority-api -->
  <dubbo:reference id="authorityService" interface="com.welab.authority.service.AuthorityService" check="false" timeout="60000" retries="-1"/>

  <!-- interview-api -->
  <dubbo:reference id="userInfoService" interface="com.welab.crm.interview.service.UserInfoService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="loansApplicationService" interface="com.welab.crm.interview.service.LoansApplicationService"
    check="false" timeout="60000" retries="-1"/>

  <!--	welab-capital-api -->
  <dubbo:reference id="capitalBasicProvider" interface="com.welab.capital.provider.CapitalBasicProvider" check="false" timeout="60000" retries="-1"/>

  <!--	agreement-api -->
  <dubbo:reference id="loanAgreementService" interface="com.welab.agreement.service.ILoanAgreementService" check="false"
    version="1.0.0" timeout="60000" retries="-1"/>
<!--  <dubbo:reference id="priceEngineServiceFacade"-->
<!--    interface="com.welab.agreement.interfaces.facade.PriceEngineServiceFacade" check="false" timeout="15000"/>-->

  <!-- application-center-api -->
  <dubbo:reference id="loanApplicationServiceFacade"
    interface="com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="creditApplicationServiceFacade"
    interface="com.welab.loanapplication.interfaces.facade.CreditApplicationServiceFacade" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="deviceInfoServiceFacade"
    interface="com.welab.loanapplication.interfaces.facade.DeviceInfoServiceFacade" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="loanApplicationServiceInterface"
    interface="com.wolaidai.appcenter.service.LoanApplicationServiceInterface" check="false" timeout="60000" retries="-1"/>

  <!--	product-api -->
  <dubbo:reference id="productServiceFacade" interface="com.welab.product.interfaces.facade.ProductServiceFacade"
    check="false" timeout="60000" retries="-1"/>

  <!--	finance-loan-procedure-api -->
  <dubbo:reference id="loanDubboService" interface="com.welab.finance.loanprocedure.dubbo.LoanDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="dueDubboService" interface="com.welab.finance.loanprocedure.dubbo.DueDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="loanRateDubboService" interface="com.welab.finance.loanprocedure.dubbo.LoanRateDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="userAgreementDubboService" interface="com.welab.finance.loanprocedure.dubbo.UserAgreementDubboService"
                   check="false" timeout="60000" retries="-1"/>

  <!--	finance-repayment-api -->
  <dubbo:reference id="repaymentCalculationDubboService"
    interface="com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="repaymentDubboService" interface="com.welab.finance.repayment.dubbo.RepaymentDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="repaymentRecordDubboService" interface="com.welab.finance.repayment.dubbo.RepaymentRecordDubboService"
    check="false"  timeout="60000" retries="-1"/>
  <dubbo:reference id="factorDubboService" interface="com.welab.finance.repayment.dubbo.FactorDubboService" retries="-1" timeout="60000"
    check="false" />

  <!--	finance-payment-api -->
  <dubbo:reference id="paymentDubboService" interface="com.welab.finance.payment.dubbo.PaymentDubboService"
    check="false" timeout="60000" retries="-1"/>

  <!--  finance-bank-card-api -->
  <dubbo:reference id="bankCardDubboService" interface="com.welab.finance.bankcard.dubbo.BankCardDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="bankDubboService" interface="com.welab.finance.bankcard.dubbo.BankDubboService" check="false" timeout="60000" retries="-1"/>

  <!--  finance-capital-allocation-api-->
  <dubbo:reference id="capitalMatchDubboService" interface="com.welab.finance.capital.dubbo.CapitalMatchDubboService"
      check="false" timeout="60000" retries="-1"/>

  <!--  finance-account-api -->
  <dubbo:reference id="financeTransDubboService" interface="com.welab.finance.account.dubbo.FinanceTransDubboService"
    check="false" timeout="60000" retries="-1"/>

  <!--  finance-accounting-api -->
  <dubbo:reference id="accountTransDubboService" interface="com.welab.finance.accounting.dubbo.AccountTransDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="financeAccountsDubboService"
    interface="com.welab.finance.accounting.dubbo.FinanceAccountsDubboService" check="false" timeout="60000" retries="-1"/>

  <!--  welab-creditline-api -->
  <dubbo:reference id="quotaService" interface="com.welab.support.credit.service.QuotaService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="quotaServiceForCS" interface="com.welab.support.credit.service.QuotaServiceForCS" check="false" timeout="60000" retries="-1"/>

  <!-- loan-center -->
  <dubbo:reference id="newMemberServiceFacade" interface="com.welab.loancenter.interfaces.facade.NewMemberServiceFacade"
                   timeout="60000" check="false" retries="-1"/>
  <dubbo:reference id="loanCenterService" interface="com.wolaidai.loancenter.service.LoanService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="loanBusinessService" interface="com.wolaidai.loancenter.api.service.LoanBusinessService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="assetBenefitServiceFacade" interface="com.welab.loancenter.interfaces.facade.AssetBenefitServiceFacade"
                   timeout="60000" check="false" retries="-1"/>

  <!--	wedefend-gateway	-->
  <dubbo:reference id="iWeDefendService" interface="com.welab.wdfgateway.service.IWeDefendService" check="false"
    cluster="failsafe" timeout="60000" retries="-1"/>
  <dubbo:reference id="weDefendServiceFacade" interface="com.welab.wdfgateway.interfaces.facade.WeDefendServiceFacade"
    check="false" timeout="60000" retries="-1"/>

  <!--	meta-space	-->
  <dubbo:reference id="areaService" interface="com.wolaidai.metaspace.service.AreaService" check="false" timeout="60000" retries="-1"/>

  <!--	marketing-api	-->
  <dubbo:reference id="newMarketingService" interface="com.welab.marketing.service.NewMarketingService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="couponV2Service" interface="com.welab.marketing.service.CouponV2Service"  check="false" retries="-1" timeout="60000"/>
  <dubbo:reference id="walletCouponService" interface="com.welab.marketing.service.wallet.WalletCouponService" check="false" retries="-1" timeout="60000"/>
  <dubbo:reference id="supVipDubboService" interface="com.welab.marketing.service.SupVipDubboService" check="false" retries="-1" timeout="60000"/>

  <!--wallet-repayment-api-->
  <dubbo:reference id="iRepaymentDubboService" interface="com.welab.wallet.repayment.dubbo.RepaymentDubboService"
    check="false" retries="-1" timeout="60000"/>
  <dubbo:reference id="walletRepaymentCalculationDubboService"
    interface="com.welab.wallet.repayment.dubbo.RepaymentCalculationDubboService" check="false" retries="-1" timeout="60000"/>

  <!--wallet-installment-api-->
  <dubbo:reference id="walletLoanDubbboService" interface="com.welab.wallet.installment.dubbo.WalletLoanDubbboService"
    check="false" timeout="60000" retries="-1">
    <dubbo:method name="queryLoan" retries="-1" timeout="60000"/>
  </dubbo:reference>

  <!--wallet-app-api-->
  <dubbo:reference id="walletOrderDubboService" interface="com.welab.wallet.app.dubbo.WalletOrderDubboService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="billDubboService" interface="com.welab.wallet.app.dubbo.BillDubboService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="walletUserDubboService" interface="com.welab.wallet.app.dubbo.WalletUserDubboService" check="false" timeout="60000" retries="-1"/>

  <!-- wallet-application-center-api -->
  <dubbo:reference id="walletCreditApplicationService"
    interface="com.welab.wallet.application.interfaces.facade.CreditApplicationServiceFacade" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="walletCreditTransformServiceFacade"
    interface="com.welab.wallet.application.interfaces.facade.WalletCreditTransformServiceFacade" check="false" retries="-1" timeout="60000"/>

  <!-- message-sms-api -->
  <dubbo:reference id="messageSendV4Service" interface="com.welab.message.sms.service.MessageSendV4Service" check="false" timeout="60000" retries="-1"/>

  <!-- welab-crm-base-api -->
  <dubbo:reference id="staffService" interface="com.welab.crm.base.service.StaffService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="wFRunBusiServiceImpl" interface="com.welab.crm.base.workflow.busi.service.WFRunBusiService" check="false" timeout="60000" retries="-1"/>


  <!-- welab-collection-interview-api -->
  <dubbo:reference id="complainService" interface="com.welab.collection.interview.service.ComplainService" timeout="60000" check="false" retries="-1">
    <dubbo:method name="addComplainCase" retries="-1" timeout="60000"/>
  </dubbo:reference>
  <dubbo:reference id="productService" interface="com.welab.collection.interview.service.ProductService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="faSuPushService" interface="com.welab.collection.interview.service.FaSuPushService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="phoneSummaryService" interface="com.welab.collection.interview.service.PhoneSummaryService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="interviewBlackListService" interface="com.welab.collection.interview.service.BlackListService" timeout="60000" retries="-1" check="false"/>
  <dubbo:reference id="lenderService" interface="com.welab.collection.interview.service.ILenderService" timeout="60000" check="false" retries="-1"/>
  <dubbo:reference id="userCenterService" interface="com.welab.collection.interview.service.IUserCenterService" check="false"
                   retries="-1" timeout="60000"/>
  <dubbo:reference id="reduceSchemeService" interface="com.welab.collection.interview.service.ReduceSchemeService" check="false" timeout="60000" retries="-1"/>


  <dubbo:reference id="capitalRuleProviderImpl"
      interface="com.welab.capital.provider.CapitalRuleProvider" timeout="60000" retries="-1"/>

  <!--消金 bank-card-api-->
  <dubbo:reference id="bankCardServiceFacade" interface="com.welab.bank.card.interfaces.facade.BankCardServiceFacade" check="false" retries="-1" timeout="60000"/>


  <!--wallet-payment-api -->

  <dubbo:reference id="walletPaymentOrderDubboService"
                   interface="com.welab.wallet.payment.dubbo.WalletOrderDubboService" check="false" retries="-1" timeout="60000"/>

  <!--voice-center-api-->
  <dubbo:reference id="departmentDetailFacade" interface="com.welab.voice.api.DepartmentDetailFacade" check="false"
                   retries="-1" timeout="60000"/>

  <dubbo:reference id="iRepaymentForWebService" interface="com.welab.collection.interview.service.IRepaymentForWebService" check="false" timeout="60000" retries="-1"/>
</beans>
