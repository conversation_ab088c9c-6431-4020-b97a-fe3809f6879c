<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:jee="http://www.springframework.org/schema/jee" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd  
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd 	
	http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.0.xsd 
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd 
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd
	">

	<!-- 提供方应用信息，用于计算依赖关系 -->
	<dubbo:application name="${app.id}" />

	<!-- 使用zookeeper注册中心暴露服务地址 -->
	<dubbo:registry address="${zookeeper.url}" protocol="zookeeper" client="curator" check="false" timeout="6000" />

	<!-- 用dubbo协议在20880端口暴露服务，如果接口冲突请修改下此处接口 -->
	<dubbo:protocol name="dubbo" port="20880" payload="104857600" threads="1000"/>

	<dubbo:provider threads="${dubbo.provider.thread}" accepts="${dubbo.provider.accepts}"/>
	<!-- 有两种方式，二选一，注意两个都存在会导致服务启动异常，当前示例采用的是第二种方式 -->

	<!-- 第一种方式： ScaffoldServiceImpl类上没有@Service的注解 -->
	<!-- bean是为了指明接口的实现 -->
	<!-- <bean id="scaffoldService" class="com.welab.crm.interview.service.impl.ScaffoldServiceImpl" /> -->
	<!-- 声明需要暴露的服务接口，其它服务通过RPC远程调用当前服务时就是通过这里的定义来调用的，注意interface写接口的路径，不要写实现的路径 -->
	<!-- <dubbo:service ref="scaffoldService" interface="com.welab.crm.interview.service.ScaffoldService" timeout="5000" /> -->

	<!-- 第二种方式： ScaffoldServiceImpl类上有@Service的注解 -->
	<!-- 声明需要暴露的服务接口，其它服务通过RPC远程调用当前服务时就是通过这里的定义来调用的，注意interface写接口的路径，不要写实现的路径 -->
	<!-- <dubbo:service ref="scaffoldServiceImpl" interface="com.welab.crm.interview.service.ScaffoldService" timeout="5000" /> -->

	 <dubbo:service ref="userInfoServiceImpl" interface="com.welab.crm.interview.service.UserInfoService" timeout="20000" retries="0" />
	 <dubbo:service ref="loansApplicationServiceImpl" interface="com.welab.crm.interview.service.LoansApplicationService" timeout="20000" retries="0"/>
	 <dubbo:service ref="couponServiceImpl" interface="com.welab.crm.interview.service.CouponService" timeout="5000" />
	 <dubbo:service ref="bankCardServiceImpl" interface="com.welab.crm.interview.service.BankCardService" timeout="5000" />
	 <dubbo:service ref="labelServiceImpl" interface="com.welab.crm.interview.service.LabelService" timeout="15000" retries="0"/>
	 <dubbo:service ref="loanApplicationServiceImpl" interface="com.welab.crm.interview.service.LoanApplicationService" timeout="20000" retries="0" />
	 <dubbo:service ref="walletServiceImpl" interface="com.welab.crm.interview.service.WalletService" timeout="20000" retries="0"/>
	 <dubbo:service ref="vipServiceImpl" interface="com.welab.crm.interview.service.VipService" timeout="15000" retries="0"/>
	 <dubbo:service ref="uploadService" interface="com.welab.crm.interview.service.IUploadService" timeout="15000" retries="0"/>
	 <dubbo:service ref="messageServiceImpl" interface="com.welab.crm.interview.service.MessageService" timeout="15000" retries="0"/>
	 <dubbo:service ref="wechatPushServiceImpl" interface="com.welab.crm.interview.service.WeChatDubboService" timeout="5000"/>
	 <dubbo:service ref="conRepeatCallServiceImpl" interface="com.welab.crm.interview.service.ConRepeatCallService" timeout="5000"/>
	 <dubbo:service ref="satisfactionServiceImpl" interface="com.welab.crm.interview.service.SatisfactionService" timeout="5000"/>
	 <dubbo:service ref="aiPushJobServiceImpl" interface="com.welab.crm.interview.service.AiPushJobService" timeout="5000"/>
	 <dubbo:service ref="financeServiceImpl" interface="com.welab.crm.interview.service.FinanceService" timeout="30000">
		 <dubbo:method name="getSettlementUrl" timeout="30000" retries="0"/>
	 </dubbo:service>
	 <dubbo:service ref="complainOrderServiceImpl" interface="com.welab.crm.interview.service.ComplainOrderService" timeout="5000"/>
	 <dubbo:service ref="tmkUuidService" interface="com.welab.crm.interview.service.TmkUuidService" timeout="15000"/>
	 <dubbo:service ref="trTokenService" interface="com.welab.crm.interview.service.TrTokenService" timeout="15000"/>
	 <dubbo:service ref="repayUploadService" interface="com.welab.crm.interview.service.RepayUploadService" timeout="10000"/>
	 <dubbo:service ref="faSuService" interface="com.welab.crm.interview.service.FaSuService" timeout="10000"/>
	 <dubbo:service ref="conPhoneSummaryService" interface="com.welab.crm.interview.service.ConPhoneSummaryService" timeout="10000"/>
	 <dubbo:service ref="workOrderServiceImpl" interface="com.welab.crm.interview.service.WorkOrderService" timeout="10000"/>
</beans>
