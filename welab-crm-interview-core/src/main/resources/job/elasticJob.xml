<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:reg="http://www.dangdang.com/schema/ddframe/reg"
  xmlns:job="http://www.dangdang.com/schema/ddframe/job"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.dangdang.com/schema/ddframe/reg
                        http://www.dangdang.com/schema/ddframe/reg/reg.xsd
                        http://www.dangdang.com/schema/ddframe/job
                        http://www.dangdang.com/schema/ddframe/job/job.xsd">

  <reg:zookeeper id="regCenter" server-lists="${job.registry.address}" namespace="welab-crm-interview-job"
    base-sleep-time-milliseconds="10000" max-sleep-time-milliseconds="30000" max-retries="3"/>

  <job:simple id="smsSendJob" class="com.welab.crm.interview.job.SmsSendJob"
    reconcile-interval-minutes="10"
    registry-center-ref="regCenter"
    sharding-total-count="1"
    cron="${job.sms.send.cron}"
    sharding-item-parameters="0=0"
    monitor-execution="false"
    monitor-port="9890"
    failover="true"
    description="短信发送定时任务"
    overwrite="true"
    disabled="false"/>

  <job:simple id="SmsConfirmJob" class="com.welab.crm.interview.job.SmsConfirmJob"
    reconcile-interval-minutes="10"
    registry-center-ref="regCenter"
    sharding-total-count="1"
    cron="${job.sms.confirm.cron}"
    sharding-item-parameters="0=0"
    monitor-execution="false"
    monitor-port="9891"
    failover="true"
    description="确认客户是否收到短信定时任务"
    overwrite="true"
    disabled="false"/>

  <job:simple id="withholdSyncJob" class="com.welab.crm.interview.job.WithholdSyncJob"
      reconcile-interval-minutes="10"
      registry-center-ref="regCenter"
      sharding-total-count="1"
      cron="${job.withhold.sync.cron}"
      sharding-item-parameters="0=0"
      monitor-execution="false"
      monitor-port="9892"
      failover="true"
      description="代扣同步定时任务"
      overwrite="true"
      disabled="false"/>

  <job:simple id="quotaSyncJob" class="com.welab.crm.interview.job.QuotaSyncJob"
      reconcile-interval-minutes="10"
      registry-center-ref="regCenter"
      sharding-total-count="1"
      cron="${job.quota.sync.cron}"
      sharding-item-parameters="0=0"
      monitor-execution="false"
      monitor-port="9893"
      failover="true"
      overwrite="true"
      description="额度同步任务"
      disabled="false"/>

  <job:simple id="lhSettlementJob" class="com.welab.crm.interview.job.LhSettlementJob"
      reconcile-interval-minutes="10"
      registry-center-ref="regCenter"
      sharding-total-count="1"
      cron="${job.settle.sync.cron}"
      sharding-item-parameters="0=0"
      monitor-execution="false"
      monitor-port="9894"
      failover="true"
      overwrite="true"
      description="蓝海结清证明获取任务"
      disabled="false"/>

  <job:simple id="pullTrTrunkReportIbJob" class="com.welab.crm.interview.job.PullTrTrunkReportIbJob"
      reconcile-interval-minutes="10"
      registry-center-ref="regCenter"
      sharding-total-count="1"
      cron="${job.pull.ib.report.cron}"
      sharding-item-parameters="0=0"
      monitor-execution="false"
      monitor-port="9895"
      failover="true"
      overwrite="true"
      description="天润呼入报表数据拉取"
      disabled="false"/>

  <job:simple id="refundDataPushJob" class="com.welab.crm.interview.job.RefundDataPushJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.refund.push.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9896"
              failover="true"
              overwrite="true"
              description="退款记录推送任务"
              disabled="false"/>

  <job:simple id="h5RepayLinkRepayResultSyncJob" class="com.welab.crm.interview.job.H5RepayLinkRepayResultSyncJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.h5repay.result.sync.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9897"
              failover="true"
              overwrite="true"
              description="h5还款链接还款结果同步任务"
              disabled="false"/>

  <job:simple id="staffMonitorWarningJob" class="com.welab.crm.interview.job.StaffMonitorWarningJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.staff.monitor.warning.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9898"
              failover="true"
              overwrite="true"
              description="员工监控告警任务"
              disabled="false"/>

  <job:simple id="orderNoticeDailyJob" class="com.welab.crm.interview.job.OrderNoticeDailyJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.order.notice.daily.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9899"
              failover="true"
              overwrite="true"
              description="每日工单提醒任务"
              disabled="false"/>

  <job:simple id="orderTaskWithinTwoMonthNoticeJob" class="com.welab.crm.interview.job.OrderTaskWithinTwoMonthNoticeJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.order.notice.within.two.month.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9900"
              failover="true"
              overwrite="true"
              description="工单未结案30-59天通知任务"
              disabled="false"/>

  <job:simple id="orderTaskOverTwoMonthNoticeJob" class="com.welab.crm.interview.job.OrderTaskOverTwoMonthNoticeJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.order.notice.over.two.month.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9901"
              failover="true"
              overwrite="true"
              description="工单未结案60天以上通知任务"
              disabled="false"/>

  <job:simple id="overSevenDayUnContactOrderNoticeJob" class="com.welab.crm.interview.job.OverSevenDayUnContactOrderNoticeJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.over.sevenday.uncontact.order.notice.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9902"
              failover="true"
              overwrite="true"
              description="7天未联系工单通知任务"
              disabled="false"/>

  <job:simple id="overTwentyFourHourUnContactOrderNoticeJob" class="com.welab.crm.interview.job.OverTwentyFourHourUnContactOrderNoticeJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.over.twentyfour.hour.uncontact.order.notice.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9903"
              failover="true"
              overwrite="true"
              description="24小时未联系工单通知任务"
              disabled="false"/>

  <job:simple id="orderResponseTimeOutStateUpdateJob" class="com.welab.crm.interview.job.OrderResponseTimeOutStateUpdateJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.response.timeout.state.update.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9904"
              failover="true"
              overwrite="true"
              description="工单超时未联系状态更新"
              disabled="false"/>
  
  

  
</beans>
