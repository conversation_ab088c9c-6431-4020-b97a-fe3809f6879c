package com.welab.crm.interview.enums;

public enum ReduceAndRefundApprovalStatusEnum {
	APPLY("0", "申请"),
	FIRST_APPROVAL("1","初审通过"),
	SECOND_APPROVAL("2","复审通过"),
	REFUSE("3","拒绝")
	;

	private String code;
	
	private String desc;

	ReduceAndRefundApprovalStatusEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
	
	
	public static String getDescByCode(String code){
		for (ReduceAndRefundApprovalStatusEnum statusEnum : values()) {
			if (statusEnum.code.equals(code)){
				return statusEnum.desc;
			}
		}
		
		return code;
	}
}
