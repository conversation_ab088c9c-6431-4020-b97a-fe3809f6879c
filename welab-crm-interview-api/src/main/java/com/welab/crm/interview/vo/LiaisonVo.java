package com.welab.crm.interview.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 联系人信息
 * Created by Lyman on 2016/11/24.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LiaisonVo implements Serializable {
	private static final long serialVersionUID = -2281423698540704792L;

	private String name;
	private String relation;
	private String mobile;

	/**
	 * 是否拉入联系人黑名单
	 */
	private Boolean isBlack;


	/**
	 * 有效开始时间
	 */
	private String validStartTime;

	/**
	 * 有效结束时间
	 */
	private String validEndTime;

	public LiaisonVo(String name, String relation, String mobile) {
		this.name = name;
		this.relation = relation;
		this.mobile = mobile;
	}
}
