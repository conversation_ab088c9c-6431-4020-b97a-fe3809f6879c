package com.welab.crm.interview.dto.message;

import com.welab.validator.Mobile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Data
@ApiModel(value = "发送短信请求对象")
public class MessageSendDTO implements Serializable {

    private static final long serialVersionUID = -8580579459511743149L;

    @ApiModelProperty(value = "短信模板id")
    @NotNull
    private Long messageId;

    @ApiModelProperty(value = "客户姓名")
    @NotBlank
    private String name;

    @ApiModelProperty(value = "客户手机号")
    @NotBlank
    @Mobile
    private String mobile;

    @ApiModelProperty(value = "期望发送时间, 格式 yyyy-MM-dd HH:mm:ss")
    @NotNull
    private String sendTime;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "客户Id")
    private Long customerId;

    @ApiModelProperty(value = "员工登录名称")
    private String loginName;

    @ApiModelProperty(value = "视频录制时需要客户说的话")
    private String readMsg;



    @ApiModelProperty(value = "其他发送参数，用于存放非name的发送变量")
    private Map<String,String> otherParams;
}
