/**
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.service;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.welab.crm.interview.dto.UserDTO;
import com.welab.crm.interview.vo.UserVO;
import com.welab.xdao.context.page.Page;

/**
 * @description TODO 这里描述类的用处，例如：用户服务接口
 *
 * <AUTHOR> 请修改名字，例如：gorge.guo
 * @date TODO 请修改时间，例如：2017-12-18 17:19:30
 * @version v1.0
 */

public interface ScaffoldService {

	/**
	 * 保存用户信息
	 *
	 * @param userDTO
	 *            用户信息
	 * @return id 用户id
	 */
	// 校验分组用，详细可以参照阿里的Dubbo框架的说明文档，里边有参数校验的说明
	@interface SaveUserInfo {
	}

	Long saveUserInfo(@NotNull(message = "用户信息不能为空") UserDTO userDTO);

	/**
	 * 批量保存用户信息
	 *
	 * @param userDTOList
	 *            用户信息集合
	 */
	void saveUserInfoBatch(@NotNull(message = "用户集合信息不能为空") List<UserDTO> userDTOList);

	/**
	 * 更新用户信息示例
	 *
	 * @param userDTO
	 */
	void updateUserInfo(@NotNull(message = "用户信息不能为空") UserDTO userDTO);

	/**
	 * 通过用户id逻辑删除用户信息
	 *
	 * @param id
	 *            用户id
	 */
	void deleteUserInfoById(@NotNull(message = "用户id不能为空") Long id);

	/**
	 * 通过用户id查询用户信息
	 *
	 * @param id
	 *            用户id
	 * @return UserVO 用户信息
	 */
	UserVO getUserInfoById(@NotNull(message = "用户id不能为空") Long id);

	/**
	 * 查询某个状态的所有用户记录集合
	 *
	 * @param isDelete
	 *            是否有效
	 * @return UserVO 用户信息集合
	 */
	List<UserVO> getUserInfoList(@NotNull(message = "用户状态不能为空") Boolean isDelete);

	/**
	 * 查询某个状态的所有用户记录Map集合
	 *
	 * @param id
	 *            用户id
	 * @param isDelete
	 *            是否有效
	 * @return 用户集合Map
	 */
	Map<Long, UserVO> getUserInfoMap(@NotNull(message = "用户id不能为空") Long id,
			@NotNull(message = "用户状态不能为空") Boolean isDelete);

	/**
	 * 分页查询某个状态的所有用户记录集合
	 *
	 * @param isDelete
	 *            是否有效
	 * @param page
	 *            第几页
	 * @param rowsPerPage
	 *            每页多少条记录
	 * @return 分页用户集合
	 */
	Page<UserVO> getUserInfoListPage(@NotNull(message = "用户状态不能为空") Boolean isDelete, int page, int rowsPerPage);

}
