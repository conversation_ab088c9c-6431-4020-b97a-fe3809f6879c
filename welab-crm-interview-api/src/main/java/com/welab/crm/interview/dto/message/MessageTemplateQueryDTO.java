package com.welab.crm.interview.dto.message;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Data
@ApiModel(value = "查询短信模板请求对象")
public class MessageTemplateQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1057652433997904226L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "短信模板代码")
    private String smsCode;

    @ApiModelProperty(value = "短信标题")
    private String title;

    @ApiModelProperty(value = "目录组")
    private String directory;

    @ApiModelProperty(value = "短信类型")
    private String smsType;
}
