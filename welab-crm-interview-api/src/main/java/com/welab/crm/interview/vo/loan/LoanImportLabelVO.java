package com.welab.crm.interview.vo.loan;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class LoanImportLabelVO {

    /**
     * 名单类型（0:委外,1:债转）
     */
    @ApiModelProperty(value = "名单类型（0:委外,1:债转）")
    private String type;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司电话
     */
    @ApiModelProperty(value = "公司电话")
    private String companyTel;

    /**
     * 债转时间
     */
    @ApiModelProperty(value = "债转时间")
    private Date deptDate;
}
