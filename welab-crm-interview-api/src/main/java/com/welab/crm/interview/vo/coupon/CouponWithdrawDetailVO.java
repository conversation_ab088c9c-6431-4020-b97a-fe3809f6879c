package com.welab.crm.interview.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/18
 */
@Data
@ApiModel(value = "用户返现红包详情响应对象")
public class CouponWithdrawDetailVO implements Serializable {

    private static final long serialVersionUID = 1041324622038100890L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "uuid")
    private Long uuid;

    @ApiModelProperty(value = "类型")
    private String channel;

    @ApiModelProperty(value = "贷款号")
    private String desc;

    @ApiModelProperty(value = "金额")
    private String amountDesc;

    @ApiModelProperty(value = "日期(可提现列表为有效期/历史记录为处理日期)")
    private Date date;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "记录类型(withdraw 提现记录; coupon 卡券记录)")
    private String recordType;
}
