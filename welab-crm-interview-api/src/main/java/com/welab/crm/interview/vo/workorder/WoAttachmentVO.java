package com.welab.crm.interview.vo.workorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 工单附件vo
 * <AUTHOR>
 * @date 2021/12/22 14:18
 */
@Data
public class WoAttachmentVO implements Serializable {

    private static final long serialVersionUID = 6543473462934621213L;

    @ApiModelProperty(value = "主键Id，删除的时候传这个", name = "id")
    private Long id;

    @ApiModelProperty(value = "文件名", name = "filename")
    private String filename;
    
    @ApiModelProperty(value = "添加了随机前缀的唯一文件名")
    private String uniqueFilename;

    @ApiModelProperty(value = "文件下载路径", name = "path")
    private String path;

    @ApiModelProperty(value = "上传人", name = "staffName")
    private String staffName;

    @ApiModelProperty(value = "上传时间", name = "uploadTime")
    private Date uploadTime;

    @ApiModelProperty(value = "上传人Id", name = "staffId")
    private Long staffId;

}
