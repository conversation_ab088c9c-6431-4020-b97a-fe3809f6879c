package com.welab.crm.interview.vo.satisfation;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/24
 */
@Data
@ApiModel(value = "坐席满意度报表响应对象")
@Excel(fileName = "坐席满意度报表")
public class ReportSatisfactionVO implements Serializable {

    private static final long serialVersionUID = -8818671631330770154L;

    @ApiModelProperty(value = "时间")
    @ExcelTitleMap(title = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String cno;

    @ApiModelProperty(value = "姓名")
    @ExcelTitleMap(title = "姓名")
    private String staffName;

    private String groupName;

    @ApiModelProperty(value = "接待量")
    @ExcelTitleMap(title = "接待量")
    private String callInNum;

    @ApiModelProperty(value = "成功邀评数")
    @ExcelTitleMap(title = "成功邀评数")
    private String inviteNum;

    @ApiModelProperty(value = "成功邀评率")
    @ExcelTitleMap(title = "成功邀评率")
    private String inviteRate;

    @ApiModelProperty(value = "未成功邀评数")
    @ExcelTitleMap(title = "未成功邀评数")
    private String noInviteNum;

    @ApiModelProperty(value = "参评数")
    @ExcelTitleMap(title = "参评数")
    private String partNum;

    @ApiModelProperty(value = "参评率")
    @ExcelTitleMap(title = "参评率")
    private String partRate;

    @ApiModelProperty(value = "未参评数")
    @ExcelTitleMap(title = "未参评数")
    private String noPartNum;

    @ApiModelProperty(value = "非常满意")
    @ExcelTitleMap(title = "非常满意")
    private String vsNum;

    @ApiModelProperty(value = "满意")
    @ExcelTitleMap(title = "满意")
    private String sNum;

    @ApiModelProperty(value = "一般")
    @ExcelTitleMap(title = "一般")
    private String commonNum;

    @ApiModelProperty(value = "不满意")
    @ExcelTitleMap(title = "不满意")
    private String uSNum;

    @ApiModelProperty(value = "非常不满意")
    @ExcelTitleMap(title = "非常不满意")
    private String uVsNum;

    @ApiModelProperty(value = "满意占比")
    @ExcelTitleMap(title = "满意占比")
    private String sRate;

    @ApiModelProperty(value = "不满意占比")
    @ExcelTitleMap(title = "不满意占比")
    private String usRate;

    @ApiModelProperty(value = "客服业务不熟悉数量")
    @ExcelTitleMap(title = "客服业务不熟悉数量")
    private String num1;

    @ApiModelProperty(value = "处理时效长数量")
    @ExcelTitleMap(title = "处理时效长数量")
    private String num2;

    @ApiModelProperty(value = "服务态度差数量")
    @ExcelTitleMap(title = "服务态度差数量")
    private String num3;

    @ApiModelProperty(value = "业务规则不满数量")
    @ExcelTitleMap(title = "业务规则不满数量")
    private String num4;

    @ApiModelProperty(value = "客服业务不熟悉占比")
    @ExcelTitleMap(title = "客服业务不熟悉占比")
    private String num1Rate;

    @ApiModelProperty(value = "处理时效长占比")
    @ExcelTitleMap(title = "处理时效长占比")
    private String num2Rate;

    @ApiModelProperty(value = "服务态度差占比")
    @ExcelTitleMap(title = "服务态度差占比")
    private String num3Rate;

    @ApiModelProperty(value = "业务规则不满占比")
    @ExcelTitleMap(title = "业务规则不满占比")
    private String num4Rate;

    @ApiModelProperty(value = "员工id")
    private String staffId;
}
