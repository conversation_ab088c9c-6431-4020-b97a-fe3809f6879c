/**
 * @Title: PersonalDetailsVoExpand.java
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.vo;

import com.welab.crm.interview.vo.vip.VipTypeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description TODO
 *
 * <AUTHOR>
 * @date 2018-07-13 15:34:19
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonalDetailsVoExpand implements Serializable {
    private static final long serialVersionUID = -5178647889379335509L;


    /**
     * 客服表内的用户主键Id
     */
    private Long id;
    /**
     * userId
     */
    private Integer userId;
    /**
     * 最后登录时间
     */
    private Date lastSignInAt;
    /**
     * 性别
     */
    private String gender;
    /**
     * 客户信息详情
     */
    private PersonalDetailsVo personalDetailsVo;
    /**
     * 来源
     */
    private String origin;
    /**
     * 是否代理
     */
    private Boolean agent;
    /**
     * 信用额度
     */
    private String creditLine;
    /**
     * 可用信用额度
     */
    private String availableCredit;

    /**
     * 额度状态
     */
    private String creditState;

    /**
     * 用户uuid
     */
    private String uuid;
    
    /**
     * 用户是否注销
     */
    private Boolean block;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 闪电贷可用额度
     * @return
     */
    private BigDecimal sddAvailableCredit;

    /**
     * 闪电贷信用额度
     * @return
     */
    private BigDecimal sddCreditLine;

    /**
     * 闪电贷额度状态
     * @return
     */
    private String sddStatus;

    /**
     * 是否vip用户
     */
    private String isVip;

    /**
     * 会员开通日期
     */
    private String cardCreatedAt;

    /**
     * 会员失效日期
     */
    private String cardExpiryAt;

    /**
     * 运营商
     */
    private String operator;

    /**
     * 注册时间
     */
    private Date registerAt;

    /**
     * 闪电贷额度状态码
     */
    private String sddStatusCode;

    /**
     * 是否逾期
     */
    private Boolean isOverdue;

    /**
     * 钱包是否逾期
     */
    private Boolean walletIsOverdue;

    /**
     * 成功借款笔数
     */
    private Integer sucLoanCount;

    /**
     * 脱敏的手机号
     */
    private String mobileMask;

    /**
     * 信息更新时间
     */
    private Date updateAt;

    /**
     * 会员信息列表
     */
    private List<VipTypeVO> vipList;

    /**
     * 黑名单有效期区间
     */
    private String blackListExpiry;

}
