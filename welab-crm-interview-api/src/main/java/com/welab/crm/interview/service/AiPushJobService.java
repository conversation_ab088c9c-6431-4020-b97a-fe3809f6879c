package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.ai.AiTmkConfigDTO;

import java.util.List;

/**
 * 处理ai推送job的服务
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022.2.24
 */
public interface AiPushJobService {

    /**
     * 新增ai外呼定时器
     */
    void createPushJob(AiTmkConfigDTO addDTO);

    /**
     * 批量新增ai外呼定时器
     */
    void batchCreatePushJob(List<AiTmkConfigDTO> configDTOList);

    /**
     * 更新ai外呼定时器的推送设置
     */
    void updatePushJob(AiTmkConfigDTO updateDTO);

    /**
     * 删除已存在的定时器
     */
    void deletePushJob(Long id);

    /**
     * 批量删除已存在的定时器
     */
    void batchDeletePushJob(List<Long> idList);
}
