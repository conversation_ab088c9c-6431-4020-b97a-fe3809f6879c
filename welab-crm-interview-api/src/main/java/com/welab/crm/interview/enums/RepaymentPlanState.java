package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 现金贷还款状态枚举
 * @date 2021/11/17
 */
public enum RepaymentPlanState {
    // 资金的状态
    CONFIRMED("confirmed", "已确认"),
    FUNDED("funded", "已满标"),
    DISBURSED("disbursed", "已放款"),
    DELINQUENT("delinquent", "已逾期"),
    REPAIED("repaied", ""),
    ERALY_SETTLED("early_settled", "提前结清"),
    CLOSED("closed", "已结清"),
    CANCELLED("cancelled", "已取消"),
    DISBURSE_FAILED("disburse_failed", "放款失败"),
    // 客服展示的状态
    SETTLED("settled", "结清"),
    OVERDUE("overdue", "逾期"),
    OPEN("open", "待还");

    private String value;
    private String text;

    RepaymentPlanState(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
