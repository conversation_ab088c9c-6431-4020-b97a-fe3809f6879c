package com.welab.crm.interview.dto.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Data
@ApiModel(value = "钱包账单查询请求对象")
public class WalletBillQueryDTO implements Serializable {

    private static final long serialVersionUID = -221397819435205005L;

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @NotBlank(message = "账单开始时间不能为空")
    @ApiModelProperty(value = "账单开始时间，格式：yyyyy-MM")
    private String billStart;

    @NotBlank(message = "账单结束时间不能为空")
    @ApiModelProperty(value = "账单结束时间，格式：yyyyy-MM")
    private String billEnd;
}
