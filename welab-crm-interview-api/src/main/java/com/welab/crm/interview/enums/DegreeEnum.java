package com.welab.crm.interview.enums;

/**
 * 学历枚举
 * <AUTHOR>
 */
public enum DegreeEnum {

    FOUR(4,"中专"),
    FIVE(5,"大专"),
    SIX(6,"本科"),
    SEVEN(7,"硕士"),
    <PERSON><PERSON><PERSON>(8,"博士"),
    TEN(10,"高中及以下"),
    TWELVE(12,"未知");

    private Integer id;

    private String name;

    DegreeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }


    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(Integer id){
        for (DegreeEnum degreeEnum : values()) {
            if (degreeEnum.id.equals(id)){
                return degreeEnum.name;
            }
        }
        return "";
    }
}
