package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.ApplicationOperateDTO;
import com.welab.crm.interview.dto.loanTransfer.LoanTransferDTO;
import com.welab.crm.interview.vo.LiaisonVo;
import com.welab.crm.interview.vo.LoanInfoVo;
import com.welab.crm.interview.vo.PersonalBasicVo;
import com.welab.crm.interview.vo.PersonalDetailsVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/27 16:38
 */
public interface LoansApplicationService {


    /**
     * 手机号或姓名或贷款编号查询贷款申请信息
     *
     * @param name
     * @param mobile
     * @return
     */
    LoanInfoVo getLoansApplicationByMobileOrNameOrApplicationId(String name, String mobile, String applicationId);


    /**
     * 手机号或姓名查询贷款申请信息
     *
     * @param name
     * @param mobile
     * @return
     */
    LoanInfoVo getLoansApplicationByMobileOrName(String name, String mobile);

    /**
     * 根据uuid获取用户额度
     */
    PersonalBasicVo getUserQuota(Long uuid);

    /**
     * 根据uuid查出 申请人信息、教育信息、单位信息、联系人信息、证明信息
     * @param uuid
     * @return
     */
    PersonalDetailsVo selectPersionBackgroundInformation(Long uuid);


    /**
     * 贷款操作
     * @param applicationOperateDTO
     * @return
     */
    Boolean applicationOperate(ApplicationOperateDTO applicationOperateDTO);



    /**
     * 发送加急审批
     * @param applicationId
     * @return
     */
    String sendUrgentApproval(String applicationId);

    /**
     * 债转结清
     *
     * @param contractList 贷款号列表
     */
    Response<String> loanSettled(List<LoanTransferDTO> contractList);

    /**
     * 贷款取消
     *
     * @param contractList 贷款号列表
     */
    boolean loanCancel(List<String> contractList);


    /**
     * 根据userID获取联系人信息(手机号未加密)
     * @param userId
     * @return
     */
    List<LiaisonVo> getLiaisonByUserId(Integer userId);
}
