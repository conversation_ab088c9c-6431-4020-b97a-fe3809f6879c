package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 收支明细
 * <AUTHOR>
 */
@Data
@ApiModel(value = "收支明细响应对象")
public class PaymentsDetailsVO implements Serializable {
	private static final long serialVersionUID = 2400825044910565967L;

	@ApiModelProperty(value = "交易时间", name = "transTime")
	private Date transTime;

	@ApiModelProperty(value = "贷款号", name = "applicationId")
	private String applicationId;

	@ApiModelProperty(value = "交易金额", name = "transAmount")
	private BigDecimal transAmount;

	@ApiModelProperty(value = "交易类型", name = "transType")
	private String transType;

	@ApiModelProperty(value = "渠道信息", name = "channelInfo")
	private String channelInfo;

	@ApiModelProperty(value = "支付渠道", name = "channel")
	private String channel;

	@ApiModelProperty(value = "备注", name = "remark")
	private String remark;

	@ApiModelProperty(value = "还款方式", name = "repaymentType")
	private String repaymentType;

	@ApiModelProperty(value = "业务流水号", name = "serviceNo")
	private String serviceNo;

	@ApiModelProperty(value = "是否新账号", name = "accountFlag")
	private Boolean accountFlag;

	@ApiModelProperty(value = "底色")
	private String backgroundColor;

}
