package com.welab.crm.interview.dto.satisfaction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/24
 */
@Data
@ApiModel(value = "天润推送满意度调查接口请求对象")
public class SatisfactionVerifyDTO implements Serializable {

    private static final long serialVersionUID = 8270385402398683978L;

    @ApiModelProperty(value = "企业id")
    private String cdr_enterprise_id;

    @ApiModelProperty(value = "中继号码")
    private String cdr_number_trunk;

    @ApiModelProperty(value = "热线号码")
    private String cdr_hotline;

    @ApiModelProperty(value = "通话标识")
    private String cdr_main_unique_id;

    @ApiModelProperty(value = "客户号码")
    private String cdr_customer_number;

    @ApiModelProperty(value = "呼叫类型，1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫")
    private String cdr_call_type;

    @ApiModelProperty(value = "开始时间")
    private String sv_start_time;

    @ApiModelProperty(value = "结束时间")
    private String sv_end_time;

    @ApiModelProperty(value = "座席工号")
    private String bridged_cno;

    @ApiModelProperty(value = "转移信息，转移后的满意度调查时有值")
    private String cdr_transfer;

    @ApiModelProperty(value = "客户按键")
    private String sv_keys;
}
