package com.welab.crm.interview.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 视频验证结果
 * <AUTHOR>
 */
@Data
public class VideoCheckResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 验证结果：
     * 0 - 非本人
     * 1 - 本人
     */
    private Boolean img_code;

    /**
     * 0 - 录音不符合预期
     * 1 - 录音符合预期
     */
    private Boolean voice_code;

    /**
     * 详细描述信息
     */
    private String msg;

    /**
     * 判定为同一人的分值，满分为1，精确到小数点后4位
     */
    private String img_score;

    /**
     * 所读内容与文本内容的相似度，满分为1，精确到小数点后4位
     */
    private String voice_score;
}
