package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 贷款详细信息响应对象
 * @date 2021/9/30
 */
@Data
@ApiModel(value = "贷款详细信息响应对象")
public class LoanDetailsVO implements Serializable {

    private static final long serialVersionUID = -8964634007455602686L;

    @ApiModelProperty(value = "客户uuid", name = "uuid")
    private Long uuid;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "申请时间", name = "applyTime")
    private Date applyTime;

    @ApiModelProperty(value = "贷款状态", name = "state")
    private String state;

    @ApiModelProperty(value = "渠道来源", name = "origin")
    private String origin;

    @ApiModelProperty(value = "审批时间", name = "approvalTime")
    private Date approvalTime;

    @ApiModelProperty(value = "申请金额", name = "applyAmount")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "审批金额", name = "approvalAmount")
    private BigDecimal approvalAmount;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    private Date disbursedTime;

    @ApiModelProperty(value = "申请期限", name = "applyTenor")
    private String applyTenor;

    @ApiModelProperty(value = "审批期限，用于绑卡查询", name = "approvalTenor")
    private String tenor;

    @ApiModelProperty(value = "确认贷款时间", name = "confirmedAt")
    private Date confirmedAt;

    @ApiModelProperty(value = "资金方", name = "partnerName")
    private String partnerName;

    @ApiModelProperty(value = "收款卡号", name = "accountNo")
    private String accountNo;

    @ApiModelProperty(value = "收款行信息", name = "bankName")
    private String bankName;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "贷款金额，用于绑卡查询", name = "amount")
    private BigDecimal amount;

    @ApiModelProperty(value = "产品编号，用于绑卡查询", name = "productCode")
    private String productCode;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "用户id，用于绑卡查询", name = "userId")
    private Long userId;

    @ApiModelProperty(value = "贷款协议服务URL", name = "loanAgreementUrl")
    private String loanAgreementUrl;

    @ApiModelProperty(value = "总利率", name = "totalRate")
    private String totalRate;

    @ApiModelProperty(value = "借据号（外部订单号）", name = "externalOrderNo")
    private String externalOrderNo;

    @ApiModelProperty(value = "投资人ID", name = "lenderId")
    private Long lenderId;

    @ApiModelProperty(value = "回购状态", name = "repurchaseStatus")
    private String repurchaseStatus;

    @ApiModelProperty(value = "催收人员id", name = "collectionStaffId")
    private String collectionStaffId;

    @ApiModelProperty(value = "催收人员姓名", name = "collectionStaffName")
    private String collectionStaffName;

    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderNo;

    @ApiModelProperty(value = "模式类型")
    private String loanModelType;
}
