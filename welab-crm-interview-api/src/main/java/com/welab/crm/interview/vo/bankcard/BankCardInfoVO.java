package com.welab.crm.interview.vo.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "银行卡信息响应对象")
public class BankCardInfoVO implements Serializable {

    private static final long serialVersionUID = -2614216485398051442L;

    @ApiModelProperty(value = "银行卡ID", name = "bankCardId")
    private String bankCardId;

    @ApiModelProperty(value = "银行", name = "bankName")
    private String bankName;

    @ApiModelProperty(value = "卡号", name = "account")
    private String account;

    @ApiModelProperty(value = "卡号", name = "account")
    private String accountEncrypt;

    @ApiModelProperty(value = "添加时间", name = "createdTime")
    private Date createdTime;

    @ApiModelProperty(value = "支付渠道（绑卡渠道）", name = "paymentChannel")
    private String paymentChannel;

    @ApiModelProperty(value = "放款机构", name = "paymentNo")
    private String paymentNo;

    @ApiModelProperty(value = "状态（绑卡状态）", name = "status")
    private String status;

    @ApiModelProperty(value = "是否已经换卡（换卡标识）", name = "renew")
    private Boolean renew;

    @ApiModelProperty(value = "快捷支付状态, 1 表示“已开通”，否则表示“未开通”", name = "quickPaymentState")
    private String quickPaymentState;

    @ApiModelProperty(value = "删除时间", name = "deletedAt")
    private Date deletedAt;

    @ApiModelProperty(value = "绑卡来源", name = "originCode")
    private String originCode;


    @ApiModelProperty(value = "是否默认卡")
    private Boolean isDefault;

    @ApiModelProperty(value = "是否可以解绑")
    private Boolean canUnbind;

}
