package com.welab.crm.interview.enums.coupon;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 卡券发送操作类型
 * @date 2022/3/10
 */
public enum CouponSendingTypeEnum {

    JGTS("jgts", "监管投诉"),
    KFRX("kfrx", "客服热线"),
    YQJTS("yqjts", "舆情聚投诉"),
    QDHZF("qdhzf", "渠道合作方"),
    ZJFTS("zjfts", "资金方投诉"),
    YQHM("yqhm", "舆情黑猫"),
    ;

    private String value;
    private String text;

    CouponSendingTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (CouponSendingTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum.getText();
            }
        }
        return null;
    }
}
