/**
 * @Title: UserQueryParam.java
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @description TODO
 *
 * <AUTHOR>
 * @date 2019-03-01 10:05:49
 * @version v1.0
 */

@Data
public class UserInfoDTO implements Serializable {
    private static final long serialVersionUID = -6496779260420493114L;

    private String name;        //姓名
    private String cnid;        //身份证
    private String mobile;      //手机号
    private Boolean blocked;    //用户状态
    private Date createdAt;     //注册时间
    private String uuid;          //uuid
    private String creditLine; //信用额度
    private String availableCredit; //可用额度

}
