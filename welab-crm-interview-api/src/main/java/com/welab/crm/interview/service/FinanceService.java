package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.vo.AssetsBoardVO;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.finance.repayment.dto.RepayChannelReq;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.finance.repayment.vo.RepayChannelVO;
import com.welab.finance.repayment.vo.UserRepaysVO;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 资金服务类
 * @date 2022/4/19 10:44
 */
public interface FinanceService {

    /**
     * 根据合同号查询用户交易记录
     * @param applicationId
     * @return
     */
    Response<List<UserRepaysVO>> queryUserPayByApplicationId(String applicationId);


    /**
     * 代扣
     *b
     * @param repaymentDTO
     * @return
     */
    Response<RepaymentVO> repayment(RepaymentDTO repaymentDTO);

    /**
     * 同步回调数据
     * @param count
     * @param date
     */
    void syncWithholdRecord(int count, Date date);

    /**
     * 查询中央监控资产看板, 只能查询当天数据
     */
    List<AssetsBoardVO> getAssetsBoard();


    /**
     * 根据合同号获取债转信息
     * @param applicationId
     * @return
     */
    WriteoffDTO getLoanTransferByApplicationId(String applicationId);

    /**
     * 获取结清证明
     * @param appNo 贷款号
     * @param partnerCode 资金方
     * @return 下载地址
     */
    String getSettlementUrl(String appNo, String partnerCode);

    /**
     * 获取可用还款方式
     * @param appNo 合同号
     * @param repayOrigin 还款来源
     * @return
     */
    Response<List<String>> getRepayMode(String appNo,String repayOrigin);

    /**
     * 获取还款通道信息
     * @param repayChannelReq
     * @return
     */
    Response<RepayChannelVO> getRepayChannel(RepayChannelReq repayChannelReq);
    
}
