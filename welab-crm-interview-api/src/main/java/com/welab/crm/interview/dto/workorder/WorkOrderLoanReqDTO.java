package com.welab.crm.interview.dto.workorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单分类请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "贷款请求对象")
public class WorkOrderLoanReqDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;
	
	/**
     * 贷款号
     */
	@ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

	/**
     * 产品名称
     */
	@ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

	/**
     * 申请时间
     */
	@ApiModelProperty(value = "申请时间", name = "applyTime")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 审批时间
     */
	@ApiModelProperty(value = "审批时间", name = "approvalTime")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;
	
	/**
     * 确认时间
     */
	@ApiModelProperty(value = "确认时间", name = "confirmTime")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;
	
	/**
     * 放款时间
     */
	@ApiModelProperty(value = "放款时间", name = "loanTime")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date loanTime;
	
	/**
     * 渠道号
     */
	@ApiModelProperty(value = "渠道号", name = "channelCode")
    private String channelCode;

	/**
     * 资金方
     */
	@ApiModelProperty(value = "资金方", name = "partnerCode")
    private String partnerCode;


	@ApiModelProperty(value = "资金方编码", name = "partnerCodeNew")
	private String partnerCodeNew;
	
	/**
     * 申请期限
     */
	@ApiModelProperty(value = "申请期限", name = "applyTenor")
    private String applyTenor;
	
	/**
     * 审批期限
     */
	@ApiModelProperty(value = "审批期限", name = "approvalTenor")
    private String approvalTenor;
	
	/**
     * 用户等级
     */
	@ApiModelProperty(value = "用户等级", name = "userLevel")
    private String userLevel;
	
	/**
     * 订单状态
     */
	@ApiModelProperty(value = "订单状态", name = "status")
    private String status;
	
	/**
     * 申请金额
     */
	@ApiModelProperty(value = "申请金额", name = "applyAmount")
    private BigDecimal applyAmount;
	
	/**
     * 审批金额
     */
	@ApiModelProperty(value = "审批金额", name = "approvalAmount")
    private BigDecimal approvalAmount;

	@ApiModelProperty(value = "总利率", name = "totalRate")
	private String totalRate;

	@ApiModelProperty(value = "债转公司")
	private String transferCompany;
}
