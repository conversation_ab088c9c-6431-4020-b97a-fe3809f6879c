package com.welab.crm.interview.enums;

public enum ReduceTaskStatusEnum {
	PROCESSING("0","处理中"),
	DONE("1","完成")
	;

	private String code;
	
	private String desc;

	ReduceTaskStatusEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
	
	public static String getDescByCode(String code){
		for (ReduceTaskStatusEnum statusEnum : values()) {
			if (statusEnum.code.equals(code)){
				return statusEnum.getDesc();
			}
		}
		
		return code;
	}
}
