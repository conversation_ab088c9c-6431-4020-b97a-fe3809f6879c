package com.welab.crm.interview.vo.message;

import lombok.Data;

import java.io.Serializable;

/**
 * 短链请求返回对象
 */
@Data
public class ShortLinkReturnVO implements Serializable {
	
	
	private Boolean success;
	
	
	private ShortLinkResult result;


	@Data
	public static class ShortLinkResult implements Serializable{

		private String id;

		private String url;

		private String deletion_url;

		private String password;

		private String expiration;
	}

}


