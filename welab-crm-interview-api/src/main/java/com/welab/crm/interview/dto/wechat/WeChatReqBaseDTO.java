package com.welab.crm.interview.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class WeChatReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 5178647889379335677L;

    /**
     * 消息类型,没特殊要求请传text
     */
    private String msgType;

    /**
     * 消息内容,文本内容,最长不超过2048个字节,必须是utf8编码
     */
    private String content;

    /**
     * userid的列表,提醒群中的指定成员(@某个成员),@all表示提醒所有人,如果开发者获取不到userid,可以使用mentioned_mobile_list
     */
    @JsonProperty("mentioned_list")
    private List<String> mentionedList;


    /**
     * 手机号列表,提醒手机号对应的群成员(@某个成员),@all表示提醒所有人,例如["1380023XXXX","@all"]
     */
    @JsonProperty(value = "mentioned_mobile_list")
    private List<String> mentionedMobileList;


    /**
     * 机器人发布地址的key值
     */
    private String key;

    /**
     * 发布链接区分，比如工单组，投诉组
     */
    private String type;
}
