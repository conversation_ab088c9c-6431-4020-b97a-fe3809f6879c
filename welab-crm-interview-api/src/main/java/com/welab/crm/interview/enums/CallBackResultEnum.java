package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 代扣回调状态枚举
 * <AUTHOR>
 * @date 2020/08/27
 */
public enum CallBackResultEnum {
    SUCCESS("0000", "成功"), FAIL("0001", "失败");
    private final String value;
    private final String text;

    private CallBackResultEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static CallBackResultEnum get(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        for (CallBackResultEnum code : values()) {
            if (code.getValue().equalsIgnoreCase(value)) {
                return code;
            }
        }
        return null;
    }
}
