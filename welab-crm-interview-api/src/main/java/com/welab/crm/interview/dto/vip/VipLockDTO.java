package com.welab.crm.interview.dto.vip;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * vip订单冻结解冻请求参数对象
 * <AUTHOR>
 */
@Data
@ApiModel("vip订单冻结解冻请求参数对象")
public class VipLockDTO implements Serializable {

    private static final long serialVersionUID = 6527039352934268026L;


    @ApiModelProperty(name = "orderNo", value = "会员订单号")
    private String orderNo;

    @ApiModelProperty(name = "uuid", value = "客户uuid")
    private String uuid;

    @ApiModelProperty(name = "refundCause", value = "退款原因")
    private String refundCause;

    @ApiModelProperty(name = "userEmail", value = "操作人邮箱")
    private String operatorEmail;


}


