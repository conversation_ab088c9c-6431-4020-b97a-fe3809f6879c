package com.welab.crm.interview.vo.repeatcall;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
@Data
@ApiModel(value = "坐席首问解决率报表响应对象")
@Excel(fileName = "坐席首问解决率报表")
public class ReportResolvedRateVO implements Serializable {

    private static final long serialVersionUID = -3207654026180487432L;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String cno;

    @ApiModelProperty(value = "坐席")
    @ExcelTitleMap(title = "坐席")
    private String staffName;

    @ApiModelProperty(value = "2小时重复来电数")
    @ExcelTitleMap(title = "2小时重复来电数")
    private Integer repeatNumber;

    @ApiModelProperty(value = "来电接起数")
    @ExcelTitleMap(title = "来电接起数")
    private Integer callInNumber;

    @ApiModelProperty(value = "首问解决率")
    @ExcelTitleMap(title = "首问解决率")
    private String resolvedRate;
}
