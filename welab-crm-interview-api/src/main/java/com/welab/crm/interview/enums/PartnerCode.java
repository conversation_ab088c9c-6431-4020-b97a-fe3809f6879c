package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 合作方枚举类
 * @date 2021/9/30
 */
public enum PartnerCode {
    SAOBEI("saobei", "扫呗"),
    DUOLABAO("duolabao", "哆啦宝"),
    TONGLIAN("tonglianbao", "通联宝");

    private String value;
    private String text;

    private PartnerCode(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getPartnerCode(String value) {
        for (PartnerCode code : values()) {
            if (code.value.equals(value)) {
                return code.getText();
            }
        }
        return null;
    }
}
