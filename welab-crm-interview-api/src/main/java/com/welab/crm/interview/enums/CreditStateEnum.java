package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 额度产品贷款状态枚举类
 * @date 2021/9/30
 */
public enum CreditStateEnum {
    APPLIED("applied", "已申请"),
    INIT_AIP("init_aip", "已初审"),
    AIP("aip", "已批准"),
    PUSHBACKED("push_backed", "退回"),
    REJECTED("rejected", "已拒绝"),
    CANCELLED("cancelled", "已取消"),
    BACK_TO_INIT("back_to_init", "已退回"),
    FREEZE("freeze", "冻结"),
    NOT_APPLIED("not_applied", "未进件");

    private String value;
    private String text;

    private CreditStateEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        for (CreditStateEnum state : values()) {
            if (state.value.equals(value)) {
                return state.getText();
            }
        }
        return "";
    }
}
