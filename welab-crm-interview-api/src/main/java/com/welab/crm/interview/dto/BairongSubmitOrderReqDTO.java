package com.welab.crm.interview.dto;

import com.welab.crm.interview.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "百融上传工单入参")
public class BairongSubmitOrderReqDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "唯一工单编号,字母跟数字组成,50个字符以内")
	@NotBlank(message = "工单编号不能为空")
	private String uniqueOrderNo;
	
	@ApiModelProperty(value = "提现订单号")
	private List<String> loanOrderId;

	@ApiModelProperty(value = "uid")
	private List<String> uid;
	
	@ApiModelProperty(value = "是否加急")
	private boolean urgeFlag;
	
	@ApiModelProperty(value = "问题类型")
	@NotBlank(message = "问题类型不能为空")
	private String questionType;
	
	@ApiModelProperty(value = "工单内容")
	@NotBlank(message = "工单内容不能为空")
	private String content;
	
	@ApiModelProperty(value = "附件下载链接列表")
	private List<WoAttachmentVO> attachmentUrls;
}
