package com.welab.crm.interview.vo.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "渠道信息响应对象")
public class ChannelInfoVO implements Serializable {

    private static final long serialVersionUID = 2694467199498177806L;

    @ApiModelProperty(value = "支付渠道", name = "paymentChannel")
    private String paymentChannel;

    @ApiModelProperty(value = "现金余额", name = "cashBalance")
    private BigDecimal cashBalance;

    @ApiModelProperty(value = "冻结余额", name = "freezeBalance")
    private BigDecimal freezeBalance;

    @ApiModelProperty(value = "奖励余额", name = "rewardBalance")
    private BigDecimal rewardBalance;
}
