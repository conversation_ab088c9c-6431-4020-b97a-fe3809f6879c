package com.welab.crm.interview.vo.monitor;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 接口请求记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
public class OpHttpLogRecordByPhoneSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户登陆名称
     */
    private String loginName;

    /**
     * 查询用户uuid
     */
    private String uuid;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * 组别
     */
    private String groupCode;

    /**
     * 姓名
     */
    private String staffName;

}
