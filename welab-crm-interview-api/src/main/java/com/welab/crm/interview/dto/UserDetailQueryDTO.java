package com.welab.crm.interview.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息查询DTO
 *
 * <AUTHOR>
 * @date 2021/9/27 14:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDetailQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * uuid
     */
    private Long uuid;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 身份证号
     */
    private String cnid;

    /**
     *  操作类型：PAY-QIANBAO-APP
     */
    private String type;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 银行卡号
     */
    private String accountNo;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    /**
     * 外部订单号(借据号)
     */
    private String externalOrderNo;
}
