package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 现金贷还款项目枚举
 * @date 2021/11/17
 */
public enum RepaymentPlanType {
    INTEREST("interest", "利息"),
    MANAGEMENT_FEE("management_fee", "管理费"),
    WITHDRAWAL_FEE("withdrawal_fee", "手续费"),
    DEPOSIT_FEE("deposit_fee", "保证金"),
    HANDLING_FEE("handling_fee", "审批费"),
    PRINCIPAL("principal", "本金"),
    OVERDUE_MANAGEMENT_FEE("overdue_management_fee", "逾期管理费"),
    OVERDUE_INTEREST("overdue_interest", "逾期利息"),
    OUTSOURCING_FEE("outsourcing_fee", "委外费"),
    GUARANTEE_FEE("guarantee_fee", "担保费"),
    INSURANCE_FEE("insurance_fee", "保费"),
    GUARANTEE_CONSULTING_FEE("guarantee_consulting_fee", "担保咨询费"),
    RISK_SERVICE_FEE("risk_service_fee", "风险服务费"),
    RISK_MARGIN("risk_margin", "风险保证金"),
    OVERDUE_PENALTY("overdue_penalty", "逾期罚金");

    private String value;
    private String text;

    RepaymentPlanType(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        for (RepaymentPlanType type : values()) {
            if (type.getValue().equals(value)) {
                return type.getText();
            }
        }
        return "其他";
    }
}
