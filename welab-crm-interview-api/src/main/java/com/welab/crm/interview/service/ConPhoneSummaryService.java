package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.ConPhoneSummaryDTO;
import com.welab.crm.interview.vo.online.OnlineHistoryModel;

import java.util.List;

/**
 * 电话小结同步数据服务
 */
public interface ConPhoneSummaryService {

    /**
     * 保存或者更新呼入电话小结
     */
    void savePhoneSummary(ConPhoneSummaryDTO summaryDTO);


    /**
     * 查询在线系统的客户联系记录
     * @param userId 用户id
     * @return
     */
    List<OnlineHistoryModel> queryOnlineHistoryContactRecord(Integer userId);
}
