package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.dto.message.MessageTemplateAddDTO;
import com.welab.crm.interview.dto.message.MessageTemplateQueryDTO;
import com.welab.crm.interview.dto.message.MessageTemplateUpdateDTO;
import com.welab.crm.interview.vo.message.MessageHistoryVO;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.xdao.context.page.Page;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 短信服务
 * @date 2021/12/8
 */
public interface MessageService {

    /**
     * 添加短信模板
     *
     * @param dto
     */
    Long addMessageTemplate(MessageTemplateAddDTO dto);

    /**
     * 修改短信模板
     *
     * @param dto
     */
    void updateMessageTemplate(MessageTemplateUpdateDTO dto);

    /**
     * 删除短信模板
     *
     * @param id
     */
    void deleteMessageTemplate(Long id);

    /**
     * 分页查询短信模板
     *
     * @param dto
     * @return
     */
    Page<MessageTemplateVO> getMessageTemplateList(MessageTemplateQueryDTO dto);


    /**
     * 置顶短信模板
     * @param templateId 主键id
     */
    void topMessageTemplate(Long templateId);

    /**
     * 发送短信，保存至历史记录，等待定时任务执行
     *
     * @param dto
     */
    String sendMessage(MessageSendDTO dto);

    /**
     * 查询短信发送历史
     *
     * @param mobile 客户手机号
     * @param customerId 客户Id
     * @return
     */
    List<MessageHistoryVO> getMessageHistory(String mobile, Long customerId);

    /**
     * 通过手机号查询所有人脸验证核验记录
     */
    ResponseVO<List<ResponseData>> listFaceValidateRecords(String mobile);
    

}
