package com.welab.crm.interview.enums;

/**
 * 创研调用的人脸验证供应商
 */
public enum ValidFaceVendorEnum {

    WELAB_AI("WELAB-AI", "天冕人工智能"),
    TENCENT("tencent", "微众"),
    FACE_ID("faceID", "旷视");

    ValidFaceVendorEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private final String value;
    private final String desc;

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByValue(String value) {
        for (ValidFaceVendorEnum type : values()) {
            if (type.value.equals(value)) {
                return type.desc;
            }
        }
        return value;
    }
}
