/*
 * @Title: AiCallBackDTO.java
 * @Copyright: © 2022 我来贷
 * @Company: 深圳卫盈联有限公司
 */

package com.welab.crm.interview.dto.callback;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ai外呼回调接收参数对象
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022-03-18 13:35:17
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AiCallBackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商名称
     */
    @JsonProperty(value = "channelName")
    private String channelName;

    /**
     * 手机号
     */
    @JsonProperty(value = "phoneNo")
    private String phoneNo;

    /**
     * 第*次数呼叫
     */
    @JsonProperty(value = "callNum")
    private String callNum;

    /**
     * 外呼开始时间
     */
    @JsonProperty(value = "callTime")
    private String callTime;

    /**
     * 外呼接通时间
     */
    @JsonProperty(value = "answerTime")
    private String answerTime;

    /**
     * 结束时间
     */
    @JsonProperty(value = "endTime")
    private String endTime;

    /**
     * 通话时长
     */
    @JsonProperty(value = "billSec")
    private Integer billSec;

    /**
     * 号码状态: 正在通话中/呼通
     */
    @JsonProperty(value = "phoneStatus")
    private String phoneStatus;

    /**
     * 客户意愿
     */
    @JsonProperty(value = "willingCol")
    private String willingCol;

    /**
     * 系统外呼执行状态(同盾)   系统外呼执行完毕
     */
    @JsonProperty(value = "sysCallStatus")
    private String sysCallStatus;

    /**
     * 业务日期
     */
    @JsonProperty(value = "businessDate")
    private String businessDate;

    /**
     * 录音地址
     */
    @JsonProperty(value = "recordUrl")
    private String recordUrl;

    /**
     * 导入时间
     */
    @JsonProperty(value = "importTime")
    private String importTime;

    /**
     * 回调返回的值(与本服务传输过去的值是相同的)
     */
    @JsonProperty(value = "extraParam")
    private String extraParam;
}
