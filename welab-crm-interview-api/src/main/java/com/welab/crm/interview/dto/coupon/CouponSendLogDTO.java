package com.welab.crm.interview.dto.coupon;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/18
 */
@Data
@ApiModel(value = "查询卡券发送记录请求对象")
public class CouponSendLogDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = -6716393735991345070L;

    @ApiModelProperty(value = "开始时间, 格式为yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间, 格式为yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "业务类型, loan 工薪贷, wallet 钱包")
    private String businessType;

    @ApiModelProperty(value = "卡劵ID")
    private Long couponId;

    @ApiModelProperty(value = "卡劵类型")
    private String amountType;

    @ApiModelProperty(value = "活动名称")
    private String description;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "发送人")
    private String staffId;

    @ApiModelProperty(value = "发送组")
    private String groupCode;

    @ApiModelProperty(value = "发送组列表")
    private List<String> groupCodeList;
}
