package com.welab.crm.interview.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Data
@ApiModel(value = "用户红包赠送记录响应对象")
public class CouponVO implements Serializable {

    private static final long serialVersionUID = 6497985301294603071L;

    @ApiModelProperty(value = "红包ID")
    private String couponUserId;

    @ApiModelProperty(value = "类型")
    private String amountType;

    @ApiModelProperty(value = "额度")
    private BigDecimal amount;

    @ApiModelProperty(value = "有效期")
    private Date expiredTime;

    @ApiModelProperty(value = "使用时间")
    private Date usedTime;

    @ApiModelProperty(value = "使用条件")
    private String useCondition;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "领取时间")
    private Date beginAt;

    @ApiModelProperty(value = "活动名称")
    private String description;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "当期期数（使用期数）")
    private Integer currentTenor;

    @ApiModelProperty(value = "贷款金额")
    private BigDecimal loanAmount;

}
