package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 进件产品贷款状态枚举类
 * @date 2021/9/30
 */
public enum LoanStateEnum {

    INIT_APPROVAL("init_aip", "初审"),
    APPLIED("applied", "已申请"),
    PRETRIAL_APPLIED("PRETRIAL_APPLIED", "预审"),
    ;

    private String value;
    private String text;

    LoanStateEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return text;
    }

    public static LoanStateEnum getByValue(String value) {
        for (LoanStateEnum loanStateEnum : values()) {
            if (loanStateEnum.getValue().equals(value)) {
                return loanStateEnum;
            }
        }
        return null;
    }
}
