package com.welab.crm.interview.vo;

import com.welab.crm.interview.dto.LoanInfoDTO;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 贷款信息视图
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoanInfoVo implements Serializable {
	private static final long serialVersionUID = -1516428986103134874L;
	private Integer flag;						//查询结果状态值
	private List<LoanInfoDTO> LoanInfo;
	private String uuid;

}
