package com.welab.crm.interview.vo.partnerInfo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 资金方信息返回对象
 */
@Data
public class PartnerInfoVO {

	/**
	 * 主键
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "主键")
	private Long id;

	/**
	 * 机构号
	 */
	@ExcelProperty(value = "机构号")
	@ApiModelProperty(value = "机构号")
	private String institutionNumber;

	/**
	 * 资金方名称
	 */
	@ExcelProperty(value = "资金方名称")
	@ApiModelProperty(value = "资金方名称")
	private String partnerName;


	/**
	 * 放款卡
	 */
	@ExcelProperty(value = "放款卡")
	@ApiModelProperty(value = "放款卡")
	private String loanCard;

	/**
	 * 系统批扣时间-还款日
	 */
	@ExcelProperty(value = "系统批扣时间")
	@ApiModelProperty(value = "系统批扣时间")
	private String systemRepayTimeNormal;

	/**
	 * 宽限期
	 */
	@ExcelProperty(value = "宽限期")
	@ApiModelProperty(value = "宽限期")
	private String gracePeriod;

	/**
	 * h5还款
	 */
	@ExcelProperty(value = "H5还款")
	@ApiModelProperty(value = "H5还款")
	private String h5Repay;

	/**
	 * 一期一期提前还款/账单展示
	 */
	@ExcelProperty(value = "一期一期提前还款/账单展示")
	@ApiModelProperty(value = "一期一期提前还款/账单展示")
	private String earlyRepayEachInstallment;

	/**
	 * 提前结清说明
	 */
	@ExcelProperty(value = "提前结清说明")
	@ApiModelProperty(value = "提前结清说明")
	private String earlySettlementMsg;

	/**
	 * 结清证明
	 */
	@ExcelProperty(value = "结清证明")
	@ApiModelProperty(value = "结清证明")
	private String settlementProof;
}
