package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.dto.wallet.WalletBillQueryDTO;
import com.welab.crm.interview.dto.wallet.WalletOrderRecordDTO;
import com.welab.crm.interview.dto.wechat.WalletLoanDTO;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.interview.vo.wallet.WalletGrantHistoryVO;
import com.welab.crm.interview.vo.wallet.WalletGrantQuotaVO;
import com.welab.crm.interview.vo.wallet.WalletLoanDueInfoVO;
import com.welab.crm.interview.vo.wallet.WalletMonthBillVO;
import com.welab.crm.interview.vo.wallet.WalletOrderRecordVO;
import com.welab.crm.interview.vo.wallet.WalletOutstandingVO;
import com.welab.crm.interview.vo.wallet.WalletRepayRecordVO;
import com.welab.support.credit.dto.cs.GetGrantHistoryReq;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 钱包服务
 * @date 2021/10/28
 */
public interface WalletService {

    /**
     * 获得用户钱包分期模式
     *
     * @param userId
     * @param month
     * @return
     */
    WalletLoanDueInfoVO getWalletLoanDue(Integer userId, String month);

    /**
     * 获得钱包月账单
     *
     * @param queryDTO
     * @return
     */
    List<WalletMonthBillVO> getMonthBill(WalletBillQueryDTO queryDTO);

    /**
     * 获得钱包交易记录
     *
     * @param dto
     * @return
     */
    List<WalletOrderRecordVO> getWalletOrderRecords(WalletOrderRecordDTO dto);

    /**
     * 获得钱包还款记录
     *
     * @param userId
     * @return
     */
    List<WalletRepayRecordVO> getWalletRepayRecords(Long userId);

    /**
     * 获得授信申请信息
     *
     * @param uuid
     * @return
     */
    List<WalletGrantQuotaVO> getGrantQuotaInfo(Long uuid);

    /**
     * 获取额度授信历史
     *
     * @param req
     * @return
     */
    List<WalletGrantHistoryVO> getGrantQuotaHistory(GetGrantHistoryReq req);

    /**
     * 钱包提前结清
     *
     * @param userId
     * @param applicationId
     */
    void earlyRepay(Long userId, String applicationId);

    /**
     * 开启支持提前结清
     *
     * @param applicationId
     */
    boolean updateLoanYBStatus(String applicationId);

    /**
     * 批量开启支持提前结清
     *
     * @param applicationIdList
     * @return 开启失败的订单号
     */
    List<String> allowEarlyRepayBatch(List<String> applicationIdList);

    /**
     * 查询在途贷款列表
     *
     * @param userId
     * @return
     */
    List<WalletOutstandingVO> getOutstandingLoanList(Integer userId);


    /**
     * 获取分期钱包还款计划
     * @param applicationId
     * @return
     */
    WalletLoanDTO getWalletRepayPlan(String applicationId);


    /**
     * 钱包代扣
     *b
     * @param repaymentDTO
     * @return
     */
    Response<RepaymentVO> repayment(RepaymentDTO repaymentDTO);
}
