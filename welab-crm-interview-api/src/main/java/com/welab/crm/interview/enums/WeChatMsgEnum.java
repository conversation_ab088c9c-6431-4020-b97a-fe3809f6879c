package com.welab.crm.interview.enums;

/**
 * 企业微信的消息类型枚举
 */
public enum WeChatMsgEnum {

    TEXT("text", "文本类型"),
    MARKDOWN("markdown", "markdown类型"),
    NEWS("news", "图文类型"),
    FILE("file", "文件类型"),
    TEMPLATE_CARD("template_card", "文本卡片类型"),
    IMAGE("image", "图片类型");

    private final String value;

    private final String desc;

    WeChatMsgEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}

