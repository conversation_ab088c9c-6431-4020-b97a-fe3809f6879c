package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "钱包在途贷款响应对象")
public class WalletOutstandingVO implements Serializable {

    private static final long serialVersionUID = -6123654053363043409L;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "贷款类型")
    private String loanType;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "转贷时间")
    private String transLoanDate;

    @ApiModelProperty(value = "支付渠道")
    private String paymentChannel;

    @ApiModelProperty(value = "资金方")
    private String partnerName;

    @ApiModelProperty(value = "期数，剩余期数除以总期数")
    private String tenor;

    @ApiModelProperty(value = "贷款金额（本金）")
    private BigDecimal amount;

    @ApiModelProperty(value = "总利率")
    private BigDecimal totalRate;

    @ApiModelProperty(value = "贷款余额")
    private BigDecimal loanBalance;

    @ApiModelProperty(value = "剩余期数")
    private String lastTenor;

    @ApiModelProperty(value = "到期日期(还款日)")
    private Date deadDate;

    @ApiModelProperty(value = "是否支持提前结清")
    private String isAllowEarlySettle;


    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;


    @ApiModelProperty(value = "贷款状态", name = "state")
    private String state;

    @ApiModelProperty(value = "渠道号", name = "origin")
    private String origin;

    @ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    private Date disbursedTime;

    @ApiModelProperty(value = "逾期天数", name = "overdueDay")
    private Integer overdueDay;

    /**
     * 名单类型（0:委外,1:债转,2-法诉)
     */
    @ApiModelProperty(value = "名单标签", name = "type")
    private String type;
    
    @ApiModelProperty(value = "公司名称", name = "companyName")
    private String companyName;
    
    @ApiModelProperty(value = "公司电话", name = "companyTel")
    private String companyTel;
    
    @ApiModelProperty(value = "债转时间", name = "deptDate")
    private Date deptDate;

    @ApiModelProperty(value = "法诉时间范围", name = "timeRange")
    private String timeRange;
}
