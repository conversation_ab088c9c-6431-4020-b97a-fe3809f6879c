package com.welab.crm.interview.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Lyman on 2017/1/12.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoanInfoDTO implements Serializable {
    private static final long serialVersionUID = -5178647889379335509L;


    private String uuid;
    private String applicationId;           //贷款号
    private String productCode;             //产品编号
    private String productName;             //产品名称
    private String name;                    //申请人名称
    private String mobile;                  //手机号
    private String company;                 //公司名称
    private BigDecimal approvalAmount;      //审批金额
    private String approvalTenor;           //审批期限
    private String status;                  //状态
    private Date applyTime;                 //申请时间
    private BigDecimal applyAmount;         //申请金额
    private Date approvalTime;              //审批时间
    private String applyTenor;              //申请期限
    private String applyPlatform;           //申请平台
    private Date disbursedTime;             //放款时间
    private String origin;                  //贷款来源
	private Boolean blocked;                //用户状态

    private String approver;				//审批员
    private Integer urgent;					//是否加急
    private BigDecimal amount;				//审批金额
    private Date approvedAt;				//审批时间
    private Integer borrowerId; 		    //用户Id

    private String partnerCode;             //资金方编码

}
