package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 */
@Data
@ApiModel(value = "钱包项目用户分期模式响应对象")
public class WalletLoanDueInfoVO implements Serializable {

    private static final long serialVersionUID = -2014885137964773308L;

    @ApiModelProperty(value = "账单列表")
    List<WalletLoanDueVO> list;

    @ApiModelProperty(value = "总应还金额")
    private BigDecimal sumFeeAmount;

    @ApiModelProperty(value = "总已还金额")
    private BigDecimal sumSettledAmount;

    @ApiModelProperty(value = "总未还金额")
    private BigDecimal sumOutstandingAmount;

    @ApiModelProperty(value = "全额结清总金额")
    private BigDecimal sumSettleAllAmount;

}
