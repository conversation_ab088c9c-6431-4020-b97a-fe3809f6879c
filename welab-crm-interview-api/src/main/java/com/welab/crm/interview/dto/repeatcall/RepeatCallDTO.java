package com.welab.crm.interview.dto.repeatcall;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/22
 */
@Data
@ApiModel(value = "坐席首次问题解决率报表请求对象")
public class RepeatCallDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = -3207654026180487432L;

    @ApiModelProperty(value = "开始时间，格式 yyyy-MM-dd HH:mm:ss")
    @NotBlank
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    @NotBlank
    private String endTime;

    @ApiModelProperty(value = "热线号码, 多个以英文逗号分隔")
    @NotBlank
    private String hotline;

    @ApiModelProperty(value = "周期",example = "day,range")
    private String period;

    @ApiModelProperty(value = "坐席工号列表")
    private List<String> cnos;
}
