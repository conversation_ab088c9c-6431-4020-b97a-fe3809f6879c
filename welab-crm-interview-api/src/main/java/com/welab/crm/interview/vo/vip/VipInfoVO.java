package com.welab.crm.interview.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/15
 */
@Data
@ApiModel(value = "会员权益信息响应对象")
public class VipInfoVO implements Serializable {

    private static final long serialVersionUID = -8108166428423424170L;

    @ApiModelProperty(value = "用户uuid")
    private Long uuid;

    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名")
    private String name;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    /**
     * 会员开通订单号
     */
    @ApiModelProperty(value = "我司订单号")
    private String orderNo;

    /**
     * 会员供应商订单号
     */
    @ApiModelProperty(value = "会员供应商订单号")
    private String supplierOrderNo;

    /**
     * 交易渠道名称
     */
    @ApiModelProperty(value = "交易渠道名称")
    private String channelName;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal paymentAmount;

    /**
     * 卫盈联红包金额
     */
    @ApiModelProperty(value = "卫盈联红包金额")
    BigDecimal wylCouponAmount;

    /**
     * 横琴红包金额
     */
    @ApiModelProperty(value = "横琴红包金额")
    BigDecimal hqCouopnAmount;

    /**
     * 会员生活权益已产生成本 提供给客服和财务核实权益使用 通过会员供应商API获取
     */
    @ApiModelProperty(value = "会员生活权益已产生成本")
    private BigDecimal benefitTotalCost;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date paymentAt;

    /**
     * 支付状态
     *
     * @see com.welab.crm.interview.enums.VipStatus.PaymentStatusEnum
     */
    @ApiModelProperty(value = "支付状态")
    private String paymentStatus;

    /**
     * 退款状态
     *
     * @see com.welab.crm.interview.enums.VipStatus.RefundStatusEnum
     */
    @ApiModelProperty(value = "退款状态")
    private String refundStatus;

    /**
     * 冻结状态
     * 正常：false、已冻结：true
     */
    @ApiModelProperty(value = "冻结状态 正常：false、已冻结：true")
    private Boolean frozeState;

    /**
     * 退款时间
     */
    @ApiModelProperty(value = "退款时间")
    private Date refundAt;

    @ApiModelProperty(value = "冻结时间")
    private Date frozeAt;

    @ApiModelProperty(value = "提额金额")
    private BigDecimal quotaBenefitAmount;

    @ApiModelProperty(value = "退款原因")
    private String refundCause;

    @ApiModelProperty(value = "购买渠道")
    private String source;

    @ApiModelProperty(value = "支付响应编码")
    private String payCode;

    @ApiModelProperty(value = "支付响应文案")
    private String payMsg;

    @ApiModelProperty(value = "会员付费模式，例如：先享后付、先付")
    private String memberPayMode;

    @ApiModelProperty(value = "订单退款失败原因，该字段目前仅通过单笔支付订单号查询时返回")
    private String refundFailedCause;

    @ApiModelProperty(value = "支付方式")
    private String paymentMode;


    @ApiModelProperty(value = "供应商")
    private String supplierCode;

    @ApiModelProperty(value = "拒就赔抽奖红包")
    private BigDecimal jjpCouponAmount;

    @ApiModelProperty(value = "底色")
    private String backgroundColor;

    /**
     * 会员类型
     */
    @ApiModelProperty(value = "会员类型")
    private String category;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String assetCode;
}
