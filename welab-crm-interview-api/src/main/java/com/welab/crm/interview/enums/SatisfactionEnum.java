package com.welab.crm.interview.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 满意度枚举
 * <AUTHOR>
 */
public enum SatisfactionEnum {
    VERY_SATISFIED("1", "非常满意"), SATISFIED("2", "满意"), NORMAL("3", "一般"), DISSATISFIED("4", "不满意"),
    VERY_DISSATISFIED("5", "非常不满意");

    private String code;

    private String desc;

    SatisfactionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 满意
     */
    public static final List<String> SATISFIED_CODE_LIST =
        Collections.unmodifiableList(Arrays.asList(SATISFIED.code, VERY_SATISFIED.code));
    
    
    public static String getDescByCode(String code){
        for (SatisfactionEnum satisfactionEnum : values()) {
            if (satisfactionEnum.code.equals(code)){
                return satisfactionEnum.desc;
            }
        }
        
        return code;
    }

    /**
     * 不满意原因枚举
     */
    public enum DissatisfactionReasonEnum {
        NOT_FAMILIAR_WITH_BIZ("1","客服业务不熟悉"),
        LONG_PROCESSING_TIME("2","处理时效长"),
        POOR_SERVICE_ATTITUDE("3","服务态度差"),
        DISCONTENT_WITH_BIZ_TYPE("4","业务规则不满")
        ;
        private String code;
        
        private String reason;

        DissatisfactionReasonEnum(String code, String reason) {
            this.code = code;
            this.reason = reason;
        }
        
        public static String getReasonByCode(String code){
            for (DissatisfactionReasonEnum reasonEnum : values()) {
                if (reasonEnum.code.equals(code)){
                    return reasonEnum.reason;
                }
            }
            return code;
        }
    }
}
