package com.welab.crm.interview.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * 返回给创研客服网聊的用户信息字段
 * <AUTHOR>
 * @date 2022/1/11 18:09
 */
@Data
public class UserInfoForChat implements Serializable {

    private static final long serialVersionUID = -1516428986103134874L;

    private String name;
    private String cnid;
    private String mobile;
    private String uuid;
    private String gender;
    private Integer userId;
    /**
     * 运营商
     */
    private String operator;
    /**
     * 是否vip用户
     */
    private String isVip;
    /**
     * 会员开通日期
     */
    private String cardCreatedAt;

    /**
     * 会员失效日期
     */
    private String cardExpiryAt;
    /**
     * 注册时间
     */
    private String registerAt;

    /**
     * 用户是否注销
     */
    private Boolean block;


    /**
     * 是否逾期
     */
    private Boolean isOverdue;


    /**
     * 逾期天数
     */
    private Integer overdueDays;

}
