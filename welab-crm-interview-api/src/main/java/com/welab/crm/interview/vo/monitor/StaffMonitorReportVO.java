package com.welab.crm.interview.vo.monitor;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户监控报表返回对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "用户监控报表返回对象")
public class StaffMonitorReportVO {

    @ApiModelProperty(value = "查询时间")
    @ExcelProperty("时间")
    private String queryDate;

    @ApiModelProperty(value = "组别")
    @ExcelProperty("组别")
    private String groupCode;
    
    private String loginName;

    @ApiModelProperty(value = "姓名")
    @ExcelProperty("姓名")
    private String staffName;

    @ApiModelProperty(value = "客户资料查询")
    @ExcelProperty("客户资料查询")
    private String userDetailQueryCount;

    @ApiModelProperty(value = "去重客户量")
    @ExcelProperty("去重客户量")
    private String queryUserCount;

    @ApiModelProperty(value = "明文号码查询")
    @ExcelProperty("明文号码查询")
    private String mobileDecodeCount;

    @ApiModelProperty(value = "贷款协议查询")
    @ExcelProperty("贷款协议查询")
    private String loanAgreementQueryCount;

    @ApiModelProperty(value = "录音保存量")
    @ExcelProperty("录音保存量")
    private String soundQueryCount;
    
    
}
