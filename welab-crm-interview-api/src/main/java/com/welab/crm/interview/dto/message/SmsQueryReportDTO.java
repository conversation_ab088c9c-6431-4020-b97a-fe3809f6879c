package com.welab.crm.interview.dto.message;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/16
 */
@Data
@ApiModel(value = "查询短信平台报告请求对象")
public class SmsQueryReportDTO implements Serializable {

    private static final long serialVersionUID = 6705536808630712705L;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户ID
     * 由"来源渠道"+"_"+"系统名称"组成
     *
     * "来源渠道" {@link} com.welab.message.sms.enums.SourceChannel
     */
    private String userId;
    /**
     * 时间戳到秒
     */
    private String reqTime;
    /**
     * 签名
     *
     * 计算方式采用md5 32位大写加密
     *  MD5.encrypt(mobile+ secretkey+reqTime).toUpperCase()
     *
     * 如mobile="13612342589";secretkey="abc123";reqTime="13955284623";
     * 计算得出sign="4C2764F394821B807FCFD975D03993A3"
     */
    private String sign;
    /**
     * 发送短信bean
     */
    private String rspMsgid;
}
