package com.welab.crm.interview.vo.coupon;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/18
 */
@Data
@ApiModel(value = "用户可赠送红包响应对象")
@Excel(fileName = "卡券全局监控")
public class CouponCardVO implements Serializable {

    private static final long serialVersionUID = -4204087696186103850L;

    @ApiModelProperty(value = "创建时间，有效期为空的时候拼接上红包卡券开始时间和过期时间")
    @ExcelTitleMap(title = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "卡劵ID")
    @ExcelTitleMap(title = "卡劵ID")
    private Long id;

    @ApiModelProperty(value = "卡劵类型")
    @ExcelTitleMap(title = "卡劵类型")
    private String amountType;

    @ApiModelProperty(value = "卡劵类型原始值(未转换)")
    @ExcelTitleMap(title = "卡劵类型原始值(未转换)")
    private String amountTypeOrigin;

    @ApiModelProperty(value = "面值")
    @ExcelTitleMap(title = "面值")
    private Float amount;

    @ApiModelProperty(value = "有效期(单位：天)")
    @ExcelTitleMap(title = "有效期(单位：天)")
    private Integer availableDays;

    @ApiModelProperty(value = "总量")
    @ExcelTitleMap(title = "卡券数量")
    private Integer total;

    @ApiModelProperty(value = "已用数量")
    @ExcelTitleMap(title = "发送数量")
    private Integer used;

    @ApiModelProperty(value = "可用数量")
    @ExcelTitleMap(title = "可用数量")
    private Integer available;

    @ApiModelProperty(value = "可用占比")
    @ExcelTitleMap(title = "可用占比")
    private String availableRate;

    @ApiModelProperty(value = "条件说明")
    private String name;

    @ApiModelProperty(value = "红包名称")
    private String description;

    @ApiModelProperty(value = "过期时间")
    private Date expiredAt;
}
