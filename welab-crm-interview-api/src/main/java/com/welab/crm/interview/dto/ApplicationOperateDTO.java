package com.welab.crm.interview.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * 贷款操作DTO
 * <AUTHOR>
 * @date 2021/9/29 15:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationOperateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 操作类型，漏接电话：1  退回订单：2  取消订单：3  调低额度：4   修改期数：5
     */
    private String type;

    /**
     * 备注
     */
    private String comment;

    /**
     * 客服手机号
     */
    private String custServiceMobile;

    /**
     * 客服姓名
     */
    private String staffName;


    private String adminId;
}
