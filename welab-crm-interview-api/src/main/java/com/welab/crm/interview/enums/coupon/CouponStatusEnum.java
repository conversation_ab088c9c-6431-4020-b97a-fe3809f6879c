package com.welab.crm.interview.enums.coupon;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 卡券状态枚举类
 * @date 2021/10/28
 */
public enum CouponStatusEnum {

    EXPIRED("expired", "到期"),
    ENABLED("enabled", "可用"),
    USED_NOT_WITHDRAWN("used_not_withdrawn", "已用未提现"),
    USED_WITHDRAWN("used_withdrawn", "已用已提现"),
    USED_LOCK("used_lock", "已用锁定中"),
    DISABLED("disabled", "不可用");

    private String value;
    private String text;

    CouponStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
