package com.welab.crm.interview.vo.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/10
 */
@Data
@ApiModel(value = "短信发送历史响应对象")
public class MessageHistoryVO implements Serializable {

    private static final long serialVersionUID = -7015559135114010102L;

    @ApiModelProperty(value = "唯一id，供前端使用")
    private String id;

    @ApiModelProperty(value = "短信内容")
    private String context;

    @ApiModelProperty(value = "接收手机号")
    private String mobile;

    @ApiModelProperty(value = "发送状态")
    private String sendStatus;

    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "实际发送时间")
    private Date actualSendTime;

    @ApiModelProperty(value = "发送人")
    private String staffName;

    @ApiModelProperty(value = "客户是否收到短信")
    private String received;

    @ApiModelProperty(value = "还款方式")
    private String repayMethod;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        MessageHistoryVO that = (MessageHistoryVO) o;

        return new EqualsBuilder()
                .append(context, that.context)
                .append(mobile, that.mobile)
                .append(sendTime, that.sendTime)
                .append(staffName, that.staffName)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(context)
                .append(mobile)
                .append(sendTime)
                .append(staffName)
                .toHashCode();
    }
}
