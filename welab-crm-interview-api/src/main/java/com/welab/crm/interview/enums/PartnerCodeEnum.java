package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 */

public enum PartnerCodeEnum {

	ZABX("zabx", "众安保险","众安"),
	ZHONGAN("zhongan", "众安","众安"),
	PPMONEY("ppmoney", "PPMoney","PPMoney"),
	JCBX("jcbx", "锦程保险","锦程"),
	JINCHENG("jincheng", "锦程","锦程"),
	FENGHUANG("fenghuang", "凤凰金融","凤凰金融"),
	ZTSSJ("ztssj", "随手记","随手记"),
	KAOLALICAI("kaolalicai", "考拉理财","考拉理财"),
	ANXIN("anxin", "安信","安信"),
	FSK("fsk", "富士康","富士康"),
	YNTRUST("yntrust", "云南信托","云南信托"),
	ZHENRONGBAO("zhenrongbao", "真融宝","真融宝"),
	HZFI("hzfi", "金投","金投"),
	DINGDONG("dingdong", "叮咚钱包","叮咚钱包"),
	JCFC("jcfc", "晋商银行","晋商银行"),
	FACEBANK("facebank", "笑脸","笑脸"),
	FCS("fcs", "富登","富登"),
	WANGSHANG("wangshang", "网商小贷","网商小贷"),
	TONGBANJIE("tongbanjie", "铜板街","铜板街"),
	JINMEIXIN("jinmeixin", "金美信","金美信"),
	YOOLI("yooli", "有利网","有利网"),
	HQXY("hqxy", "横琴（售后回租）","横琴"),
	HENGQIN_ALLINPAY("hengqin-allinpay", "横琴（自营）","横琴"),
	HENGQIN_ALIYUN("hengqin-aliyun", "横琴（支付宝）","横琴"),
	WACAI("wacai", "挖财","挖财"),
	YNTRUST1("yntrust1", "云南信托-新信托计划","云南信托"),
	YNTRUST2("yntrust2", "云南信托2-增信担保模式","云南信托"),
	YNTRUST3("yntrust3", "云南信托3-普惠570信托","云南信托"),
	XCWS("xcws", "携程网商","携程网商"),
	EROAD("eroad", "E路同心","E路同心"),
	JIMU("jimu", "积木盒子","积木盒子"),
	CYCFC("cycfc","长银消费金额","长银"),
	YIQIANDAI("yiqiandai", "亿钱贷","亿钱贷"),
	HUBEICFC("hubeicfc", "湖北消金","湖北消金"),
	HUBEICFC1("hubeicfc1", "湖北消金1","湖北消金"),
	XZTRUST("xztrust","西藏信托","西藏信托"),
	XMZBBANK("xmzbbank","小米众邦银行","众邦银行"),
	XMJMX("xmjmx", "小米金美信","金美信"),
	ZBBANK("zbbank", "众邦银行","众邦银行"),
	NPA("npa", "不良资产资方",""),
	JMXCFC("jmxcfc", "金美信-担保模式","金美信"),
	HAIERCFC("haiercfc", "海尔消金","海尔消金"),
	HAIERMONEY("haiermoney", "海尔云贷","海尔云贷"),
	CZCB("czcb", "稠州银行","稠州银行"),
	CYCFC1("cycfc1", "长银消金二期","长银消金"),
	WEISHENMA("weishenma", "微神马irr36","微神马"),
	WEISHENMA1("weishenma1", "微神马apr36","微神马"),
	HENGQIN_JXZ("hengqin-jxz", "横琴（京小租）","横琴"),
	HAIERCFC1("haiercfc1", "海尔消金（IRR36）","海尔消金"),
	SDTRUST("sdtrust", "山东信托","山东信托"),
	GMTRUST("gmtrust", "国民信托","国民信托"),
	GMTRUST2("gmtrust2", "国民信托","国民信托-分期"),
	BOBCFC("bobcfc", "北银消金","北银消金"),
	FUXING("fuxing","复星小贷","复星小贷"),
	JCDB("jcdb", "锦程担保","锦程"),
	WSXMTRUST("wsxmtrust", "网商厦门信托","网商"),
	XMTRUST("xmtrust", "厦门信托二号绑卡渠道","网商"),
	ZBBANK1("zbbank1", "众邦银行(非持牌担保)","众邦银行"),
	JINTAI("jintai", "锦泰","锦泰"),
	SDTRUST1("sdtrust1", "山东信托二期","山东信托"),
	HAIERMONEY1("haiermoney1", "海尔云贷二期irr","海尔云贷"),
	HAIERMONEY2("haiermoney2", "海尔云贷二期apr","海尔云贷"),
	QSBANK("qsbank", "齐商银行","齐商银行"),
	HUBEICFC2("hubeicfc2", "湖北消金apr他收","湖北消金"),
	WSWL("wswl", "网商玮徕","网商玮徕"),
	QSBANK1("qsbank1", "齐商银行二期","齐商银行"),
	SDTRUST2("sdtrust2", "山东信托三期","山东信托"),
	LZBANK("lzbank", "兰州银行","兰州银行"),
	WSYC("wsyc", "网商粤财","网商粤财"),
	HUBEICFC3("hubeicfc3", "湖北消金三期apr他收","湖北消金"),
	SDTRUST3("sdtrust3", "山东信托四期","山东信托"),
	SDTRUST4("sdtrust4", "山东信托五期","山东信托"),
	LZBANK1("lzbank1", "安硕兰州","兰州银行"),
	SDTRUST5("sdtrust5", "山东信托六期","山东信托"),
	SDTRUST6("sdtrust6", "山东信托七期","山东信托"),
	SDTRUST7("sdtrust7", "山东信托八期","山东信托"),
	SDTRUST8("sdtrust8", "山东信托九期","山东信托"),
	WEISHENMA2("weishenma2", "微神马中裔担保irr36","微神马"),
	WEISHENMA3("weishenma3", "微神马中裔担保apr36","微神马"),
	QBCYCFC("qbcycfc", "长银消金","钱包长银"),
	FULONG("fulong", "富龙小贷","富龙小贷"),
	WEISHENMA4("weishenma4", "微神马中裔担保昊悦代收apr36","微神马"),
	OTHER1("other1", "其他1","北银"),
	LZBANK2("lzbank2", "安硕兰州irr36","兰州银行"),
	BOBCFC1("bobcfc1", "北银-经协IRR","北银"),
	BOBCFC3("bobcfc3", "北银-经协IRR24","北银"),
	OTHER2("other2", "北银-经协","北银"),
	HUBEICFC4("hubeicfc4", "湖北消金irr","湖北消金"),
	HUBEICFC5("hubeicfc5", "湖北消金apr","湖北消金"),
	CYCFC2("cycfc2", "长银消金中智信三期","长银消金"),
	LZBANK3("lzbank3", "安硕兰州自营apr","兰州银行"),
	LZBANK4("lzbank4", "安硕兰州自营irr","兰州银行"),
	HAIERCFC2("haiercfc2", "海尔消金三期irr他收","海尔消金"),
	HUIHEBANK("huihebank", "汇和银行","汇和银行"),
	JCFR("jcfr", "锦程分润", "锦程"),
	ZHONGZHIXIN("zhongzhixin","中智信","中智信"),
	HAIERCFC3("haiercfc3", "海尔消金","海尔消金"),
	SDTRUST10("sdtrust10", "山东信托十期","山东信托"),
	CYCFC3("cycfc3","长银消金-中智信irr24","长银消金"),
	SDTRUST11("sdtrust11", "山东信托十一期","山东信托"),
	SDTRUST12("sdtrust12", "山东信托十二期","山东信托"),
	JMX("jmx", "金美信","金美信"),
	JINMXCFC4("jinmxcfc4", "金美信四期","金美信四期"),
	SDTRUST13("sdtrust13", "山东信托十四期","山东信托"),
	SDTRUST14("sdtrust14", "山东信托十六期","山东信托"),
	SDTRUST15("sdtrust15", "山东信托十八期","山东信托"),
	SDTRUST16("sdtrust16", "山东信托十三期","山东信托"),
	SDTRUST17("sdtrust17", "山东信托十五期","山东信托"),
	SDTRUST18("sdtrust18", "山东信托十七期","山东信托"),
	ZBBANK2("zbbank2", "众邦银行irr24","众邦银行"),
	WPCFC("wpcfc","唯品富邦消金irr24","唯品富邦消金"),
	WEISHENMA5("weishenma5", "微神马IRR24-中裔","微神马"),
	JCDB2("jcdb2", "锦程担保二期","锦程"),
	ZBBANK3("zbbank3", "众邦银行三期irr24","众邦银行"),
	OPPO("oppo", "OPPO金融","OPPO金融"),
	OPPO1("oppo1", "OPPO金融(36费率)","OPPO金融(36费率)"),
	YKBANK("ykbank", "营口银行irr24", "营口银行"),
	YKBANK2("ykbank2", "营口银行irr36", "营口银行"),
	LHBANK("lhbank", "蓝海银行irr24我收","蓝海银行"),
	LHBANK2("lhbank2", "蓝海银行irr36我收","蓝海银行"),
	CYCFC4("cycfc4","长银消金五期","长银消金"),
	JINMXCFC5("jinmxcfc5","金美信五期","金美信五期"),
	OPPO2("oppo2","OPPO金融24%-xs","OPPO金融24%-xs"),
	OPPO3("oppo3","OPPO金融36%-za","OPPO金融24%-za"),
	GUOMINTRUST("guomintrust",  "国民信托一期", "国民信托一期"),
	GUOMINTRUST2("guomintrust2", "国民信托二期", "国民信托二期"),
	GUOMINTRUST3("guomintrust3", "国民信托三期", "国民信托三期"),
	HBCFC("hbcfc","湖北消金24","湖北消金24"),
	HBCFC1("hbcfc1","湖北消金36","湖北消金36"),
	LFBANK("lfbank","廊坊银行irr24","廊坊银行"),
	BOBCFC4("bobcfc4", "北银消金irr36","北银消金irr36"),
	SNBANK("snbank","苏宁银行24","苏宁银行24"),
	SNBANK1("snbank1","苏宁银行36","苏宁银行36"),
	WEISHENMA6("weishenma6", "微神马-大行irr36","微神马"),
	WEISHENMA7("weishenma7", "微神马大行中智信","微神马"),
	CABANK("cabank","长安银行irr24","长安银行"),
	CYCFC5("cycfc5","长银消金六期","长银消金"),
	CABANK2("cabank2","长安银行irr36","长安银行"),
	JINMXCFC6("jinmxcfc6","金美信六期irr36","金美信六期"),
	HTBANK("htbank","华通银行irr36","华通银行"),
	;


	public final String partnerCode;
	public final String partnerName;
	public final String name;

	PartnerCodeEnum(String partnerCode, String partnerName,String name) {
		this.partnerCode = partnerCode;
		this.partnerName = partnerName;
		this.name = name;
	}

	public static String getPartnerNameByCode(String partnerCode){
		for(PartnerCodeEnum codeEnum:PartnerCodeEnum.values()){
			if(codeEnum.partnerCode.equals(partnerCode)){
				return codeEnum.partnerName;
			}
		}
		return "";
	}

	public static PartnerCodeEnum getEnumByCode(String partnerCode){
		for(PartnerCodeEnum codeEnum:PartnerCodeEnum.values()){
			if(codeEnum.partnerCode.equals(partnerCode)){
				return codeEnum;
			}
		}
		return null;
	}

}
