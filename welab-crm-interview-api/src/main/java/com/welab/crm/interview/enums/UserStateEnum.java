package com.welab.crm.interview.enums;


/**
 * 用户状态枚举
 * <AUTHOR> 
 */
public enum UserStateEnum {
	NORMAL("1","正常用户"),
	OVERDUE("2","逾期用户"),
	TRANSFER_OR_FASU("3","债转或者法诉用户"),
	VIP("4","会员&特权卡"),
	NOT_REGISTER("5","非注册用户"),
	;
	private String code;

	private String msg;

	UserStateEnum(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public String getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}
}
