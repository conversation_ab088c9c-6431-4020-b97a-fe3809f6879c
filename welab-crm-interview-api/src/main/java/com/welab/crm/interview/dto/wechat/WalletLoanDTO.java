/**
 * @Title: UserQueryParam.java
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.dto.wechat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 钱包项目用户信息DTO
 * <AUTHOR>
 * @date 2020-11-16 10:05:49
 * @version v2.0
 */
@Data
@ApiModel("钱包贷款详情返回对象")
public class WalletLoanDTO implements Serializable {
    private static final long serialVersionUID = 1994759997194816354L;

    private Integer id;
    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;
    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;
    @ApiModelProperty(value = "订单描述", name = "orderTitle")
    private String orderTitle;
    @ApiModelProperty(value = "用户ID", name = "userID")
    private Integer userId;
    @ApiModelProperty(value = "期数", name = "tenor")
    private Integer tenor;
    @ApiModelProperty(value = "放款金额", name = "amount")
    private BigDecimal amount;
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    @ApiModelProperty(value = "确认日期", name = "confirmedAt")
    private String confirmedAt;
    @ApiModelProperty(value = "放款日期", name = "disbursedAt")
    private String disbursedAt;
    @ApiModelProperty(value = "结清日期", name = "closedAt")
    private String closedAt;
    @ApiModelProperty(value = "外部订单号", name = "externalOrderNo")
    private String externalOrderNo;
    @ApiModelProperty(value = "核销标识", name = "writeOff")
    private Integer writeOff;
    @ApiModelProperty(value = "确认日期", name = "partnerName")
    private String partnerName;
    @ApiModelProperty(value = "资金方编码", name = "partnerCode")
    private String partnerCode;
    @ApiModelProperty(value = "产品号", name = "productCode")
    private String productCode;
    @ApiModelProperty(value = "渠道号", name = "channelCode")
    private String channelCode;
    @ApiModelProperty(value = "来源", name = "originCode")
    private String originCode;
    @ApiModelProperty(value = "回购标识", name = "repurchaseStatus")
    private Integer repurchaseStatus;
    @ApiModelProperty(value = "原资方编码", name = "originalPartnerCode")
    private String originalPartnerCode;
    @ApiModelProperty(value = "贷款类型", name = "loanType")
    private String loanType;
    @ApiModelProperty(value = "当前还款期数", name = "currentIndexNo")
    private Integer currentIndexNo;
    @ApiModelProperty(value = "当期应还金额", name = "currentFeeAmount")
    private BigDecimal currentFeeAmount;
    @ApiModelProperty(value = "当期已还金额", name = "currentSettledAmount")
    private BigDecimal currentSettledAmount;
    @ApiModelProperty(value = "当期未还金额", name = "currentOutstandingAmount")
    private BigDecimal currentOutstandingAmount;
    @ApiModelProperty(value = "是否全额结清", name = "isAllowEarlySettle")
    private String isAllowEarlySettle;
    @ApiModelProperty(value = "全额结清金额", name = "settleAllAmount")
    private BigDecimal settleAllAmount;
    @ApiModelProperty(value = "银行卡号", name = "elecCardNo")
    private String elecCardNo;
    @ApiModelProperty(value = "当期还款计划", name = "dues")
    private List<WalletDueDTO> dues;
}
