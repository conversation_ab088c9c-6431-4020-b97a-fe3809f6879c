package com.welab.crm.interview.dto.wechat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 钱包还款计划DTO
 * <AUTHOR>
 * @date 2020-11-16 10:05:49
 * @version v2.0
 */
@Data
@ApiModel("钱包还款计划详情DTO")
public class WalletDueSettleDTO implements Serializable {
    private static final long serialVersionUID = -2249358507018643295L;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;
    @ApiModelProperty(value = "用户ID", name = "userId")
    private Integer userId;
    @ApiModelProperty(value = "贷款期数", name = "indexNo")
    private Integer indexNo;
    @ApiModelProperty(value = "还款日", name = "dueDate")
    private Date dueDate;
    @ApiModelProperty(value = "费用类型", name = "dueType")
    private String dueType;
    @ApiModelProperty(value = "应还金额", name = "dueAmount")
    private BigDecimal dueAmount;
    @ApiModelProperty(value = "已还金额", name = "settledAmount")
    private BigDecimal settledAmount;
    @ApiModelProperty(value = "还款计划", name = "detail")
    private BigDecimal outstandingAmount;
    @ApiModelProperty(value = "还款时间", name = "settledAt")
    private String settledTime;
    @ApiModelProperty(value = "还款状态", name = "state")
    private String state;

}
