package com.welab.crm.interview.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志实体类
 * <AUTHOR>
 */
@Data
@ApiModel("操作日志实体类")
public class VipOperateLogVO implements Serializable {

    private static final long serialVersionUID = -3578319582268382868L;
    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "id")
    private Long id;

    /**
     * 关联单号
     */
    @ApiModelProperty(name = "serviceNo", value = "关联单号")
    private String serviceNo;

    /**
     * 操作内容
     */
    @ApiModelProperty(name = "operationContent", value = "操作内容")
    private String operationContent;

    /**
     * 操作类型
     */
    @ApiModelProperty(name = "operationType", value = "操作类型")
    private String operationType;

    /**
     * 操作角色
     */
    private String operationRole;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    private Date createdAt;
}
