package com.welab.crm.interview.enums;

/**
 * 支付渠道枚举类
 * <AUTHOR>
 */
public enum PayCodeEnum {
    ZABX("zabx", "0021", "众安保险"),
    PPMONEY("ppmoney", "0017", "PPMoney"),
    JCBX("jcbx", "0019", "锦程保险"),
    FENGHUANG("fenghuang", "0018", "凤凰金融"),
    ZTSSJ("ztssj", "0014", "随手记"),
    KAOLALICAI("kaolalicai", "0009", "考拉理财"),
    ALLINPAY("allinpay", "0005", "通联"),
    UMPAY("umpay", "0002", "联动优势"),
    ALIPAY("alipay", "0000", "支付宝线下"),
    YILIAN("yilian", "0001", "易联"),
    WEIXIN("weixin", "0003", "微信"),
    YUNSHANGTONG("yunshangtong", "0023", "云商通"),
    HENGQINALIYUN("hengqin-al<PERSON><PERSON>", "0020", "横琴（支付宝）"),
    JINMEIXIN("jinmeixin", "0026", "金美信"),
    YOOLI("yooli", "0024", "有利网"),
    ZHONGAN("zhongan", "0006", "众安"),
    JINCHENG("jincheng", "0008", "锦城"),
    JINTOU("hzfi", "0011", "金投"),
    TONGBANJIE("tongbanjie", "0013", "铜板街"),
    ANXIN("anxin", "0022", "安信"),
    WACAI("wacai", "0027", "挖财"),
    JIMU("jimu", "0032", "积木盒子"),
    ZAZL("zazl", "0028", "众安租赁"),
    YNTRUST1("yntrust1", "0030", "云南信托-新信托计划"),
    YNTRUST2("yntrust2", "0044", "云南信托2-增信担保模式"),
    XCWS("xcws", "0029", "携程网商"),
    BOHAITRUST_TCCB("bohaitrust-tccb", "0031", "渤海信托"),
    YIQIANDAI("yiqiandai", "0033", "亿钱贷"),
    EROAD("eroad", "0035", "E路同心"),
    CYCFC("cycfc", "0037", "长银消费金额"),
    MANAOWAN("manaowan", "0036", "玛瑙湾"),
    HUBEICFC("hubeicfc", "0042", "湖北消金"),
    XZTRUST("xztrust", "0038", "西藏信托"),
    HAIERMONEY("haiermoney", "0039", "海尔云贷"),
    XIAOMI("xiaomi", "0040", "小米"),
    HAIERCFC("haiercfc", "0041", "海尔消金"),
    CTRIP("ctrip", "0042", "携程"),
    ZBBANK("zbbank", "0043", "众邦银行"),
    FSKALIYUN("fsk-aliyun", "0045", "中原（支付宝）"),
    BAOFU("baofu", "0046", "宝付"),
    XCJC("xcjc", "0047", "携程锦程"),
    CZCB("czcb", "0048", "稠州银行"),
    BOSC("bosc", "0049", "上海银行"),
    ALLINPAY_WALLET("allinpay_wallet", "0050", "通联-钱包"),
    KLTB("kltb", "0005", "快乐通宝"),
    JSCFC("jscfc", "0051", "晋商消金"),
    BOBCFC("bobcfc", "0052", "北银消金"),
    HENGQIN_JXZ("hengqin-jxz", "0053", "横琴(京小租)"),
    YNTRUST3("yntrust3", "0054", "云南信托增信担保3"),
    XMJMX("xmjmx", "0056", "小米金美信"),
    HAIERCFC1("haiercfc1", "0057", "海尔消金（IRR36）"),
    SDTRUST("sdtrust", "0061", "山东信托"),
    WALLET_GMTRUST("gmtrust", "0063", "钱包国民信托渠道"),
    FUXING("fuxing", "0059", "复星小贷"),
    WSXMTRUST("wsxmtrust", "0060", "网商厦门信托"),
    XMTRUST("xmtrust", "0062", "厦门信托二号绑卡渠道"),
    JCDB("jcdb", "0064", "锦程担保"),
    SDTRUST1("sdtrust1", "0066", "山东信托二期"),
    HAIERMONEY1("haiermoney1", "0067", "海尔云贷二期irr"),
    HAIERMONEY2("haiermoney2", "0068", "海尔云贷二期apr"),
    ZBBANK1("zbbank1", "", "众邦银行(非持牌担保)"),
    WEISHENMA("weishenma", "", "微神马irr36"),
    WEISHENMA1("weishenma1", "", "微神马apr36"),
    QSBANK("qsbank", "0069", "齐商银行"),
    HUBEICFC2("hubeicfc2", "0070", "湖北消金apr他收"),
    WSWL("wswl", "", "网商玮徕"),
    AIBANK("aibank", "0074", "百信银行"),
    DXMPAY("dxmpay", "0073", "度小满"),
    QSBANK1("qsbank1", "0075", "齐商银行二期"),
    SDTRUST2("sdtrust2", "0076", "山东信托三期"),
    GMTRUST_INSTALLMENT("gmtrust2", "0079", "钱包国民信托渠道(分期)"),
    LZBANK("lzbank", "", "兰州银行"),
    WSYC("wsyc", "0077", "网商粤财"),
    HUBEICFC3("hubeicfc3", "0080", "湖北消金三期apr他收"),
    SDTRUST3("sdtrust3", "0081", "山东信托四期"),
    SDTRUST4("sdtrust4", "0082", "山东信托五期"),
    LZBANK1("lzbank1", "", "安硕兰州"),
    SDTRUST5("sdtrust5", "0083", "山东信托六期"),
    SDTRUST6("sdtrust6", "0084", "山东信托七期"),
    WEISHENMA2("weishenma2", "", "微神马中裔担保irr36"),
    WEISHENMA3("weishenma3", "", "微神马中裔担保apr36"),
    QBCYCFC("qbcycfc", "", "钱包长银"),
    FULONG("fulong", "", "富龙小贷"),
    WEISHENMA4("weishenma4", "", "微神马中裔担保昊悦代收apr36"),
    OTHER1("other1", "", "其他1"),
    LZBANK2("lzbank2", "", "安硕兰州irr36"),
    OTHER2("other2", "", "其他2"),
    BOBCFC1("bobcfc1", "", "北银消金"),
    HUBEICFC4("hubeicfc4", "0085", "湖北消金irr"),
    HUBEICFC5("hubeicfc5", "0086", "湖北消金apr"),
    CYCFC2("cycfc2","","长银消金中智信"),
    ALLINPAY2("allinpay2","1005","通联所有支持银行"),
    LZBANK3("lzbank3", "", "安硕兰州自营"),
    BAOFU2("baofu2","1046","宝付"),
    LZBANK4("lzbank4", "", "安硕兰州自营irr"),
    HAIERCFC2("haiercfc2","0087", "海尔消金三期irr他收"),
    NJCB("njcb","0089","南京银行"),
    JCFR("jcfr", "0090", "锦程分润"),
    HUIHEBANK("huihebank", "", "汇和银行"),
    ZHONGZHIXIN("zhongzhixin", "0095", "中智信资方"),
    HAIERCFC3("haiercfc3", "0097", "海尔消金四期irr24他收"),
    SDTRUST10("sdtrust10", "0098", "山东信托十期"),
    CYCFC3("cycfc3", "0004", "长银消金中智信irr24"),
    SDTRUST11("sdtrust11", "0010", "山东信托十一期"),
    SDTRUST12("sdtrust12", "0016", "山东信托十二期"),
    JMX("jmx", "0088", "金美信"),
    JINMXCFC4("jinmxcfc4", "0100", "金美信四期"),
    SDTRUST13("sdtrust13", "0012", "山东信托十四期"),
    SDTRUST14("sdtrust14", "0025", "山东信托十六期"),
    SDTRUST15("sdtrust15", "0031", "山东信托十八期"),
    SDTRUST16("sdtrust16", "0034", "山东信托十三期"),
    SDTRUST17("sdtrust17", "0045", "山东信托十五期"),
    SDTRUST18("sdtrust18", "0047", "山东信托十七期"),
    ZBBANK2("zbbank2", "0051", "众邦银行irr24他收");

    public final String channelCode;
    public final String payCode;
    public final String channelname;

    private PayCodeEnum(String channelCode, String payCode, String desc) {
        this.channelCode = channelCode;
        this.payCode = payCode;
        this.channelname = desc;
    }

    public static String getNameByChannel(String channelCode) {
        for (PayCodeEnum type : values()) {
            if (type.getChannelCode().equals(channelCode)) {
                return type.channelname;
            }
        }
        return channelCode;
    }

    public static String getNameByPaycode(String payCode) {
        for (PayCodeEnum type : values()) {
            if (type.getPayCode().equals(payCode)) {
                return type.channelname;
            }
        }
        return payCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public String getPayCode() {
        return payCode;
    }

    public String getChannelname() {
        return channelname;
    }
}
