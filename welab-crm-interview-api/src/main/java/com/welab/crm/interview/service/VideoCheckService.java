package com.welab.crm.interview.service;

import java.util.Date;

/**
 * 视频录制相关服务
 * <AUTHOR>
 */
public interface VideoCheckService {


    /**
     * 根据 token 获取
     * @param token
     * @return
     */
    String getReadMsg(String token);

    /**
     * 上传视频并且推送至Ai部门进行校验
     * @param sourceBytes 源文件字节数组
     * @param bytes 文件字节数组
     * @param token token
     * @param startTime 开始录制时间
     * @param endTime 结束录制时间
     */
    void uploadVideoAndPushToAi(byte[] bytes, byte[] sourceBytes, String token, Date startTime,
        Date endTime);
}
