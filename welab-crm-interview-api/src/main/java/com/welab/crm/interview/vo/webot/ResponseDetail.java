package com.welab.crm.interview.vo.webot;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class ResponseDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回消息: 例如请确保本人操作且正脸对框
     */
    private String msg;
    /**
     * 验证结果：0-成功   1-失败  2-未验证
     */
    private Integer code;

    /**
     * 验证结果对应的中文
     */
    private String codeDesc;

    /**
     * 供应商
     */
    private String vendor;

    /**
     * 验证时间：2023-02-13 14:51:47
     */
    @JSONField(name = "verify_time")
    private String verifyTime;
}
