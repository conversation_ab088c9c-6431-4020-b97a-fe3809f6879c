package com.welab.crm.interview.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息修改DTO
 * <AUTHOR>
 * @date 2021/9/28 15:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoModifyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 现手机号
     */
    private String mobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证
     */
    private String cnid;

}
