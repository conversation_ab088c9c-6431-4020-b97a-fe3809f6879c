package com.welab.crm.interview.service;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.dto.workorder.WorkOrderNoticeDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WorkOrderService {

    /**
     * 根据客户手机号查询客户备用号码
     * @param mobile
     * @return
     */
    List<JSONObject> queryOrderMobileBak(String mobile);


    /**
     * 批量更新wo_task表中的suc_loan_count字段
     */
    void batchUpdateWoTaskSucLoanCount();

    /**
     * 查询当天需要通知的工单
     */
    void queryDailyOrderAndNotice();

    /**
     * 根据未结案天数查询工单并通知
     * @param startDay 未结案起始天数
     * @param endDay 未结案结束天数
     */
    void queryNotCloseOrderAndNotice(Integer startDay, Integer endDay);

    /**
     * 敏感工单通知
     */
    void sensitiveOrderNotice(WorkOrderNoticeDTO dto);


    /**
     * 查询7天未联系的工单并通知
     */
    void queryOverSevenDayUnContactOrderAndNotice();

    /**
     * 查询超过24小时未联系的工单并通知
     */
    void queryOverTwentyFourHourUnContactOrderAndNotice();

    /**
     * 工单响应超时状态更新
     */
    void orderResponseTimeOutStateUpdate();
    
}
