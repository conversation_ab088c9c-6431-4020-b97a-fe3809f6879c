package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
@Data
@ApiModel(value = "钱包交易记录响应对象")
public class WalletOrderRecordVO implements Serializable {

    private static final long serialVersionUID = -5901469095671809315L;

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "交易流水号")
    private String orderNo;

    @ApiModelProperty(value = "账单号")
    private String billNo;

    /**
     * 交易类型
     * {@link com.welab.wallet.app.enums.OrderTypeEnum}
     */
    @ApiModelProperty(value = "交易类型")
    private String orderType;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "交易日期")
    private Date transactionDate;

    @ApiModelProperty(value = "交易时间")
    private Date transactionTime;

    /**
     * 交易状态
     * {@link com.welab.wallet.app.enums.WalletOrderStatusEnum}
     */
    @ApiModelProperty(value = "交易状态")
    private String status;

    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    @ApiModelProperty(value = "消费渠道")
    private String tradeChannel;

    @ApiModelProperty(value = "通道类型")
    private String channelType;

    @ApiModelProperty(value = "处理信息（失败原因）")
    private String retMsg;


}
