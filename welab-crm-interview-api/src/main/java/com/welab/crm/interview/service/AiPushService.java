package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.callback.AiCallBackDTO;
import com.welab.crm.interview.vo.callback.AiCallBackVo;

/**
 * ai外呼具体的对接ivr推送服务
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022.2.25
 */
public interface AiPushService {

    /**
     * 用id查询出需要推送的产品相关数据，推送到voice_ivr服务
     *
     * @param id ai外呼主键id
     */
    void pushDataToIvr(Long id);


    /**
     * 保存voice-ivr回调过来的ai外呼数据
     *
     * @param callBackDTO 客户电话,通话时间等相关信息
     * @return 约定的回复状态消息
     */
    AiCallBackVo saveCallbackData(AiCallBackDTO callBackDTO);
}
