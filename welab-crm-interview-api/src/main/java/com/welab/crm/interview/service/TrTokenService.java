package com.welab.crm.interview.service;


/**
 * 天润token服务接口，对接voice-center
 *
 * <AUTHOR>
 */
public interface TrTokenService {


    /**
     * 根据enterpriseId获取天润token
     *
     * @param enterpriseId 天润企业ID
     * @return
     */
    String getTokenByEnterpriseId(String enterpriseId);


    /**
     * 查询天润 中继报表来电分析 ，把相关数据入库
     */
    void saveTrunkReportIbToDb(String countDate);


    /**
     * 获取录音文件地址
     * @param enterpriseId
     * @param cdrRecordFile
     * @return
     */
    String getWaveSoundUrl(String enterpriseId, String cdrRecordFile);

}
