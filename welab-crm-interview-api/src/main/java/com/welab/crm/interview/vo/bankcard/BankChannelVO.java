package com.welab.crm.interview.vo.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(value = "绑卡查询响应对象")
public class BankChannelVO implements Serializable {

    private static final long serialVersionUID = 4487772830193965492L;

    @ApiModelProperty(value = "资金名称（资金方）")
    private String partnerName;

    @ApiModelProperty(value = "资金方编号（放款机构）")
    private String partnerNo;

    @ApiModelProperty(value = "匹配结果")
    private String matchResult;

    @ApiModelProperty(value = "绑卡状态（是否绑卡）")
    private String bankcardStatus;

    @ApiModelProperty(value = "备注")
    private String message;

    @ApiModelProperty(value = "银行名称（支持银行卡）")
    private String bankcardName;
    
    @ApiModelProperty(value = "放款时间")
    private String loanTime;
}
