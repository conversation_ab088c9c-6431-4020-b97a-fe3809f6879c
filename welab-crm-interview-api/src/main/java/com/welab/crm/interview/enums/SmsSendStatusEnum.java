package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 短信发送状态枚举
 * @date 2021/12/10
 */
public enum SmsSendStatusEnum {

    READY("ready", "已预约发送"),
    SUCCESS("success", "发送成功"),
    FAILURE("failure", "发送失败"),
    ;

    private String value;
    private String text;

    SmsSendStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        for (SmsSendStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum.getText();
            }
        }
        return "";
    }
}
