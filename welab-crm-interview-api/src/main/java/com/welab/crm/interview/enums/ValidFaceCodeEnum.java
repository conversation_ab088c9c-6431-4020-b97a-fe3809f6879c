package com.welab.crm.interview.enums;

/**
 * 创研返回的结果码枚举
 */
public enum ValidFaceCodeEnum {


    SUCCESS(0, "成功"),
    FAIL(1, "失败"),
    WAITING(2, "未验证");

    ValidFaceCodeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private final int value;
    private final String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByValue(int value) {
        for (ValidFaceCodeEnum type : values()) {
            if (type.value == value) {
                return type.desc;
            }
        }
        return null;
    }
}
