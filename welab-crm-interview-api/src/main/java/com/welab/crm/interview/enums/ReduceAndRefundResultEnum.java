package com.welab.crm.interview.enums;

/**
 * 减免退款状态
 * <AUTHOR> 
 */
public enum ReduceAndRefundResultEnum {
	RECHARGE_SUCCESS("recharge_success", "补贴成功"),
	RECHARGE_FAILED("recharge_failed", "补贴失败"),

	REFUND_SUCCESS("refund_success", "退款成功"),
	REFUND_PROCESSING("refund_processing", "退款处理中"),
	REFUND_FAILED("refund_failed", "退款失败"),
	PUSH_FAILED("push_failed", "推送失败"),
	;

	private String status;
	private String desc;

	ReduceAndRefundResultEnum(String status, String desc) {
		this.status = status;
		this.desc = desc;
	}

	public String getStatus() {
		return status;
	}

	public String getDesc() {
		return desc;
	}
	
	public static String getDescByStatus(String status){
		for (ReduceAndRefundResultEnum resultEnum : values()) {
			if (resultEnum.status.equals(status)){
				return resultEnum.desc;
			}
		}
		
		return status;
	}
}
