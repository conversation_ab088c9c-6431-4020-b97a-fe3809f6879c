package com.welab.crm.interview.vo.bankcard;

import lombok.Data;

@Data
public class OriginInfo {

    /**
     * 渠道归属
     */
    private String belong;
    /**
     * 小类
     */
    private String category;
    /**
     * 进件状态，true-已上架；false-已下架；default-默认
     */
    private String incomingOnLine;
    /**
     * 中类
     */
    private String middleCategory;
    /**
     * 渠道名称
     */
    private String name;
    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 渠道号
     */
    private String origin;
    /**
     * 渠道前序
     */
    private String prefix;
    /**
     * 产品号
     */
    private String product;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 是否自营：1-是，0-否
     */
    private Integer selfSupport;
}
