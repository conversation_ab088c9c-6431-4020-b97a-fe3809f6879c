package com.welab.crm.interview.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
public class UserQueryDTO {

    /**
     * 用户手机号码
     */
    @NotBlank(message = "用户手机号码不能为空")
    private String mobile;

    /**
     * 时间戳
     */
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;

    /**
     * 校验用签名
     */
    @NotBlank(message = "签名信息不能为空")
    private String sign;
}
