package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.xdao.context.page.Page;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 软电话2小时重复来电服务
 * @date 2022/2/22
 */
public interface ConRepeatCallService {

    /**
     * 分页查询首问解决率报表
     *
     * @param dto
     * @return
     */
    Page<ReportResolvedRateVO> queryRecord(RepeatCallDTO dto);

    /**
     * 查询首问解决率报表
     *
     * @param dto
     * @return
     */
    List<ReportResolvedRateVO> queryRecordList(RepeatCallDTO dto);
}
