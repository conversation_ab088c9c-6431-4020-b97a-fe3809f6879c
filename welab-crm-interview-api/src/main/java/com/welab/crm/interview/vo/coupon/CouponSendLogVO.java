package com.welab.crm.interview.vo.coupon;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/18
 */
@Data
@ApiModel(value = "卡券发送记录响应对象")
@Excel(fileName = "卡券发送记录")
public class CouponSendLogVO implements Serializable {

    private static final long serialVersionUID = -1195944741936890879L;

    @ApiModelProperty(value = "业务类型, loan 工薪贷, wallet 钱包")
    @ExcelTitleMap(title = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "卡劵ID")
    @ExcelTitleMap(title = "卡劵ID")
    private Long couponId;

    @ApiModelProperty(value = "卡劵类型")
    @ExcelTitleMap(title = "卡劵类型")
    private String amountType;

    @ApiModelProperty(value = "面值")
    @ExcelTitleMap(title = "面值")
    private BigDecimal amount;

    @ApiModelProperty(value = "活动名称")
    @ExcelTitleMap(title = "活动名称")
    private String description;

    @ApiModelProperty(value = "操作类型")
    @ExcelTitleMap(title = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "有效期")
    @ExcelTitleMap(title = "有效期")
    private Integer availableDays;

    @ApiModelProperty(value = "userId")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty(value = "发送日期")
    @ExcelTitleMap(title = "发送日期")
    private Date gmtCreate;

    @ApiModelProperty(value = "发送人名称")
    @ExcelTitleMap(title = "发送人名称")
    private String staffName;

    @ApiModelProperty(value = "发送组名称")
    @ExcelTitleMap(title = "发送组名称")
    private String groupName;

    @ApiModelProperty(value = "发送人")
    private String staffId;

    @ApiModelProperty(value = "发送组")
    private String groupCode;

}
