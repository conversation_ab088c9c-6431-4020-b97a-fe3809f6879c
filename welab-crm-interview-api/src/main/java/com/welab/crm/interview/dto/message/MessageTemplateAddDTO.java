package com.welab.crm.interview.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Data
@ApiModel(value = "添加短信模板请求对象")
public class MessageTemplateAddDTO implements Serializable {

    private static final long serialVersionUID = 5261435261267609640L;

    @ApiModelProperty(value = "短信模板代码，来自短信系统")
    @NotBlank
    private String smsCode;

    @ApiModelProperty(value = "短信标题")
    @NotBlank
    private String title;

    @ApiModelProperty(value = "目录组，字典字段")
    @NotBlank
    private String directory;

    @ApiModelProperty(value = "模板描述")
    private String description;

    @ApiModelProperty(value = "短信类型，字典字段")
    @NotBlank
    private String smsType;
}
