package com.welab.crm.interview.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 用户支付记录
 */
@Data
@ApiModel(value = "用户支付记录响应对象")
public class VipOrderVO implements Serializable {

    private static final long serialVersionUID = 910528987278465575L;

    @ApiModelProperty(value = "单号", name = "seqNo")
    String seqNo;

    @ApiModelProperty(value = "供应商订单号", name = "supplierOrderNo")
    String supplierOrderNo;

    @ApiModelProperty(value = "交易金额", name = "amount")
    BigDecimal amount;

    @ApiModelProperty(value = "交易渠道名称", name = "channelName")
    String channelName;

    @ApiModelProperty(value = "银行卡号", name = "bankCardAccount")
    String bankCardAccount;

    @ApiModelProperty(value = "所属银行", name = "bankBranchName")
    String bankBranchName;

    @ApiModelProperty(value = "支付状态", name = "status")
    String status;

    @ApiModelProperty(value = "失败原因", name = "retMsg")
    String retMsg;

    @ApiModelProperty(value = "交易完成时间", name = "transactionTime")
    String transactionTime;

    @ApiModelProperty(value = "购买来源", name = "source")
    String source;
}
