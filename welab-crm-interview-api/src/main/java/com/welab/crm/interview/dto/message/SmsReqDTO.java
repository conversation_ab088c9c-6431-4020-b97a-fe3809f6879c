package com.welab.crm.interview.dto.message;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/16
 */
@Data
@ApiModel(value = "短信平台发送短信请求对象")
public class SmsReqDTO implements Serializable {

    private static final long serialVersionUID = 4086513393380042019L;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 短信模板内容变量参数
     */
    private Map<String, String> params;
    /**
     * 短信内容
     */
    private String content;
    /**
     * 分类统计标识
     */
    private String appTags;
    /**
     * 用户ID 由"来源渠道"+"_"+"系统名称"组成
     * <p>
     * "来源渠道"@see com.welab.message.sms.enums.SourceChannel
     */
    private String userId;
    /**
     * 时间戳到秒
     */
    private String reqTime;
    /**
     * 签名
     * <p>
     * 计算方式采用md5 32位大写加密 MD5.encrypt(mobile+ secretkey+reqTime).toUpperCase()
     * <p>
     * 如mobile="13612342589";secretkey="abc123";reqTime="13955284623"; 计算得出sign="4C2764F394821B807FCFD975D03993A3"
     */
    private String sign;
}
