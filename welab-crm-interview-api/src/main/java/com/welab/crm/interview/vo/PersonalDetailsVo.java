package com.welab.crm.interview.vo;

import com.welab.crm.interview.dto.LoanInfoDTO;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Lyman on 2016/11/25.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonalDetailsVo implements Serializable {
	private static final long serialVersionUID = 8136405389535298723L;

	private LoanInfoDTO loan;
	private ProfileVo profile;
	private List<EducationVo> education;
	private CompanyVo company;
	private List<LiaisonVo> liaison;
	private List<DocumentVo> document;
}
