package com.welab.crm.interview.enums;

/**
 * 坐席监控告警类型中英文转化枚举
 * <AUTHOR>
 */

public enum StaffMonitorWarningTypeEnum {
	userDetailQueryCount("userDetailQueryCount","客户资料查询"),
	queryUserCount("queryUserCount","去重客户量"),
	mobileDecodeCount("mobileDecodeCount","明文号码查询"),
	loanAgreementQueryCount("loanAgreementQueryCount","贷款协议查询"),
	soundQueryCount("soundQueryCount","录音保存量"),
	phoneSummaryCount("phoneSummaryCount","电话小结敏感关键词"),
	;


	private String code;
	
	private String desc;

	StaffMonitorWarningTypeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
}
