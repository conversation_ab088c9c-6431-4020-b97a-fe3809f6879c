package com.welab.crm.interview.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class BairongRequestVO<T> {

    private long timestamp;

    private String sign;

    private String data;

    public BairongRequestVO(Object businessData, String privateKey, String aesKey) {
        this.timestamp = System.currentTimeMillis();
        this.data = JSON.toJSONString(businessData,
                SerializerFeature.WriteNullNumberAsZero,
                SerializerFeature.WriteNullStringAsEmpty);
        if (StringUtils.isNotBlank(aesKey)) {
            byte[] bytes = DES_CBC_Encrypt(data.getBytes(), aesKey.getBytes());
            this.data = byteToHexString(bytes);
        }
        Map<String, Object> signData = getSignData();
        this.sign = sign(JSON.toJSONString(signData), privateKey);
    }

    public Map<String, Object> getSignData() {
        Map<String, Object> params = new HashMap<>();
        params.put("timestamp", String.valueOf(timestamp));
        params.put("data", data);
        return params;
    }


    public T toObject(Class<T> clazz, String aesKey) {
        if (aesKey == null || clazz == null || data == null) {
            return null;
        }
        byte[] bytes = hexStringToByteArray(data);
        try {
            this.data = new String(DES_CBC_Decrypt(bytes, aesKey.getBytes()), "UTF-8");
        } catch (Exception e) {
            log.error("toObject error data = {} e = ", data, e);
            return null;
        }
        return JSONObject.parseObject(data, clazz);
    }

    public boolean checkSign(String publicKey) {
        return verify(JSON.toJSONString(this.getSignData()), getSign(), publicKey);
    }

    public boolean expire(long expireSeconds) {
        return System.currentTimeMillis() - this.getTimestamp() > expireSeconds * 1000;
    }
    public static byte[] DES_CBC_Encrypt(byte[] content, byte[] keyBytes) {
        try {
            DESKeySpec keySpec = new DESKeySpec(keyBytes);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey key = keyFactory.generateSecret(keySpec);
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(keySpec.getKey()));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.out.println("exception:" + e.toString());
        }
        return null;
    }

    public static byte[] DES_CBC_Decrypt(byte[] content, byte[] keyBytes) {
        try {
            DESKeySpec keySpec = new DESKeySpec(keyBytes);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey key = keyFactory.generateSecret(keySpec);
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(keyBytes));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.out.println("exception:" + e.toString());
        }
        return null;
    }

    public static String byteToHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer(bytes.length);
        String sTemp;
        for (int i = 0; i < bytes.length; i++) {
            sTemp = Integer.toHexString(0xFF & bytes[i]);
            if (sTemp.length() < 2)
                sb.append(0);
            sb.append(sTemp.toUpperCase());
        }
        return sb.toString();
    }

    public static byte[] hexStringToByteArray(String s) {
        BigInteger bi = new BigInteger(s, 16);
        // 使用可能的负值
        byte[] bytes = bi.toByteArray();
        // 如果最高位是0，那么byte[]的长度可能会比实际的多一个字节
        // 需要去除这个额外的字节
        if (bytes[0] == 0) {
            byte[] tempBytes = new byte[bytes.length - 1];
            System.arraycopy(bytes, 1, tempBytes, 0, tempBytes.length);
            return tempBytes;
        }
        return bytes;
    }


    public static String sign(String data, String privateKeyStr) {
        try {
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyStr));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(data.getBytes());
            return Base64.getEncoder().encodeToString(signature.sign());
        } catch (Exception e) {
            log.error("sign error data = {} e = ", data, e);
            return null;
        }
    }

    public static boolean verify(String data, String signStr, String publicKeyStr) {
        try {
            byte[] sign = Base64.getDecoder().decode(signStr);
            java.security.spec.X509EncodedKeySpec publicKeySpec = new java.security.spec.X509EncodedKeySpec(Base64.getDecoder().decode(publicKeyStr));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(publicKeySpec);

            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);
            signature.update(data.getBytes());
            return signature.verify(sign);
        } catch (Exception e) {
            log.error("vertify error data = {} sign = {} e = ", data, signStr, e);
            return false;
        }
    }

    public static void main(String[] args) {
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq4PJZLiiTEeYmQiATQtfVFx+uuK4vJiRt1/t0oNt+DLhQ3LgjqWdsNsTOUAq4emOpoaltMVtvEMQgrLeLaFak+bV/Kc/7KYdyQfEtsuASzEYOB0MOEqhLC7/pTxM7m9vTFsU5VTkFdobuoRUDNy0sH+ZC+RsuJjg3MpUYRXCF8fxOBuL+VM++7miI+nSdEHLrqfvIgt6bo9EoNvcyPqiU3RVqdHYCDFHGDl4YPhcM3WsaE5kHIo8faOn66ygpD+nqKNMKcTfvr+M8AsZmBSeAyYX0Z48O7yHvBaNNLLxfcmlsHIPUg578D/t6k39CXdISckFLtxuAHRr4sTVJ3J1NwIDAQAB";
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCrg8lkuKJMR5iZCIBNC19UXH664ri8mJG3X+3Sg234MuFDcuCOpZ2w2xM5QCrh6Y6mhqW0xW28QxCCst4toVqT5tX8pz/sph3JB8S2y4BLMRg4HQw4SqEsLv+lPEzub29MWxTlVOQV2hu6hFQM3LSwf5kL5Gy4mODcylRhFcIXx/E4G4v5Uz77uaIj6dJ0Qcuup+8iC3puj0Sg29zI+qJTdFWp0dgIMUcYOXhg+FwzdaxoTmQcijx9o6frrKCkP6eoo0wpxN++v4zwCxmYFJ4DJhfRnjw7vIe8Fo00svF9yaWwcg9SDnvwP+3qTf0Jd0hJyQUu3G4AdGvixNUncnU3AgMBAAECggEAP+akxQrpJORT3UhGjO3cwZCIgtZvn4x0Vc6+y3y+HwfN15RYR606mCa2/dCDaZHb3P0zhas83DRcVWWQ041XMt2BAlXJwkCNvlVPnA8rqFgNPvLLl7luXQjRpF1YCEmqPNd+bIb5R47wX8hnjDc6drvDaKPdQdVribc0QsuZecQeTfmFe5pAs/skzSYjNr+S74mPs8cA7LLwC7TMrNxLkAf/NWKFZ755s9IImAVinlxgowuL5yfafB8cHk6RjnZQXMVk5RqPFK9zTN2BoMi2OgkvJ8Hzh3cwpj5Aa4rsklOaCE3YbNv7HamdgY1P9Z21Bg4xEgNZnxcGvs7cbzdvuQKBgQDreUnO8NnW/xXePLVEf97NZhHfThOZ39NCgCFd6ZlFKDoZodbPncA89ZocsxHlwse1Qn1NNxHqf3BWdZip79ski39l7dWeH7mvzk0NBii9ffxi4eJR+qc1qSD83a5e2LGUrSiIrPd4ZWwaAJYpeqzVprFWnHeHWCIoB4q6l/KwHQKBgQC6dzj0GKzx3bZIfK71DMaezM+8CvZpl9Nu8JB4P/wcVvvhc5tX0pXYkmk2OmSK4zkFdxHXnXRcZNO1IgxKXDLJOQgTte1uGrakoKYWO9GyduOjCj+LgF7jnYX9x33wsC9OjB8mg/8es20er8ha5yWtXP2rJtmpA7fp/CcE+N6iYwKBgQDVEWG07ADhZt29w0buXZoYV2SSn85irMI3aUNH/AjhJ/4ylVuPb5m0TzQNBgeK1E3q+Ofyh4MhogDfavchxdSQY916ONPTibd2mgDkaQ9SL37BZs1oCptklc4woNKYJhHBi1GjIoemnHdNq/w0OjnL9VKAIYKSqC/38qMYJkfGDQKBgQCvrrKvcSxI+Z7L7wWchjwA1HGI9z8xP8YKyYckdQyg2ueYhCbBePyLv253lE60eN95obTi1EwU3T6Gc5zm1rVW13AfuhII4gxhUacKXbP2FIPnUjmKwxkkFX9wGuwywJzhwa17yXOuRlkyR2ZbF1ttWqb6AbGjx4gZYeSVj7GbhwKBgFre5UeUtsqldlhVnxn4VZAnb/5MR0SU81uX11tvCGUii3mbuh+/weO4FMV3Yxpxh8qCEJJu1Jop121/2Qg4ErGGuZ25VZH6haJv/dcJldRGch2kkG688Q8KCoMdZZZ5YghtXpWXgorPFCeunRD9FX4Wai66PRqAOaxAAVovOdgI";
        String json = "{\"request\":\"2\"}";
        String aesKey = "bairong_";
        BairongRequestVO req = new BairongRequestVO(json, privateKey, aesKey);
        System.out.println("request sign check  = " + req.checkSign(publicKey));
        String requestParams = JSON.toJSONString(req);
        System.out.println("request params = \n" + requestParams);


        String request = "{\"data\":\"765C584F3766580B9EB6CA6E21A393FA5F7340267360C3E1\",\"sign\":\"cCGRzhfj/AW8rP46jD+ULzehFrjeuYS+z0kns4AOhHHXcig/S5TuqZ4W9BF/J1SivfhJml4Z2Q6Uq9/jMUfYXV6Npa6d1ZasI21ZLPb2USGiEkzTPv9YyPsS1G0nxYIMC8CgLUAupamL8b/Kw5Nr46Wf5jetgCDg8RdsDsSAAYdcw+P5a4zt3iPOH9PwyHl/JWVv1IwuBKa+39szdaz2u8VLAAFSobIyHgxwTXaXSbSD87zZKmi4ZrJPllr0SNrzib8BJQblOVJUIr2dVQ0AbB1giLBKwLoK8hUT3wkT7D3Alh/5duDEZXyvt/eiKp1BA5rE9wgFYG7GTwqNc84WOg==\",\"timestamp\":1726649862563}\n";

        System.out.println(" ===========================  ");
        BairongRequestVO requestVO = JSON.parseObject(request, BairongRequestVO.class);
        System.out.println("sign check  = " + requestVO.checkSign(publicKey));
        System.out.println("sign is expire  = " + requestVO.expire(300));
        Object vo = requestVO.toObject(Object.class, aesKey);
        System.out.println("to object = \n" + vo);
    }


}