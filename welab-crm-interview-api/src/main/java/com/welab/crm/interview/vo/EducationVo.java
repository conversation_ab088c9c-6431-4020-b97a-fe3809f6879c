package com.welab.crm.interview.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 教育信息
 * Created by Lyman on 2016/11/24.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EducationVo implements Serializable {
	private static final long serialVersionUID = 6952800036697539060L;

	private String schoolName;
	private String schoolAddr;
	private String degree;
	private String pengyuanVeri;
	private Date entranceTime;
	private Date graduateTime;

}
