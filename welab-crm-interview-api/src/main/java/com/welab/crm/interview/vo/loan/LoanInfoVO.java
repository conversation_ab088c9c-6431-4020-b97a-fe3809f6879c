package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "进件产品和额度产品贷款详情")
public class LoanInfoVO implements Serializable {

    private static final long serialVersionUID = -5178647889379335509L;

    @ApiModelProperty(value = "客户uuid", name = "uuid")
    private Long uuid;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "产品编号", name = "productCode")
    private String productCode;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "申请人名称", name = "name")
    private String name;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

    @ApiModelProperty(value = "公司名称", name = "company")
    private String company;

    @ApiModelProperty(value = "审批金额", name = "approvalAmount")
    private BigDecimal approvalAmount;

    @ApiModelProperty(value = "审批期限", name = "approvalTenor")
    private String approvalTenor;

    /**
     * @see com.welab.enums.LoanApplicationStateEnum
     */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "申请时间", name = "applyTime")
    private Date applyTime;

    @ApiModelProperty(value = "申请金额", name = "applyAmount")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "审批时间", name = "approvalTime")
    private Date approvalTime;

    @ApiModelProperty(value = "申请期限", name = "applyTenor")
    private String applyTenor;

    @ApiModelProperty(value = "申请平台", name = "applyPlatform")
    private String applyPlatform;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    private Date disbursedTime;

    @ApiModelProperty(value = "用户确认放款时间", name = "confirmedAt")
    private Date confirmedTime;

    @ApiModelProperty(value = "贷款来源", name = "origin")
    private String origin;

    @ApiModelProperty(value = "用户状态", name = "blocked")
    private Boolean blocked;

    @ApiModelProperty(value = "审批员", name = "approver")
    private String approver;

    @ApiModelProperty(value = "是否加急", name = "urgent")
    private Integer urgent;

    @ApiModelProperty(value = "审批金额", name = "amount")
    private BigDecimal amount;

    @ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

    @ApiModelProperty(value = "用户Id", name = "borrowerId")
    private Integer borrowerId;

    @ApiModelProperty(value = "资金方编码", name = "partnerCode")
    private String partnerCode;

    @ApiModelProperty(value = "总利率", name = "totalRate")
    private BigDecimal totalRate;

    @ApiModelProperty(value = "贷款号对应的会员订单号", name = "vipOrderNo")
    private String vipOrderNo;

    @ApiModelProperty(value = "渠道方会员来源")
    private String vipAssetCode;


    @ApiModelProperty(value = "外部订单号")
    private String externalOrderNo;
}
