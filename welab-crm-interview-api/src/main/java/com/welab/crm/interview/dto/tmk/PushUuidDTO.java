package com.welab.crm.interview.dto.tmk;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 运营平台推送提额用户DTO
 *
 * <AUTHOR>
 * @version v1.0
 */
@Data
public class PushUuidDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * uuid 列表
     */
    private List<UuidDTO> uuid;

    /**
     * 外呼方式
     */
    private String callType;

    /**
     * 号码包id
     */
    private String numPackageId;

    /**
     * 号码包定义
     */
    private String packageDefine;

    /**
     * 额度更新时间
     */
    private Date quotaUpdateTime;

    /**
     * 名单类型，进件类型：jj；提现类型：tx
     */
    private String type;
}