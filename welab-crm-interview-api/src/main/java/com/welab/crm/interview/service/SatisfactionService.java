package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import com.welab.xdao.context.page.Page;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 满意度调查服务
 * @date 2022/2/24
 */
public interface SatisfactionService {

    /**
     * 分页查询坐席满意度报表
     *
     * @param dto
     * @return
     */
    Page<ReportSatisfactionVO> getSatisfactionReport(RepeatCallDTO dto);

    /**
     * 查询坐席满意度报表
     *
     * @param dto
     * @return
     */
    List<ReportSatisfactionVO> getSatisfactionReportList(RepeatCallDTO dto);
}
