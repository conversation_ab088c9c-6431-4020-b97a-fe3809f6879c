package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 对应消金那边的支付渠道
 */
public enum PaymentModeEnum {
    OAK_ALLINPAY("oak_allinpay", "银行卡支付-橡树"),
    ALLINPAY("allinpay", "银行卡支付"),
    OAK_ALIPAY("oak_alipay", "支付宝支付-橡树");

    private String code;
    private String desc;

    PaymentModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String code) {
        if (StringUtils.isBlank(code)) {
            return code;
        }
        for (PaymentModeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return code;
    }
}
