package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 *
 * 额度状态枚举
 * <AUTHOR>
 * @date 2021/12/6 14:30
 */
public enum QuotaStateEnum {

    NORMAL("normal","正常"),
    FREEZED("freezed","冻结"),
    END("end","终止"),
    WRITE_OFF("write-off","注销");

    private String code;
    private String desc;

    QuotaStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String code){
        if(StringUtils.isBlank(code)){
            return code;
        }
        for (QuotaStateEnum value : values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return code;
    }
}
