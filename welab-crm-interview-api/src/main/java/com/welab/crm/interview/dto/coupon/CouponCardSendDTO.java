package com.welab.crm.interview.dto.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/19
 */
@Data
@ApiModel(value = "发送红包请求对象")
public class CouponCardSendDTO implements Serializable {

    @NotNull
    @ApiModelProperty(value = "卡券id ")
    private Long couponId;

    @NotNull
    @ApiModelProperty(value = "客户id")
    private Integer userId;

    @NotNull
    @ApiModelProperty(value = "客户uuid")
    private Long uuid;

    @NotBlank
    @ApiModelProperty(value = "业务类型, loan 工薪贷, wallet 钱包")
    private String businessType;

    @ApiModelProperty(value = "面值")
    private String amount;

    @ApiModelProperty(value = "卡劵类型")
    private String amountType;

    @ApiModelProperty(value = "活动名称")
    private String description;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "有效期(单位：天)")
    private Integer availableDays;

    @ApiModelProperty(value = "发送人")
    private String createStaffId;

    @ApiModelProperty(value = "发送组")
    private String createStaffGroup;
}
