package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
@Data
@ApiModel(value = "钱包还款记录响应对象")
public class WalletRepayRecordVO implements Serializable {

    private static final long serialVersionUID = -2986363071855438350L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "还款流水号")
    private String serviceNo;

    @ApiModelProperty(value = "还款模式（还款类型）")
    private String action;

    @ApiModelProperty(value = "扣款金额")
    private BigDecimal debitAmount;

    @ApiModelProperty(value = "红包金额")
    private BigDecimal couponAmount;

    @ApiModelProperty(value = "扣款来源")
    private String debitSource;

    @ApiModelProperty(value = "代扣渠道")
    private String debitChannelCode;

    @ApiModelProperty(value = "还款状态")
    private String status;

    @ApiModelProperty(value = "还款日期")
    private Date repaymentDate;

    @ApiModelProperty(value = "还款相应信息（还款备注）")
    private String remark;

}
