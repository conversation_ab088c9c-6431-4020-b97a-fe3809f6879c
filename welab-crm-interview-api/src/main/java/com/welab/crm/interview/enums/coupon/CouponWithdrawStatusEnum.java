package com.welab.crm.interview.enums.coupon;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 我的返现卡券状态枚举
 * @date 2021/11/18
 */
public enum CouponWithdrawStatusEnum {

    NOT_EXPIRED("0", "未过期"),
    EXPIRED("1", "已过期"),
    WITHDRAWN("2", "已提现");

    private String value;
    private String text;

    CouponWithdrawStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        for (CouponWithdrawStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum.getText();
            }
        }
        return "";
    }
}
