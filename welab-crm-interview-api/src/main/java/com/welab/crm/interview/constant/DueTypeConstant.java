package com.welab.crm.interview.constant;

/**
 * 资金还款计划表对应的分期费用类型，此枚举列出的各个费用比较全面
 */
public final class DueTypeConstant {

    /**
     * 审批费
     */
    public static final String handlingFee = "handling_fee";
    /**
     * 利息
     */
    public static final String interest = "interest";
    /**
     * 管理费
     */
    public static final String managementFee = "management_fee";
    /**
     * 本金
     */
    public static final String principal = "principal";
    /**
     * 逾期利息
     */
    public static final String overdueInterest = "overdue_interest";
    /**
     * 提现费
     */
    public static final String withdrawalFee = "withdrawal_fee";
    /**
     * 代扣费
     */
    public static final String depositFee = "deposit_fee";
    /**
     * 代扣费
     */
    public static final String collectionFee = "collection_fee";
    /**
     * 逾期管理费
     */
    public static final String overdueManagementFee = "overdue_management_fee";
    /**
     * 逾期罚金
     */
    public static final String overduePenalty = "overdue_penalty";
    /**
     * 逾期罚金
     */
    public static final String overdueFee = "overdue_fee";
    /**
     * 提前还款罚息
     */
    public static final String earlySettleFee = "early_settle_fee";
    /**
     * 催收委外费
     */
    public static final String outsourcingFee = "outsourcing_fee";
    /**
     * 保费
     */
    public static final String insuranceFee = "insurance_fee";
    /**
     * 手机保障费
     */
    public static final String premium = "premium";
    /**
     * 手机买断费
     */
    public static final String buyoutFee = "buyout_fee";
    /**
     * 手机损坏费
     */
    public static final String damageFee = "damage_fee";
    /**
     * 红包
     */
    public static final String coupon = "coupon";
    /**
     * 担保费
     */
    public static final String guaranteeFee = "guarantee_fee";
    /**
     * 风险保障服务费
     */
    public static final String riskServiceFee = "risk_service_fee";
    /**
     * 风险保障金
     */
    public static final String riskMargin = "risk_margin";
    /**
     * 评审服务费
     */
    public static final String assessServiceFee = "assess_service_fee";
    /**
     * 担保咨询服务费
     */
    public static final String guaranteeConsultingFee = "guarantee_consulting_fee";
}
