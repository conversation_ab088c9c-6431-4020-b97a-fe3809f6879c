package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户贷款信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "客户贷款信息响应对象")
public class LoanApplicationVO implements Serializable {

    private static final long serialVersionUID = -5742644938774709230L;

    @ApiModelProperty(value = "主键id", name = "id")
    private Integer id;

    @ApiModelProperty(value = "贷款申请编号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "产品编号", name = "productCode")
    private String productCode;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "申请时间", name = "申请时间")
    private Date appliedAt;

    @ApiModelProperty(value = "申请金额", name = "appliedAmount")
    private BigDecimal appliedAmount;

    @ApiModelProperty(value = "申请期限", name = "applyTenor")
    private String appliedTenor;

    @ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

    @ApiModelProperty(value = "审批金额-状态(state)-(api:审批金额;applied:申请金额))", name = "amount")
    private BigDecimal approvedAmount;

    @ApiModelProperty(value = "审批期限", name = "approvalTenor")
    private String approvedTenor;

    @ApiModelProperty(value = "贷款状态", name = "state")
    private String state;

    @ApiModelProperty(value = "客户确认放款时间", name = "confirmedAt")
    private Date confirmedTime;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    private Date disbursedTime;

    @ApiModelProperty(value = "资金方名称", name = "partnerName")
    private String partnerName;

    @ApiModelProperty(value = "渠道号", name = "origin")
    private String origin;

    @ApiModelProperty(value = "渠道中文名称", name = "originName")
    private String originName;

    @ApiModelProperty(value = "总利率", name = "totalRate")
    private BigDecimal totalRate;

    @ApiModelProperty(value = "贷款号对应的会员订单号", name = "vipOrderNo")
    private String vipOrderNo;

    @ApiModelProperty(value = "结清获取", name = "closeGet")
    private String closeGet;

    @ApiModelProperty(value = "发票获取", name = "invoiceGet")
    private String invoiceGet;

    @ApiModelProperty(value = "关闭授信", name = "closeQuota")
    private String closeQuota;
    
    @ApiModelProperty(value = "模式类型")
    private String loanModelType;
    
    @ApiModelProperty(value = "资金方编码")
    private String partnerCodeNew;

    @ApiModelProperty(value = "债转公司")
    private String transferCompany;

    @ApiModelProperty(value = "底色")
    private String backgroundColor;
    
    @ApiModelProperty(value = "外部订单号")
    private String externalOrderNo;


    @ApiModelProperty(value = "渠道方会员来源")
    private String vipAssetCode;


}
