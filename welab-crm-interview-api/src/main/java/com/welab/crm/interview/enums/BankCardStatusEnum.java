package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/3
 */
public enum  BankCardStatusEnum {

    INIT("init", "初始化"),
    WAIT("wait", "绑卡中"),
    FAILED("failed", "失败"),
    //第三方返回成功状态
    SUCCESS("succeed", "成功"),
    PROCESSING("processing","处理中"),
    UNKNOWN("unknown","状态不明"),
    CHANGE("change","换卡中"),
    DELETE("delete","删除"),
    //返回给前端的状态
    PASS("pass", "成功");

    private String value;
    private String text;

    BankCardStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value){
        for (BankCardStatusEnum status : values()){
            if(status.getValue().equals(value)){
                return status.getText();
            }
        }
        return UNKNOWN.getText();
    }
}
