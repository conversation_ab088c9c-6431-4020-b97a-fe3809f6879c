package com.welab.crm.interview.enums;

public enum ChannelCodeEnum {

	ZABX("zabx", "0021", "众安保险", "zhongan-insurance-investment"),
	PPMONEY("ppmoney", "0017", "PPMoney", "ppmoney-investment"),
	JCBX("jcbx", "0019", "锦程保险", "jincheng-insurance-investment"),
	FENGHUANG("fenghuang", "0018", "凤凰金融", "fenghuang-investment"),
	ZTSSJ("ztssj", "0014", "随手记", "feidee-investment"),
	KAOLALICAI("kaolalicai", "0009", "考拉理财", "kaola-investment"),
	ALLINPAY("allinpay", "0005", "通联", "allinpay-channel"),
	UMPAY("umpay", "0002", "联动优势", "umpay-channel"),
	ALIPAY("alipay", "0000", "支付宝线下", ""),
	YILIAN("yilian", "0001", "易联", "yilian-channel"),
	WEIXIN("weixin", "0003", "微信", "wechat-channel"),
	YUNSHANGTONG("yunshangtong", "0023", "云商通", "yunshangtong-channel"),
	HENGQINALIYUN("hengqin-aliyun", "0020", "横琴（支付宝）", "alipay-renting-investment"),
	JINMEIXIN("jinmeixin", "0026", "金美信", "jinmeixin-investment"),
	YOOLI("yooli", "0024", "有利网", "yooli-investment"),
	ZHONGAN("zhongan", "0006", "众安", "zhongan-investment"),
	JINCHENG("jincheng", "0008", "锦城", "jincheng-investment"),
	JINTOU("hzfi", "0011", "金投", "jintou-investment"),
	TONGBANJIE("tongbanjie", "0013", "铜板街", "tongbanjie-investment"),
	WACAI("wacai", "0027", "挖财", "wacai-investment"),
	JIMU("jimu", "0032", "积木盒子", "jimu-investment"),
	YNTRUST1("yntrust1", "0030", "云南信托-新信托计划", "yntrust-project1-investment"),
	YNTRUST2("yntrust2", "0044", "云南信托2-增信担保模式", "yntrust-project2-investment"),
	YNTRUST3("yntrust3", "0054", "云南信托3-普惠570信托", "yntrust-project2-investment"),
	XCWS("xcws", "0029", "携程网商", ""),
	CYCFC("cycfc","0037","长银消费金额", "cycfc-investment"),
	HUBEICFC("hubeicfc", "0071", "湖北消金irr", "hubeicfc-investment"),
	HUBEICFC1("hubeicfc1", "0072", "湖北消金apr", "hubeicfc-investment"),
	XZTRUST("xztrust","0038","西藏信托", "xztrust-investment"),
	BAOFU("baofu","0046","宝付", "baofu-channel"),
	CTRIP("ctrip", "0042", "携程", "ctrip-channel"),
	XIAOMI("xiaomi", "0040", "小米", "xiaomi-channel"),
	HAIERCFC("haiercfc", "0041", "海尔消金", "haiercfc-investment"),
	HAIERMONEY("haiermoney", "0039", "海尔云贷", "haiermoney-investment"),
	CZCB("czcb", "0048", "稠州银行", "czcb-investment"),
	BOSC("bosc", "0049", "上海银行", "bosc-channel"),
	ALLINPAY_WALLET("allinpay_wallet", "0050", "通联-钱包", "allinpay-channel"),
	HENGQINJXZ("hengqin-jxz", "0053", "横琴（京小租）", "jingxiaozu-channel"),
	XMJMX("xmjmx", "0056","小米金美信", "jmxcfc-investment"),
	HAIERCFC1("haiercfc1", "0057", "海尔消金（IRR36）", "haiercfc-investment"),
	SDTRUST("sdtrust", "0061", "山东信托", "sdtrust-investment"),
	WALLET_GMTRUST("gmtrust", "0063", "钱包国民信托渠道", "gmtrust-investment"),
	BOBCFC("bobcfc", "0052", "北银消金", "bobcfc-investment"),
	FUXING("fuxing", "0059", "复星小贷", "fuxing-investment"),
	WSXMTRUST("wsxmtrust", "0060", "网商厦门信托", "wangshang-xmtrust-investment"),
	XMTRUST("xmtrust", "0062", "厦门信托二号绑卡渠道", "wangshang-xmtrust-investment"),
	JCDB("jcdb", "0064", "锦程担保", "jincheng-guarantee-investment"),
	SDTRUST1("sdtrust1", "0066", "山东信托二期", "sdtrust-investment"),
	HAIERMONEY1("haiermoney1", "0067", "海尔云贷二期irr", "haiermoney-investment"),
	HAIERMONEY2("haiermoney2", "0068", "海尔云贷二期apr", "haiermoney-investment"),
	ZBBANK("zbbank","0043","小米众邦银行", "zbbank-investment"),
	ZBBANK1("zbbank1", "", "众邦银行(非持牌担保)", "zbbank-salary-investment"),
	WEISHENMA("weishenma", "", "微神马irr36", "weishenma-investment"),
	WEISHENMA1("weishenma1", "", "微神马apr36", "weishenma-investment"),
	QSBANK("qsbank", "0069", "齐商银行", "qsbank-investment"),
	HUBEICFC2("hubeicfc2", "0070", "湖北消金apr他收", "hubeicfc-investment"),
	WSWL("wswl", "", "网商玮徕", "wswl-investment"),
	AIBANK("aibank", "0074", "百信银行", "wallet-aibank-channel"),
	DXMPAY("dxmpay", "0073", "度小满", "dxmpay-channel"),
	QSBANK1("qsbank1", "0075", "齐商银行二期", "qsbank-investment"),
	SDTRUST2("sdtrust2", "0076", "山东信托三期", "sdtrust-investment"),
	GMTRUST_INSTALLMENT("gmtrust2", "0079", "钱包国民信托渠道(分期)", "gmtrust-installment-investment"),
	LZBANK("lzbank", "", "兰州银行", "lzbank-investment"),
	WSYC("wsyc", "0077", "网商粤财", "wangshang-yuecai-investment"),
	HUBEICFC3("hubeicfc3", "0080", "湖北消金三期apr他收", "hubeicfc-investment"),
	SDTRUST3("sdtrust3", "0081", "山东信托四期", "sdtrust-investment"),
	SDTRUST4("sdtrust4", "0082", "山东信托五期", "sdtrust-investment"),
	LZBANK1("lzbank1", "", "安硕兰州", "lzbank-anshuo-investment"),
	SDTRUST5("sdtrust5", "0083", "山东信托六期", "sdtrust-investment"),
	SDTRUST6("sdtrust6", "0084", "山东信托七期", "sdtrust-investment"),
	SDTRUST7("sdtrust7", "0094", "山东信托八期", "sdtrust-investment"),
	SDTRUST8("sdtrust8", "0096", "山东信托九期", "sdtrust-investment"),
	WEISHENMA2("weishenma2", "", "微神马中裔担保irr36", "weishenma-investment"),
	WEISHENMA3("weishenma3", "", "微神马中裔担保apr36", "weishenma-investment"),
	QBCYCFC("qbcycfc", "", "钱包长银", "wallet-cycfc-investment"),
	FULONG("fulong", "", "富龙小贷", "fulong-investment"),
	WEISHENMA4("weishenma4", "", "微神马中裔担保昊悦代收apr36", "weishenma-investment"),
	OTHER1("other1", "0091", "其他1", "bobcfc-investment"),
	LZBANK2("lzbank2", "", "安硕兰州irr36", "lzbank-anshuo-investment"),
	OTHER2("other2", "0093", "其他2", "bobcfc-investment"),
	BOBCFC1("bobcfc1", "0092", "北银消金", "bobcfc-investment"),
	BOBCFC3("bobcfc3", "0099", "北银消金", "bobcfc-investment"),
	HUBEICFC4("hubeicfc4", "0085", "湖北消金irr", "hubeicfc-investment"),
	HUBEICFC5("hubeicfc5", "0086", "湖北消金apr", "hubeicfc-investment"),
	CYCFC2("cycfc2","","长银消金中智信", "cycfc1-investment"),
	ALLINPAY2("allinpay2","1005","通联所有支持银行", ""),
	LZBANK3("lzbank3", "", "安硕兰州自营", "lzbank-anshuo-investment"),
	BAOFU2("baofu2","1046","宝付", "baofu-channel"),
	LZBANK4("lzbank4", "", "安硕兰州自营irr", "lzbank-anshuo-investment"),
	HAIERCFC2("haiercfc2","0087", "海尔消金三期irr他收","haiercfc-investment"),
	NJCB("njcb","0089","南京银行","njcb-channel"),
	JCFR("jcfr", "0090", "锦程分润","jincheng-feesplitting-investment"),
	HUIHEBANK("huihebank", "", "汇和银行", "huihebank-investment"),
	ZHONGZHIXIN("zhongzhixin","0095","中智信资方","zhongzhixin-investment"),
	HAIERCFC3("haiercfc3", "0097", "海尔消金四期irr24他收", "haiercfc-investment"),
	SDTRUST10("sdtrust10", "0098", "山东信托十期", "sdtrust-investment"),
	CYCFC3("cycfc3","0004","长银消金中智信irr24", "cycfc2-investment"),
	SDTRUST11("sdtrust11", "0010", "山东信托十一期", "sdtrust-investment"),
	SDTRUST12("sdtrust12", "0016", "山东信托十二期", "sdtrust-investment"),
	JMX("jmx", "0088", "金美信", "jmx-investment"),
	JINMXCFC4("jinmxcfc4", "0100", "金美信四期", "jmx-investment"),
	SDTRUST13("sdtrust13", "0012", "山东信托十四期", "sdtrust-investment"),
	SDTRUST14("sdtrust14", "0025", "山东信托十六期", "sdtrust-investment"),
	SDTRUST15("sdtrust15", "0031", "山东信托十八期", "sdtrust-investment"),
	SDTRUST16("sdtrust16", "0034", "山东信托十三期", "sdtrust-investment"),
	SDTRUST17("sdtrust17", "0045", "山东信托十五期", "sdtrust-investment"),
	SDTRUST18("sdtrust18", "0047", "山东信托十七期", "sdtrust-investment"),
	ZBBANK2("zbbank2", "0051", "众邦银行irr24他收", "zbbank-investment"),
	WPCFC("wpcfc","0078","唯品富邦消金irr24他收","wpcfc-investment"),
	WEISHENMA5("weishenma5", "", "微神马IRR24-中裔", "weishenma-investment"),
	JCDB2("jcdb2", "0028", "锦程担保二期", "jincheng-guarantee-investment"),
	ZBBANK3("zbbank3", "0103", "众邦银行三期irr24他收", "zbbank-investment"),
	OPPO("oppo", "0102", "oppo金融", "oppo-investment"),
	OPPO1("oppo1", "0108", "oppo金融（36费率）", "oppo-investment"),
	YKBANK("ykbank", "0104", "营口银行irr24", "ykbank-investment"),
	YKBANK2("ykbank2", "0105", "营口银行irr36", "ykbank-investment"),
	LHBANK("lhbank", "0106", "蓝海银行irr24我收", "lhbank-investment"),
	LHBANK2("lhbank2", "0107", "蓝海银行irr36我收", "lhbank-investment"),
	CYCFC4("cycfc4","0109","长银消金五期", "cycfc2-investment"),
	JINMXCFC5("jinmxcfc5", "0110", "金美信五期", "jmx-investment"),
	OPPO2("oppo2","0111","OPPO金融24%-xs", "oppo-investment"),
	OPPO3("oppo3","0102","OPPO金融36%-za", "oppo-investment"),
	GUOMINTRUST("guomintrust", "0112", "国民信托一期", "guomintrust-investment"),
	GUOMINTRUST2("guomintrust2", "0113", "国民信托二期", "guomintrust-investment"),
	GUOMINTRUST3("guomintrust3", "0114", "国民信托三期", "guomintrust-investment"),
	HBCFC("hbcfc", "0115", "湖北消金24", "hbcfc-investment"),
	HBCFC1("hbcfc1", "0116", "湖北消金36", "hbcfc-investment"),
	LFBANK("lfbank","0117","廊坊银行","lfbank-investment"),
	BOBCFC4("bobcfc4", "0118", "北银消金", "bobcfc-investment"),
	SNBANK("snbank","0121","苏宁银行24","snbank-investment"),
	SNBANK1("snbank1","0122","苏宁银行36","snbank-investment"),
	WEISHENMA6("weishenma6", "0120", "微神马-大行irr36", "weishenma-investment"),
	CYCFC5("cycfc5","0109","长银消金六期", "cycfc2-investment"),
	WEISHENMA7("weishenma7", "0125", "微神马大行中智信", "weishenma-investment"),
	CABANK("cabank","0119","长安银行irr24","cabank-investment"),
	CABANK2("cabank2","0123","长安银行irr36","cabank-investment"),
	JINMXCFC6("jinmxcfc6", "0124", "金美信六期", "jmx-investment"),
	HTBANK("htbank","0126","华通银行irr36","htbank-investment"),
	HTBANK2("htbank2","0130","华通银行irr24","htbank-investment"),
	YCBANK("ycbank","0128","微神马伊春银海", "ycbank-investment"),
	YCBANK2("ycbank2","0129","微神马伊春玮徕", "ycbank-investment"),
	CABANK3("cabank3","0131","长安银行irr24","cabank-investment"),
	CABANK4("cabank4","0132","长安银行irr36","cabank-investment"),
	WEISHENMA8("weishenma8", "0133", "微神马大行玮徕irr24", "weishenma-investment"),
	;

	public final String channelCode;
	public final String payCode;
	public final String channelname;
	public final String appName;

	ChannelCodeEnum(String channelCode, String payCode, String desc, String appName) {
		this.channelCode = channelCode;
		this.payCode = payCode;
		this.channelname = desc;
		this.appName = appName;
	}

	public static String getChannelCodeByPayCode(String payCode){
		for(ChannelCodeEnum enums : values()){
			if(enums.payCode.equals(payCode)){
				return enums.channelCode;
			}
		}
		return "";
	}

	public static String getChannelNameByChannelCode(String channelCode){
		for(ChannelCodeEnum enums : values()){
			if(enums.channelCode.equals(channelCode)){
				return enums.channelname;
			}
		}
		return "";
	}

	public static String getAppNameByChannelCode(String channelCode){
		for(ChannelCodeEnum enums : values()){
			if(enums.channelCode.equals(channelCode)){
				return enums.appName;
			}
		}
		return "";
	}


	public static String getPayCodeByChannelCode(String channelCode){
		for(ChannelCodeEnum enums : values()){
			if(enums.channelCode.equals(channelCode)){
				return enums.payCode;
			}
		}
		return "";
	}

	/**
	 * 判断是否是公司渠道
	 * @param channelCode
	 * @return true 公司渠道;false 其他渠道
	 */
	public static boolean isWelabChannel(String channelCode){
		if(ChannelCodeEnum.UMPAY.channelCode.equals(channelCode)
				|| ChannelCodeEnum.ALLINPAY.channelCode.equals(channelCode)){
			return true;
		}
		return false;
	}
}
