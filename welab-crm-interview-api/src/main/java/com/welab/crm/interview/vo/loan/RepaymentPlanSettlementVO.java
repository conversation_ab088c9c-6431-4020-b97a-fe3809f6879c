package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "在途贷款还款明细结算信息响应对象")
public class RepaymentPlanSettlementVO implements Serializable {

    private static final long serialVersionUID = 595225199956703863L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "交易号")
    private String transId;

    @ApiModelProperty(value = "应还金额")
    private BigDecimal dueAmount;

    @ApiModelProperty(value = "实还金额")
    private BigDecimal settledAmount;

    @ApiModelProperty(value = "还款项目")
    private String dueType;

    @ApiModelProperty(value = "还款计划")
    private Integer index;

    @ApiModelProperty(value = "还款时间")
    private Date settledTime;

    @ApiModelProperty(value = "还款状态")
    private String state;
}
