package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/8/16 15:40
 */
public enum DueState {
    SETTLED("settled", "结清"),
    OVERDUE("overdue", "逾期"),
    OPEN("open", "待还");

    private String m_value;
    private String m_text;

    DueState(String value, String text) {
        m_value = value;
        m_text = text;
    }

    public String getValue() {
        return m_value;
    }

    public String getText() {
        return m_text;
    }
}
