package com.welab.crm.interview.enums;

/**
 * 联系人关系枚举
 * <AUTHOR>
 */
public enum RelationshipEnum {

    PARENTS("parents","父母"),
    spouse("spouse","配偶"),
    sibling("sibling","兄弟姐妹"),
    friend("friend","朋友"),
    colleague("colleague","同事"),
    children("children","子女"),
    relative("relative","亲属"),
    unknown("unknown","未知")
    ;
    private String value;

    private String desc;

    RelationshipEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(String value){
        for (RelationshipEnum relationshipEnum : values()) {
            if (relationshipEnum.value.equals(value)){
                return relationshipEnum.desc;
            }
        }
        return value;
    }

}
