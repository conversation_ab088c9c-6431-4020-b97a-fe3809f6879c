package com.welab.crm.interview.dto.ai;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class AiCustomerDTO {

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 客户性别
     */
    private String sex;

    /**
     * 客户手机号码
     */
    private String phone;

    /**
     * 额外的必要参数，比如ai外呼任务id和电销任务唯一id
     */
    private String extraParam;

    /**
     * 由运营传过来的，ai需要的参数
     */
    private Map<String,String> aiPushParams;
}
