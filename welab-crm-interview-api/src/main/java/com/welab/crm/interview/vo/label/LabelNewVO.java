package com.welab.crm.interview.vo.label;

import lombok.Data;

@Data
public class LabelNewVO {

    /**
     * 审核通过时间
     */
    private Long checkTime;
    /**
     * 标签码
     */
    private String code;
    /**
     * 标签类别
     */
    private String typeCode;
    /**
     * 有效期
     */
    private Long validTime;
    /**
     * 标签码优先级
     */
    private Integer priority;
    /**
     * 标签名称
     */
    private String name;
}
