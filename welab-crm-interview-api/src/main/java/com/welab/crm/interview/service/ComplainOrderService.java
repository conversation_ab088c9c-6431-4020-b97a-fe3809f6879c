package com.welab.crm.interview.service;

import com.welab.collection.interview.dto.complain.ComplainDTO;
import com.welab.collection.interview.dto.complain.ComplainResultDTO;
import com.welab.collection.interview.vo.complain.ComplainDictVO;
import com.welab.collection.interview.vo.complain.CustomerBlackListVO;

import java.util.List;

/**
 * 处理投诉工单的接口服务
 */
public interface ComplainOrderService {

    /**
     * 根据工单的分类判断是否需要投诉级别
     *
     * @param complainDTO 合同号列表, 工单编号等数据
     */
    void pushComplainOrder(ComplainDTO complainDTO);

    /**
     * 更新催收系统传递过来的投诉处理结果信息(更新主键是工单编号)
     */
    void returnResultForUpdate(ComplainResultDTO resultDTO);

    /**
     * 工单投诉批量处理状态催促, 推送到催收系统
     *
     * @param orderNoList 工单编号列表
     */
    void updateComplainUrgeStatus(List<String> orderNoList);

    /**
     * 增量更新投诉相关的文件列表
     */
    void updateComplainFileList(ComplainDTO complainDTO);

    /**
     * 获取客服投诉相关的核实结果和处理结果的字典配置数据
     */
    ComplainDictVO getComplainDictList();

    /**
     * 获取客服黑名单信息
     */
    List<CustomerBlackListVO> getUserBlackList(Integer userId, String mobile);
}