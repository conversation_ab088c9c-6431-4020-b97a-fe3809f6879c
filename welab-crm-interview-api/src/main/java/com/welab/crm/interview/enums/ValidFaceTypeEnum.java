package com.welab.crm.interview.enums;

/**
 * 人脸类型的枚举类
 */
public enum ValidFaceTypeEnum {

    SMS("telephone", "短信"),
    ONLINE("online", "在线");

    ValidFaceTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private final String value;
    private final String desc;

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByValue(String value) {
        for (ValidFaceTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type.desc;
            }
        }
        return value;
    }
}
