package com.welab.crm.interview.service;

import com.welab.crm.interview.dto.coupon.CouponCardDTO;
import com.welab.crm.interview.dto.coupon.CouponCardSendDTO;
import com.welab.crm.interview.dto.coupon.CouponSendLogDTO;
import com.welab.crm.interview.vo.coupon.CouponCardVO;
import com.welab.crm.interview.vo.coupon.CouponSendLogVO;
import com.welab.crm.interview.vo.coupon.CouponVO;
import com.welab.crm.interview.vo.coupon.CouponWithdrawVO;
import com.welab.marketing.vo.PagedResponse;
import com.welab.xdao.context.page.Page;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 卡券服务
 * @date 2021/10/28
 */
public interface CouponService {

    /**
     * 获得用户红包赠送记录
     *
     * @param uuid
     * @return
     */
    List<CouponVO> getUserCouponList(Long uuid);

    /**
     * 分页查询可赠送的用户红包
     *
     * @param dto
     * @return
     */
    PagedResponse<CouponCardVO> getUserCardPage(CouponCardDTO dto);

    /**
     * 分页查询卡券全局监控
     *
     * @param dto
     * @return
     */
    PagedResponse<CouponCardVO> couponGlobalMonitoring(CouponCardDTO dto);

    /**
     * 卡券全局监控所有数据
     * @param dto
     * @return
     */
    List<CouponCardVO> getCouponGlobalMonitoringList(CouponCardDTO dto);

    /**
     * 批量发送卡券
     *
     * @param couponId   卡券id
     * @param uuid 客户uuid列表
     * @return 以前已经领取过的客户uuid
     */
    @Deprecated
    List<Long> sendCoupon(Long couponId, List<Long> uuid);

    /**
     * 批量发送卡券，新版本
     *
     * @param dto
     * @return 以前已经领取过的客户uuid
     */
    List<Long> sendCoupon(CouponCardSendDTO dto);

    /**
     * 分页查询卡券发送记录
     *
     * @param dto
     * @return
     */
    Page<CouponSendLogVO> getSendLog(CouponSendLogDTO dto);

    /**
     * 卡券发送记录所有数据
     * @param dto
     * @return
     */
    List<CouponSendLogVO> getSendLogList(CouponSendLogDTO dto);

    /**
     * 返现红包使用详情
     *
     * @param uuid
     * @return
     */
    CouponWithdrawVO getWithdrawCoupons(Long uuid);
}
