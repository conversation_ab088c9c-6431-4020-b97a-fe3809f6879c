package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "在途贷款交易记录响应对象")
public class LoanRecordVO implements Serializable {

    private static final long serialVersionUID = 846032816833856246L;

    @ApiModelProperty(value = "批次号")
    private String transId;

    @ApiModelProperty(value = "交易类型")
    private String transType;

    @ApiModelProperty(value = "备注（失败原因释义）")
    private String remark;

    @ApiModelProperty(value = "代扣分类（失败原因分类）")
    private String errorType;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal transAmount;

    @ApiModelProperty(value = "发起时间（交易时间）")
    private Date beginTime;

    @ApiModelProperty(value = "完成时间")
    private Date endTime;

    @ApiModelProperty(value = "处理状态")
    private String status;

    @ApiModelProperty(value = "支付渠道")
    private String paymentChannel;
    
    @ApiModelProperty(value = "还款来源")
    private String repayOrigin;

    @ApiModelProperty(value = "扣款银行")
    private String accountNo;

    @ApiModelProperty(value = "扣款卡号")
    private String payBank;

    @ApiModelProperty(value = "扣款通道")
    private String debitChannelCode;

    public enum RepayErrorMsg {
        PERMISSIONS("权限问题", "开通,失败,拒绝,代收付,不予,代扣", "该卡交易拒绝、暂不支持信用卡交易、该卡有风险，" +
            "发卡行限制交易、发卡行拒付、银联风险受限、渠道不支持交易无法支持、查开户方原因"),
        IDENTITY("身份信息有误", "不一致,过期,留存,更换,预留,户名,不匹配", "手机号、身份证号码、姓名与开户时登记的不一致\n" +
            "持卡人身份信息、手机号或CVN2输入不正确\n" +
            "持卡人身份证已过期\n" +
            "账号户名不符"),
        CHANNEL_REASON("渠道不支持", "渠道,不支持", "支付渠道不支持，需换卡"),
        CARD_PROBLEM("卡问题", "不允许,卡,误,状态,联系,冻结,开户,无此账户,挂失,未激活,销户,签约,未绑定,存管", "卡号:" +
            "无效、卡号不合法、过期、不支持该业务、睡眠卡\n账户异常:信息错误、关户、被冻结、银行黑名单、" +
            "被禁止发起交易、不支持交易、挂失状态/暂时禁用等状态异常、" +
            "账户被锁、已注销、户名证件号不符\n基于风险控制原因失败\n" +
            "接收方账户状态问题:例如被锁定、挂失、止付、未激活"),
        OVERTIME("通讯异常", "系统,维护,稍后,超时,重复", "宝付加密异常/宝付请求异常/系统内部异常/网络异常"),
        UPPER_LIMIT("超额超次数", "上限,次数,限额,超限,超过,超出,限制", "单卡单日余额不足次数超限"),
        NO_REASON("无具体原因", "无具体原因", "详细咨询发卡行/重新支付\n" +
            "格式错误、流水不存在、订单不存在、风险交易拒绝、无此交易渠道、原交易不存在/不成功、商户号未登记、商户信息未找到"),
        BALANCE_NOT_ENOUGH("余额不足", "余额,不足", "余额不足或超限");

        private String m_key;
        private String m_value;
        private String m_text;

        private RepayErrorMsg(String key, String value, String text) {
            m_key = key;
            m_value = value;
            m_text = text;
        }

        public String getKey() {
            return m_key;
        }

        public String getValue() {
            return m_value;
        }

        public String getText() {
            return m_text;
        }

        public static RepayErrorMsg getErrorValue(String value) {
            for (RepayErrorMsg msg : values()) {
                for (String s : Arrays.asList(msg.m_value.split(","))) {
                    if (value.indexOf(s) != -1) {
                        return msg;
                    }
                }
            }
            return null;
        }
    }
}
