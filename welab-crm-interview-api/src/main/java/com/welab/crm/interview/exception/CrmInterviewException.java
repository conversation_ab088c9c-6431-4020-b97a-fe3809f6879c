package com.welab.crm.interview.exception;

import com.welab.common.exception.WeLabException;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/27
 */
public class CrmInterviewException extends WeLabException {

    public CrmInterviewException() {
        super();
    }

    public CrmInterviewException(String message, Throwable cause) {
        super(message, cause);
    }

    public CrmInterviewException(Throwable cause) {
        super(cause);
    }

    public CrmInterviewException(final String message) {
        super("999", message);
    }

    public CrmInterviewException(final String code, final Object... args) {
        super(code, args);
    }

    public CrmInterviewException(final String code, final String message, final Object... args) {
        super(code, message, args);
    }
}
