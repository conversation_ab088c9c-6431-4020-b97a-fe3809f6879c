package com.welab.crm.interview.util;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PublicKey;
import java.security.PrivateKey;
import java.util.Base64;

public class KeyPairGeneration {
    public static void main(String[] args) throws Exception {
        // 1. 创建密钥对生成器
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");

        // 2. 初始化生成器并指定密钥大小
        keyPairGenerator.initialize(2048);

        // 3. 生成密钥对
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 4. 从密钥对中获取公钥和私钥
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        // 5. 将公钥和私钥转为字符串表示形式
        String publicKeyString = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        String privateKeyString = Base64.getEncoder().encodeToString(privateKey.getEncoded());

        System.out.println("String publicKey = \"" + publicKeyString + "\";");
        System.out.println("String privateKey = \"" + privateKeyString + "\";");
    }
}