package com.welab.crm.interview.vo.workorder;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工单通知VO
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class WorkOrderNoticeVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * wo_task 表id
	 */
	private Long id;


	/**
	 * 客服姓名
	 */
	private String staffName;
	/**
	 * 工单类型
	 */
	private String orderType;
	/**
	 * 工单三类
	 */
	private String orderThreeType;

	/**
	 * 用户uuid
	 */
	private String uuid;


	/**
	 * 分单时间
	 */
	private Date distributeTime;

	/**
	 * 上次联系时间
	 */
	private Date lastContactTime;

	/**
	 * 工单创建时间
	 */
	private Date orderCreateTime;

	/**
	 * 客户姓名
	 */
	private String customerName;

	/**
	 * 工单是否超时未联系
	 */
	private Boolean responseTimeOut;
	
}
