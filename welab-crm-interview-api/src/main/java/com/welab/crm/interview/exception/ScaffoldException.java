package com.welab.crm.interview.exception;

import com.welab.common.exception.WeLabException;

/**
 * @description 在项目中定义各自的异常并继承WelabException，并且在业务实现层XxxServiceImpl或者Controller层抛出，解决RPC调用时Dubbo对非Jar包内RuntimeException包装成String的问题
 *
 * <AUTHOR>
 * @date 2018-05-22 14:39:23
 * @version v1.0
 */

public class ScaffoldException extends WeLabException {

	private static final long serialVersionUID = 1L;
	
	public ScaffoldException() {
		super();
	}

	public ScaffoldException(String message, Throwable cause) {
		super(message, cause);
	}

	public ScaffoldException(Throwable cause) {
		super(cause);
	}

	public ScaffoldException(final String code) {
		super(code);
	}
	
	public ScaffoldException(final String code, final Object... args) {
		super(code, args);
	}
	
	public ScaffoldException(final String code, final String message, final Object... args) {
		super(code, message, args);
	}
	
}
