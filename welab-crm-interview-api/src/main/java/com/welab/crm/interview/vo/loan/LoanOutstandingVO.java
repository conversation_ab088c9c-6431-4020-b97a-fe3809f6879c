package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "在途贷款响应对象")
public class LoanOutstandingVO implements Serializable{

    private static final long serialVersionUID = -6123654053363043409L;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "贷款金额", name = "amount")
    private BigDecimal amount;

    @ApiModelProperty(value = "期数，剩余期数除以总期数", name = "tenor")
    private String tenor;

    @ApiModelProperty(value = "当期应还", name = "currBalance")
    private BigDecimal currBalance;

    @ApiModelProperty(value = "到期日期", name = "deadDate")
    private Date deadDate;

    @ApiModelProperty(value = "全额还款", name = "settleAllAmount")
    private BigDecimal settleAllAmount;

    @ApiModelProperty(value = "全额还款组成", name = "settleAllAmountDetail")
    private String settleAllAmountDetail;

    @ApiModelProperty(value = "支付渠道", name = "paymentChannel")
    private String paymentChannel;

    @ApiModelProperty(value = "资金方", name = "partnerName")
    private String partnerName;

    @ApiModelProperty(value = "资金方Code", name = "partnerCode")
    private String partnerCode;

    @ApiModelProperty(value = "是否支持提前结清", name = "isAllowEarlySettle")
    private String isAllowEarlySettle;

    @ApiModelProperty(value = "确认放款时间", name = "confirmedAt")
    private Date confirmedAt;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;


    @ApiModelProperty(value = "贷款状态", name = "state")
    private String state;

    @ApiModelProperty(value = "渠道号", name = "origin")
    private String origin;

    @ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

    @ApiModelProperty(value = "总利率", name = "totalRate")
    private BigDecimal totalRate;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    private Date disbursedTime;

    @ApiModelProperty(value = "逾期天数", name = "overdueDay")
    private Integer overdueDay;

    @ApiModelProperty(value = "核销", name = "writeoff")
    private String writeoff;
    
    @ApiModelProperty(value = "还款时间", name = "repayTime")
    private String repayTime;

    /**
     * 名单类型（0:委外,1:债转,2-法诉)
     */
    @ApiModelProperty(value = "名单标签", name = "type")
    private String type;
    
    @ApiModelProperty(value = "公司名称", name = "companyName")
    private String companyName;
    
    @ApiModelProperty(value = "公司电话", name = "companyTel")
    private String companyTel;
    
    @ApiModelProperty(value = "债转时间", name = "deptDate")
    private Date deptDate;

    @ApiModelProperty(value = "法诉时间范围", name = "timeRange")
    private String timeRange;


    @ApiModelProperty(value = "模式类型")
    private String loanModelType;

    @ApiModelProperty(value = "资金方编码")
    private String partnerCodeNew;
}
