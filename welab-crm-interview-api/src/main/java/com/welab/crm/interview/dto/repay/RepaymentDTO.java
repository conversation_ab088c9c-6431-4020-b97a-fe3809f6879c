/**
 * @Title: RepaymentCalculateDTO.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.dto.repay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO 功能描述
 * @date 2019/4/17 10:32
 */
@Data
@ApiModel("代扣请求实体类")
public class RepaymentDTO implements Serializable {
	private static final long serialVersionUID = -834583903980663859L;

	@NotNull(message = "userId不能为空")
    @ApiModelProperty(value = "用户Id", name = "userId")
	private Integer userId;
    
    @NotEmpty(message = "repaymentMode不能为空")
    @ApiModelProperty(value = "代扣模式；YD:按期还款；YH:还清全部逾期", name = "repaymentMode")
    private String repaymentMode;

    @NotEmpty(message = "applicationId不能为空")
    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "还款来源，前端不用赋值", name = "repayOrigin")
    private String repayOrigin;

    @ApiModelProperty(value = "还款服务号，前端不用赋值", name = "serviceNo")
    private String serviceNo;

    @ApiModelProperty(value = "客服系统Id", name = "customerId")
    private Long customerId;

    @ApiModelProperty(value = "代扣金额", name = "amount")
    private String amount;

    @ApiModelProperty(value = "指定银行卡id")
    private Long bankCardId;

    
}
