package com.welab.crm.interview.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class ConPhoneSummaryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客服系统电话小结表主键Id
     */
    private Long mainId;

    /**
     * 客服系统客户表主键Id
     */
    private Long customerId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 客户userId
     */
    private Long userId;

    /**
     * 呼入系统时间
     */
    private Date cdrStartTime;

    /**
     * 保存小结时间
     */
    private Date saveSummaryTime;

    /**
     * 接听的员工坐席号
     */
    private String cdrCalleeCno;

    /**
     * 电话小结内容
     */
    private String callSummary;

    /**
     * 备注
     */
    private String callComment;

    /**
     * 创建人(发送员工staffId)
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     * 呼叫类型
     */
    private String cdrCallType;
}
