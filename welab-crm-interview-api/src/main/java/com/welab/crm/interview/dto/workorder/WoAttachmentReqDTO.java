package com.welab.crm.interview.dto.workorder;

import com.welab.crm.interview.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 工单附件保存DTO
 * <AUTHOR>
 * @date 2021/12/22 11:34
 */

@Data
public class WoAttachmentReqDTO implements Serializable {

    private static final long serialVersionUID = -5664737425969824471L;


    @ApiModelProperty(value = "附件名", name = "fileName")
    private List<WoAttachmentVO> fileList;

    @ApiModelProperty(value = "工单表业务主键", name = "id")
    private Long id;
}
