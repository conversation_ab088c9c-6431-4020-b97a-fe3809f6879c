package com.welab.crm.interview.service;

import com.welab.common.response.Response;

import java.util.List;
import java.util.Map;

/**
 * @Description: oss相关
 * <AUTHOR>
 * @date 2020/07/29
 */
public interface IUploadService {

	/**获取文件路径
	 * @param fileList
	 * @return
	 */
	Map<String, Object> getUploadFile(List<String> fileList);


	/**
	 * 获取文件下载路径，指定存活时间
	 * @param fileList
	 * @param seconds 链接有效时间，单位：秒
	 * @return
	 */
	Map<String, Object> getUploadFileSurvivalTime(List<String> fileList, long seconds);
	/**
	 * 上传图片
	 * @param bytes
	 * @param fileName
	 * @return
	 */
	Response<String> uploadFile(byte[] bytes,String fileName);
	/**
	 * 获取阿里云资源
	 * @param fileName
	 * @return
	 */
	Response<byte[]> getFile(String fileName);

	/**
	 * 获取带水印文件
	 * @param path 文件下载地址
	 * @param fileName 文件名
	 * @param watermarkContent 水印内容
	 * @return
	 */
	Response<byte[]> getWatermarkFile(String path, String fileName, String watermarkContent);

	/**
	 * 获取带水印文件->贷款合同专用
	 * @param path 文件下载地址
	 * @param fileName 文件名
	 * @param watermarkContent 水印内容
	 * @return
	 */
	Response<byte[]> getWatermarkFileByAppNo(String path, String fileName, String watermarkContent);
    
}
