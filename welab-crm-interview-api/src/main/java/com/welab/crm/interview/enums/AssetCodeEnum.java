package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 渠道来源枚举
 */
public enum AssetCodeEnum {
    GMYK_CREDIT("gmyk_credit", "国美"),
    MEITU("meitu", "美图"),
    MSXF("msxf", "马上消费金融"),
    OPPO("oppo", "oppo"),
    QIANXIAOLE("qianxiaole", "钱小乐"),
    SUNING("suning", "苏宁"),
    TIANCHENG("tiancheng", "甜橙"),
    TONGCHENG58("tongcheng58", "58"),
    VIVO("vivo", "vivo"),
    WEIBO("weibo", "微博"),
    WEIDIAN("weidian", "微店"),
    XIAOMISEMI("xiaomisemi", "小米"),
    ZHIXIN360("zhixin360", "360"),
    ZHONGAN("zhongan", "众安"),
    RONGSHU("rongshu","百融榕树"),
    WELAB("welab", "自营");

    private final String code;
    private final String name;

    AssetCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取数据源名称
     * @param code 数据源代码
     * @return 数据源名称，如果未找到则返回原code
     */
    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return code;
        }
        for (AssetCodeEnum dataSource : values()) {
            if (dataSource.getCode().equals(code)) {
                return dataSource.getName();
            }
        }
        return code;
    }

    /**
     * 根据name获取数据源代码
     * @param name 数据源名称
     * @return 数据源代码，如果未找到则返回null
     */
    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (AssetCodeEnum dataSource : values()) {
            if (dataSource.getName().equals(name)) {
                return dataSource.getCode();
            }
        }
        return null;
    }

    /**
     * 判断是否是自营数据源
     * @param code 数据源代码
     * @return true 自营数据源; false 其他数据源
     */
    public static boolean isWelabDataSource(String code) {
        return WELAB.getCode().equals(code);
    }
}
