package com.welab.crm.interview.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 客户特权卡信息
 */
@Getter
@Setter
@ApiModel(value = "用户特权卡信息响应对象")
public class PrivilegeCardVO implements Serializable {

    /**
     * 是否拥有特权卡
     */
    @ApiModelProperty(value = "是否拥有特权卡")
    private boolean cardUser;

    /**
     * 会员开通日期
     */
    @ApiModelProperty(value = "会员开通日期")
    private String cardCreatedAt;

    /**
     * 会员失效日期
     */
    @ApiModelProperty(value = "会员失效日期")
    private String cardExpiryAt;

    /**
     * 卡类型：特权卡或者至尊卡
     */
    @ApiModelProperty(value = "卡类型")
    private String cardType;


    /**
     * 标签名称列表
     */
    @ApiModelProperty(value = "会员标签列表")
    private List<String> labelName;
}
