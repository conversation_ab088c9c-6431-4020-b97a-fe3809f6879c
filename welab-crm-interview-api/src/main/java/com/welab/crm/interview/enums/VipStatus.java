package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 会员状态枚举类
 * @date 2021/11/15
 */
public class VipStatus {

    public enum PaymentStatusEnum {
        SUCCEED("succeed", "支付成功"),
        FAILED("failed", "支付失败"),
        PROCESSING("processing", "支付中"),
        CANCEL("cancel","取消");

        private String value;

        private String text;

        private PaymentStatusEnum(String value, String text) {
            this.value = value;
            this.text = text;
        }

        public String getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(String status) {
            if(StringUtils.isNotBlank(status)){
                for (PaymentStatusEnum statusEnum : values()) {
                    if (statusEnum.getValue().equals(status)) {
                        return statusEnum.getText();
                    }
                }
            }
            return "";
        }
    }

    public enum RefundStatusEnum {
        SUCCEED("succeed", "退款成功"),
        FAILED("failed", "退款失败"),
        PROCESSING("processing", "退款中"),
        REVIEWING("reviewing", "待复核"),
        APPROVAL("approval", "待审批");

        private String value;

        private String text;

        private RefundStatusEnum(String value, String text) {
            this.value = value;
            this.text = text;
        }

        public String getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(String status) {
            if(StringUtils.isNotBlank(status)){
                for (RefundStatusEnum statusEnum : values()) {
                    if (statusEnum.getValue().equals(status)) {
                        return statusEnum.getText();
                    }
                }
            }
            return "";
        }
    }
}
