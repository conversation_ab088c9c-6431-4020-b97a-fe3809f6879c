package com.welab.crm.interview.dto.ai;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AiTmkConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务Id(对应ai外呼推送配置表的主键id)
     */
    private Long id;

    /**
     * 推送方式对应的数量值，当按时间时此值表示小时数，当按数量时表示个数
     */
    private Integer number;

    /**
     * 推送开始时间节点
     */
    private Integer startHour;

    /**
     * 推送结束时间节点
     */
    private Integer endHour;

    /**
     * cron表达式
     */
    private String cron;
}
