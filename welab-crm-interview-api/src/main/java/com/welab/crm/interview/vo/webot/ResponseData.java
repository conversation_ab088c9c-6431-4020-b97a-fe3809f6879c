package com.welab.crm.interview.vo.webot;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class ResponseData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人脸验证链接唯一标识
     */
    private String token;

    /**
     * 返回类型: telephone: 电话客服, online: 在线客服
     */
    private String source;

    /**
     * 发送组
     */
    private String group;

    /**
     * 发送人
     */
    @JSONField(name = "service_user")
    private String serviceUser;

    /**
     * 验证链接生成时间，列表中数据按create_time倒序排列
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 验证次数
     */
    private Integer count;

    /**
     * 最终结果: 0-成功   1-失败  2-未验证
     */
    private Integer code;

    /**
     * 最终结果的中文
     */
    private String  codeDesc;

    /**
     * 详情列表数据
     */
    private List<ResponseDetail> detail;
}
