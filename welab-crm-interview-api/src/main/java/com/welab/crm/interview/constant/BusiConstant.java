package com.welab.crm.interview.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 业务常量类
 */
public class BusiConstant {

    /**
     * 钱包分期模式订单号前缀
     */
    public static final String WL = "WL";


    /**
     * 钱包账单模式模式订单号前缀
     */
    public static final String BL = "BL";


    /**
     * 客服呼叫中心编码
     */
    public static final List<String> ENTERPRISE_IDS = new ArrayList<>(Arrays.asList("7600080", "7600088","7600104"));
    
    //客服更新
    public static final String KEFU_MODIFY_MOBILE = "kefu_modify_mobile";
    //客服更新
    public static final String KEFU_MODIFY_NAME = "kefu_modify_name";
    //客服注销
    public static final String KEFU_BLOCK_USER = "kefu_block_user";
}
