package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 钱包查询额度授信历史数据
 * @date 2021/10/28
 */
@Data
@ApiModel(value = "钱包查询额度授信历史数据响应对象")
public class WalletGrantHistoryVO implements Serializable {

    private static final long serialVersionUID = 8010036373348234064L;

    @ApiModelProperty(value = "授信额度")
    private BigDecimal amount;

    @ApiModelProperty(value = "额度类型code")
    private String quotaType;

    @ApiModelProperty(value = "审批号")
    private String aipNo;

    @ApiModelProperty(value = "授信类型。user_apply 用户申请，inner_credit 内部授信")
    private String creditType;

    @ApiModelProperty(value = "操作类型")
    private String opType;

    @ApiModelProperty(value = "授信是否已撤销")
    private Boolean revoked;

    @ApiModelProperty(value = "审批时间")
    private String gmtCreate;

    @ApiModelProperty(value = "审批员")
    private String changeBy;
}

