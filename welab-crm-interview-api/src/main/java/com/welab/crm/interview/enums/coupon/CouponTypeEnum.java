package com.welab.crm.interview.enums.coupon;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 卡券类型
 * @date 2021/12/1
 */
public enum CouponTypeEnum {

    AMOUNT("amount", "免息红包"),
    PERCENT("percent", "卡券"),
    REDUCE_RATE("reduce_rate", "减息卡"),
    FREE_LIMIT("free_limit", "免息额度卡"),
    ACTIVITY("activity_withdraw_amount", "现金红包"),
    WITHDRAW_AMOUNT("withdraw_amount", "提现红包"),
    WX_AMOUNT("wx_amount", "微信消费抵扣红包"),
    FREE_LIMIT_HALF("free_limit_half", "免息额度半月卡"),
    ZFB_AMOUNT("zfb_amount", "支付宝消费抵扣红包"),
    FREE_LIMIT_WITHDRAW_AMOUNT("free_limit_withdraw_amount", "免息额度提现"),
    PERIOD_AMOUNT("period_amount", "红包分期抵扣券"),
    PERIOD_FREE_LIMIT("period_free_limit", "免息额度分期抵扣券"),
    ;

    private String value;
    private String text;

    CouponTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        for (CouponTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum.getText();
            }
        }
        return "";
    }
}
