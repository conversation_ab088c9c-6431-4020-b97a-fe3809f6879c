package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 放款中贷款状态枚举类
 * @date 2021/10/29
 */
public enum DisbursedLoanStateEnum {

    UNSETTLED("unsettled", "未结清"),
    SETTLED("settled", "已结清"),
    ;

    private String value;
    private String text;

    DisbursedLoanStateEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
