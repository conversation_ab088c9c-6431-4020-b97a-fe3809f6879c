package com.welab.crm.interview.dto;

import com.welab.crm.interview.vo.LiaisonVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description 钱包项目用户信息DTO
 * <AUTHOR>
 * @date 2019-10-29 10:05:49
 * @version v1.0
 */
@Data
public class WalletUserDTO implements Serializable {
    private static final long serialVersionUID = 3357199229146905002L;


    private Long id;                    // 客服库内的用户Id
    private Integer userId;                 //用戶Id
    private String name;                //姓名
    private String cnid;                //身份证
    private String mobile;              //手机号
    private Boolean block;            //用户状态(false:正常;true:注销;null:用户不存在)
    private String uuid;                //uuid
    private String degree;              //学历
    private String currentAddr;         //居住地址
    private Integer refereeId;          //注册邀请ID
    private String registerAt;          //注册时间
    private String origin;              //注册渠道
    private Integer userScore;          //用户积分
    private Boolean isAlipayAuth;       //支付宝授权
    private Boolean isCarrierAuth;      //运营商授权
    private Boolean agent;              //是否代理
    private String bankDate;            //绑卡时间
    private String elecCardNo;          //二类户账户
    private BigDecimal creditLine;      //信用额度
    private BigDecimal avlCreditLine;   //可用余额
    private String state;               //额度状态
    private Integer age;                //年龄
    private String gender;              //性别
    private Boolean isConsume;          //是否消费
    private List<LiaisonVo> liaisons;
    /**
     * 额度状态英文名
     */
    private String creditStateCode;

    private Boolean isOverdue;

    /**
     * 钱包是否逾期
     */
    private Boolean walletIsOverdue;

    /**
     * 手机号脱敏字段
     */
    private String mobileMask;

    /**
     * 黑名单有效期区间
     */
    private String blackListExpiry;
}
