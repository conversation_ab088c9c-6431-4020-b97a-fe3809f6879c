package com.welab.crm.interview.dto.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/17
 */
@Data
@ApiModel(value = "绑卡功能查询请求对象")
public class BankCardMatchDTO implements Serializable {

    private static final long serialVersionUID = -9214332053011796546L;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "贷款期数")
    private String tenor;

    @ApiModelProperty(value = "贷款金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "产品类型")
    private String productCode;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "渠道号")
    private String origin;
}
