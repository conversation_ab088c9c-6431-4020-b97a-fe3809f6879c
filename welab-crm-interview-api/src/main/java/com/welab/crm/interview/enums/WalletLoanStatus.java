package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 钱包贷款状态枚举类
 * @date 2021/10/28
 */
public enum WalletLoanStatus {
    CONFIRMED("confirmed", "已确认"),
    DISBURSING("disbursing", "放款中"),
    DISBURSED("disbursed", "已放款"),
    CANCELLED("cancelled", "已取消"),
    CLOSED("closed", "贷款完结"),
    EARLYSETTLED("early_settled", "提前结清"),
    REFUNDED("refunded", "已退款");

    private String value;
    private String text;

    WalletLoanStatus(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        for (WalletLoanStatus code : values()) {
            if (code.value.equals(value)) {
                return code.getText();
            }
        }
        return null;
    }
}
