package com.welab.crm.interview.dto.coupon;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 */
@Data
@ApiModel(value = "查询可赠送红包请求对象")
public class CouponCardDTO extends PageQueryDTO {

    private static final long serialVersionUID = 8826635721240087690L;

    @ApiModelProperty(value = "开始时间, 格式为yyyy-MM-dd")
    private String start;

    @ApiModelProperty(value = "结束时间, 格式为yyyy-MM-dd")
    private String end;

    @ApiModelProperty(value = "部门,kefu 客服,dianxiao 电销")
    private String dept;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务类型, loan 现金贷, wallet 钱包")
    private String businessType;

    @ApiModelProperty(value = "卡劵ID")
    private Long id;

    @ApiModelProperty(value = "卡劵类型")
    private String amountType;

    @ApiModelProperty(value = "面值")
    private Float amount;
}
