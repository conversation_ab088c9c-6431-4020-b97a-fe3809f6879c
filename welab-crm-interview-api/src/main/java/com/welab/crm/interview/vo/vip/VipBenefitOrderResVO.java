package com.welab.crm.interview.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户权益返回对象
 * <AUTHOR>
 */
@Data
@ApiModel("用户权益返回对象")
public class VipBenefitOrderResVO implements Serializable {
    private static final long serialVersionUID = 6527039352934268026L;

    /**
     * 橡树权益订单号
     */
    @ApiModelProperty(name = "orderNumber",value = "橡树权益订单号")
    private String orderNumber;

    /**
     * 权益名称
     */
    @ApiModelProperty(name = "benefitName",value = "权益名称")
    private String benefitName;

    /**
     * 权益成本
     */
    @ApiModelProperty(name = "cost",value = "权益成本")
    private BigDecimal cost;

    /**
     * 权益兑换日期
     */
    @ApiModelProperty(name = "redeemedAt",value = "权益兑换日期")
    private Date redeemedAt;

    /**
     * 会员套餐总金额
     */
    @ApiModelProperty(name = "orderAmount",value = "会员套餐总金额")
    private BigDecimal orderAmount;
}
