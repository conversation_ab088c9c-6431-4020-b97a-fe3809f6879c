package com.welab.crm.interview.dto.wallet;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
@Data
@ApiModel(value = "钱包交易记录请求对象")
public class WalletOrderRecordDTO extends PageQueryDTO {

    @ApiModelProperty(value = "userId")
    private Long userId;
}
