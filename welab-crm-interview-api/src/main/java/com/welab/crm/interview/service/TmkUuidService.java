package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.tmk.PushUuidDTO;
import com.welab.crm.interview.dto.tmk.PushUuidQueryDTO;
import com.welab.crm.interview.dto.tmk.TmkUuidConfigDTO;
import com.welab.crm.interview.vo.oss.SpeechTaskVO;
import com.welab.crm.interview.vo.tmk.TmkUuidCountVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 客服电销UUID名单服务
 * @date 2022/2/23 15:31
 */
public interface TmkUuidService {

    /**
     * 查询生效中的话术配置列表
     */
    List<SpeechTaskVO> getSpeechTaskList();

    /**
     * 保存从运营系统推送过来的UUID电销名单
     */
    Boolean saveUuidListFromOss(PushUuidDTO pushUuidDTO);

    /**
     * 同步外呼任务的配置数据到tmk_uuid_config表
     */
    void syncCrmCallConfig(TmkUuidConfigDTO tmkUuidConfigDTO);

    /**
     * 验证手机 ai外呼推送
     *
     * @param mobiles   待验证手机号
     * @param configId 任务配置id
     */
    void validateAiMobiles(String mobiles, Long configId);


    /**
     * 查询号码包推送数量
     * 运营系统查询
     * @param dto
     * @return
     */
    Response<TmkUuidCountVO> queryPushUuidCount(PushUuidQueryDTO dto);
    
    
    
}
