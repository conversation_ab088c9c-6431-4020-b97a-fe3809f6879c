package com.welab.crm.interview.vo.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Data
@ApiModel(value = "短信模板响应对象")
public class MessageTemplateVO implements Serializable {

    private static final long serialVersionUID = 6375314236564368461L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "短信模板代码")
    private String smsCode;

    @ApiModelProperty(value = "短信标题")
    private String title;

    @ApiModelProperty(value = "目录组")
    private String directory;

    @ApiModelProperty(value = "模板描述")
    private String description;

    @ApiModelProperty(value = "短信内容")
    private String context;

    @ApiModelProperty(value = "短信类型")
    private String smsType;
    
    @ApiModelProperty(value = "排序字段")
    private Integer sort;
}
