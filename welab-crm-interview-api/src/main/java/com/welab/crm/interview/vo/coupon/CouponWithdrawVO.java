package com.welab.crm.interview.vo.coupon;

import com.welab.marketing.dto.ActivityCashCouponHistoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/1
 */
@Data
@ApiModel(value = "用户返现红包响应对象")
public class CouponWithdrawVO implements Serializable {

    private static final long serialVersionUID = 5146720882787091567L;

    @ApiModelProperty(value = "uuid")
    private Long uuid;

    @ApiModelProperty(value = "可提现金额")
    private BigDecimal availableAmount;

    @ApiModelProperty(value = "历史提现红包列表")
    private List<CouponWithdrawDetailVO> couponsHistory;
}
