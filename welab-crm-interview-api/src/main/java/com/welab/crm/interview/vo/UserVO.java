/**
 * @Title: UserInfoVO.java
 * @Copyright: © 2018 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.interview.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * @description TODO
 *
 * <AUTHOR>
 * @date 2018-01-25 14:16:37
 * @version v1.0
 */

@Data
public class UserVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户id
	 */
	private Long id;

	/**
	 * 用户名
	 */
	private String userName;

	/**
	 * 创建时间
	 */
	private Date gmtCreate;

	/**
	 * 更新时间
	 */
	private Date gmtModified;

	/**
	 * 记录状态。0无效；1有效。逻辑删除时用
	 */
	private Boolean isDeleted;

}
