package com.welab.crm.interview.dto.tmk;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TmkUuidConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外呼任务id
     */
    private Long configId;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 号码包id
     */
    private String numPackageId;

    /**
     * 号码包定义
     */
    private String numPackageDef;

    /**
     * ai话术
     */
    private Integer speechId;

    /**
     * 话术所属的任务id
     */
    private Integer taskId;

    /**
     * 外呼说明
     */
    private String callInstruction;

    /**
     * 外呼方式: ai-ai外呼, man-人工外呼
     */
    private String callType;

    /**
     * 数据软删除标志
     */
    private Boolean deleteFlag;

    /**
     * 创建人userId
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date gmtCreate;
}
