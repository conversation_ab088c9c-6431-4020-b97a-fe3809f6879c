package com.welab.crm.interview.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 接收天润挂机推送参数的DTO，天润那边传过来的字段是下划线的。
 * <AUTHOR>
 * @date 2021/10/20 17:41
 */
@Data
public class ConPhoneCallInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 企业Id
     */
    private String cdr_enterprise_id;

    /**
     * 中继号码
     */
    private String cdr_number_trunk;

    /**
     * 热线号码
     */
    private String cdr_hotline;

    /**
     * 通话标识
     */
    private String cdr_main_unique_id;

    /**
     * 客户号码
     */
    private String cdr_customer_number;

    /**
     * 客户号码所属区号
     */
    private String cdr_customer_area_code;

    /**
     * 客户号码所属城市
     */
    private String cdr_customer_city;

    /**
     * 客户号码所属省份
     */
    private String cdr_customer_province;

    /**
     * 客户号码类型 1 固话 2 手机
     */
    private String cdr_customer_number_type;

    /**
     * 1 座席接听; 2 已呼叫座席,座席未接听; 3 系统接听; 4 系统未接-IVR配置错误; 5 系统未接-停机;
     */
    private String cdr_status;

    /**
     * 1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫
     */
    private String cdr_call_type;

    /**
     * 接听的员工坐席号
     */
    private String cdr_callee_cno;

    /**
     * 呼入进入队列时间
     */
    private String cdr_join_queue_time;

    /**
     * 呼入座席接听时间/外呼客户接听时间
     */
    private String cdr_bridge_time;

    /**
     * 进入系统时间/座席发起外呼时间
     */
    private String cdr_start_time;

    /**
     * 系统接听时间/外呼座席接听时间
     */
    private String cdr_answer_time;

    /**
     * 呼入/外呼挂机时间
     */
    private String cdr_end_time;

    /**
     * 录音文件名称
     */
    private String cdr_record_file_1;

    /**
     * 挂机原因1000 主通道挂机; 1001 非主通道挂机; 1002 被强拆
     */
    private String cdr_end_reason;

    /**
     * 双方接听时长
     */
    private String cdr_end_bridge_time;

    /**
     * 队列号
     */
    private String cdr_queue;

    /**
     * 号码状态识别结果。710:忙 711:超时 712:拒接 713:空号 714:关机 715:暂时无法接听 716:停机
     */
    private String cdr_detail_sip_cause;

    /**
     * 满意度调查开始时间，如果没有进行满意度调查值为0
     */
    private String cdr_investigation;

    /**
     * 坐席工号
     */
    private String cdr_cno;

    /**
     * 虚拟小号
     */
    private String cdr_x_number;

    /**
     * 客户振铃时间
     */
    private String callee_ringing_time;

    /**
     * 通话请求id
     */
    private String cdr_request_unique_id;

    /**
     * 座席绑定电话
     */
    private String cdr_agent_number;

    /**
     * 客户侧外显号码
     */
    private String cust_callee_clid;

    /**
     * 座席侧外显号码
     */
    private String cdr_clid;

    /**
     * ivr id
     */
    private String cdr_ivr_id;

    /**
     * ivr 按键轨迹
     */
    private String cdr_ivr_flow;

    /**
     * 外呼线路组
     */
    private String preview_outcall_ob_clid_group;

}
