package com.welab.crm.interview.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 收支明细还款方式枚举
 * @date 2021/11/23
 */
public enum RepaymentTypeEnum {

    WECHAT("wechat", "微信", "手动"),
    APP("app", "app", "手动"),
    OTHER("other", "其他", "自动"),
    OFFLINE("offline", "线下余额冲账", "线下余额冲账"),
    WITHHOLD("withhold", "系统代扣", "系统代扣"),
    CUISHOU("cuishou", "催收", "催收"),
    KEFU("kefu", "客服", "客服"),
    HELPPAYMENT("helpPayment", "帮还", "帮还"),
    OFFLINEPAYMENT("offlinePayment", "帮还线下模式", "帮还线下模式"),
    NOTIFICATION("notify", "资方代扣通知", "资方代扣通知")
    ;

    private String value;
    private String name;
    private String type;

    RepaymentTypeEnum(String value, String name, String type) {
        this.value = value;
        this.name = name;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getType(String value) {
        for (RepaymentTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type.getType();
            }
        }
        return value;
    }
}
