package com.welab.crm.interview.vo.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "用户银行卡信息响应对象")
public class BankCardUserVO implements Serializable {

    private static final long serialVersionUID = -212939559020390517L;

    @ApiModelProperty(value = "渠道信息", name = "channelInfo")
    private List<ChannelInfoVO> channelInfoVO;

    @ApiModelProperty(value = "信用额度", name = "creditLine")
    private BigDecimal creditLine;

    @ApiModelProperty(value = "可用信用额度", name = "avaiCreditLine")
    private BigDecimal avalCreditLine;

    @ApiModelProperty(value = "银行卡信息", name = "bankCard")
    private List<BankCardInfoVO> bankCard;
}
