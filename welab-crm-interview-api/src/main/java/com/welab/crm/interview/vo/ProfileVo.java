package com.welab.crm.interview.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 申请人信息
 * Created by Lyman on 2016/11/24.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfileVo implements Serializable {
	private static final long serialVersionUID = 2560607159249225410L;

	private String name;
	private String cnid;
	private String creditLine;
	private String currentAddr;
	private BigDecimal availableCreditLine;        //可用信用额度
	private String familyAddr;
	private Integer age;
	private String pengyuanIdentifyVeri;  //鹏元身份验证
	/**
	 * 用户籍贯
	 */
	private String idCardAddr;
	/**
	 * 身份证签发机关
	 */
	private String idCardGenOrg;
	/**
	 * 用户生肖
	 */
	private String zodiacSign;
	/**
	 * 用户星座
	 */
	private String starSign;
}
