package com.welab.crm.interview.dto.vip;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class SVipQueryDTO implements Serializable {

    private static final long serialVersionUID = 2967178941573519427L;

    private Long uuid;

    private Integer userId;

    /**
     * 支付状态
     */
    private String state;

    /**
     * 商家订单号
     */
    private String supplierOrderNo;

    /**
     * 我司订单号
     */
    private String orderNo;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 购买来源
     */
    private String buyChannel;

    /**
     * 会员付费模式，例如：先享后付、先付
     */
    private String memberPayMode;
}
