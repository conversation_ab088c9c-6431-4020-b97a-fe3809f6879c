package com.welab.crm.interview.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/3/7
 */
@Data
@ApiModel(value = "中央监控资产看板响应对象")
public class AssetsBoardVO implements Serializable {

    private static final long serialVersionUID = 4959046694147704002L;

    @ApiModelProperty(value = "优先级")
    private String priority;

    @ApiModelProperty(value = "资金方code")
    private String companyCode;

    @ApiModelProperty(value = "资金方编号")
    private String companyNo;

    @ApiModelProperty(value = "资金方")
    private String companyName;

    @ApiModelProperty(value = "额度限制")
    private BigDecimal limitAmount;

    @ApiModelProperty(value = "已用额度")
    private BigDecimal usedAmount;

    @ApiModelProperty(value = "可用额度")
    private BigDecimal unUsedAmount;

    @ApiModelProperty(value = "是否启用")
    private String state;
}
