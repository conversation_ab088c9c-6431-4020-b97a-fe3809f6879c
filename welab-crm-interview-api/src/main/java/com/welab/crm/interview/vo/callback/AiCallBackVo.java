package com.welab.crm.interview.vo.callback;

import com.welab.common.response.enums.ResponsCodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 回调回复给voice-ivr服务的响应值
 **/
@Getter
@Setter
public class AiCallBackVo {

    private String state;

    private String msg;

    public static AiCallBackVo success() {
        AiCallBackVo vo = new AiCallBackVo();
        vo.setState("200");
        vo.setMsg("success");
        return vo;
    }

    public static AiCallBackVo failure(String message) {
        AiCallBackVo vo = new AiCallBackVo();
        vo.setState(ResponsCodeTypeEnum.FAILURE.getCode());
        vo.setMsg(message);
        return vo;
    }
}

