package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.vip.SVipQueryDTO;
import com.welab.crm.interview.dto.vip.VipLockDTO;
import com.welab.crm.interview.dto.vip.VipLockTipsDTO;
import com.welab.crm.interview.vo.vip.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description vip客户服务
 * @date 2021/10/22
 */
public interface VipService {

    /**
     * 根据手机号获取VIP客户订单列表
     *
     * @param userId
     * @return
     */
    List<VipOrderVO> getVipOrderList(Integer userId);

    /**
     * 查询会员信息列表
     *
     * @param queryDTO uuid、订单号、付费模式、支付状态、购买来源、退款状态
     */
    List<VipInfoVO> getVipInfoList(SVipQueryDTO queryDTO);

    /**
     * 查询用户特权卡信息(查询是否特权卡用户，特权卡持续时间)
     */
    PrivilegeCardVO getUserPrivilegeCardList(Long uuid);

    /**
     * 查询用户特权卡信息(除了 getUserPrivilegeCardList 的信息外，添加IRR24标签和特权卡v2标签)
     */
    PrivilegeCardVO getUserPrivilegeCardListPlus(Long uuid, Integer userId);

    /**
     * 查询用户权益
     * @param orderNo
     * @return
     */
    List<VipBenefitOrderResVO> getVipBenefitOrders(String orderNo);

    /**
     * 查询操作日志
     * @param orderNo
     * @return
     */
    List<VipOperateLogVO> getVipOperateLog(String orderNo);

    /**
     * 冻结vip订单
     * @param dto
     * @return
     */
    Response<String> lock(VipLockDTO dto);

    /**
     * 冻结vip订单提示语
     * @param dto
     * @return
     */
    Response<String> lockTips(VipLockTipsDTO dto);

    /**
     * 解冻vip订单
     * @param dto
     * @return
     */
    Response<String> unlock(VipLockDTO dto);
}
