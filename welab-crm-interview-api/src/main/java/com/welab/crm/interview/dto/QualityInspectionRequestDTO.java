package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 质检请求DTO
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
@Data
public class QualityInspectionRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 录音文件URL
     */
    private String recordingUrl;

    /**
     * 通话唯一标识
     */
    private String callId;

    /**
     * 通话类型：1-呼入，4-呼出
     */
    private String callType;

    /**
     * 客户号码
     */
    private String customerNumber;

    /**
     * 座席工号
     */
    private String agentNumber;

    /**
     * 通话时长（秒）
     */
    private Integer callDuration;

    /**
     * 回调地址
     */
    private String callbackUrl;
}
