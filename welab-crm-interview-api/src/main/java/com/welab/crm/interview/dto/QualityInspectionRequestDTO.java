package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 阿里云质检请求DTO（符合UploadAudioData接口标准）
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Data
public class QualityInspectionRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务空间ID
     */
    private Long baseMeAgentId;

    /**
     * 是否自动分轨（0：不分轨，1：自动分轨）
     */
    private Integer autoSplit = 1;

    /**
     * 数据集ID（用于角色分离规则）
     */
    private Long recognizeRoleDataSetId;

    /**
     * 客服关键词列表（用于单轨录音角色分离）
     */
    private List<String> serviceChannelKeywords;

    /**
     * 客服轨道编号（双轨录音）
     */
    private Integer serviceChannel = 0;

    /**
     * 客户轨道编号（双轨录音）
     */
    private Integer clientChannel = 1;

    /**
     * 录音采样率（8：8000hz，16：16000hz）
     */
    private Integer sampleRate = 8;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 录音文件列表
     */
    private List<CallInfo> callList;

    /**
     * 录音文件信息
     */
    @Data
    public static class CallInfo implements Serializable {
        /**
         * 录音文件URL
         */
        private String voiceFileUrl;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 录音发生时间（毫秒时间戳）
         */
        private Long callStartTime;

        /**
         * 客服ID
         */
        private Long customerServiceId;

        /**
         * 客服姓名
         */
        private String customerServiceName;

        /**
         * 技能组ID
         */
        private Long skillGroupId;

        /**
         * 技能组名称
         */
        private String skillGroupName;

        /**
         * 呼叫类型（1：呼出，3：呼入）
         */
        private Integer callType;

        /**
         * 被叫号码
         */
        private String callee;

        /**
         * 主叫号码
         */
        private String caller;

        /**
         * 通话ID
         */
        private String callId;

        /**
         * 业务线名称
         */
        private String business;

        /**
         * 全局唯一标识（幂等使用）
         */
        private String callUuid;

        /**
         * 会话组ID
         */
        private String sessionGroupId;

        /**
         * 客户ID
         */
        private String customerId;

        /**
         * 客户姓名
         */
        private String customerName;

        /**
         * 质检任务ID
         */
        private String schemeTaskConfigId;

        /**
         * 自定义数据1-25
         */
        private String remark1;
        private String remark2;
        private String remark3;
        private String remark4;
        private Long remark5;
        private String remark6;
        private String remark7;
        private String remark8;
        private String remark9;
        private String remark10;
        private String remark11;
        private String remark12;
        private String remark13;
        private Long remark14;
        private Long remark15;
        private String remark16;
        private String remark17;
        private String remark18;
        private String remark19;
        private String remark20;
        private String remark21;
        private String remark22;
        private String remark23;
        private String remark24;
        private String remark25;

        /**
         * 更多自定义字段（JSON格式）
         */
        private String jsonParamStr;
    }
}
