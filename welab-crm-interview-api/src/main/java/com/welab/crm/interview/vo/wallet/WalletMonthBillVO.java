package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
@Data
@ApiModel(value = "钱包月账单响应对象")
public class WalletMonthBillVO implements Serializable {

    private static final long serialVersionUID = -8388069704314737908L;

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "账单年份")
    private int year;

    @ApiModelProperty(value = "账单月份")
    private int month;

    @ApiModelProperty(value = "账单日")
    private Date statementDate;

    @ApiModelProperty(value = "还款日")
    private Date repaymentDate;

    @ApiModelProperty(value = "月账单金额(应还账单总额)")
    private BigDecimal monthBillAmount;

    @ApiModelProperty(value = "本金")
    private BigDecimal principalAmount;

    @ApiModelProperty(value = "利息")
    private BigDecimal interestAmount;

    @ApiModelProperty(value = "担保费")
    private BigDecimal guaranteeAmount;

    @ApiModelProperty(value = "最低还款金额（剩余应还）")
    private BigDecimal leastRepayAmount;

    /**
     * 账单状态 {@link com.welab.wallet.app.enums.BillStatusEnum}
     */
    @ApiModelProperty(value = "账单状态")
    private String status;
}

