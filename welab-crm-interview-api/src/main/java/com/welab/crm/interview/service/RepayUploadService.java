package com.welab.crm.interview.service;

import java.util.List;

/**
 * 客户还款明细数据查询并保存文件
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
public interface RepayUploadService {

    /**
     * 从消金和资金的接口中查询和计算数据，形成文件推送到云并保存由此返回的url供下载
     *
     * @param id       数据库表cs_repay_file主键id
     * @param loanType 贷款类型:loan-现金贷,wallet-钱夹谷谷
     * @param uuidList 查询的用户列表
     */
    void queryAndSaveFileUrl(Long id, String loanType, List<String> uuidList);
}
