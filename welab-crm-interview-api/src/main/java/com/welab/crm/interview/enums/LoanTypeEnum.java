package com.welab.crm.interview.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 贷款类型枚举类
 */
public enum LoanTypeEnum {
    LOAN("loan", "现金贷"),
    WALLET("wallet", "钱夹谷谷");

    private final String value;
    private final String text;

    LoanTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static LoanTypeEnum getLoanTypeByValue(String loanType) {
        if (StringUtils.isNotBlank(loanType)) {
            for (LoanTypeEnum lte : values()) {
                if (lte.getValue().equals(loanType)) {
                    return lte;
                }
            }
        }
        return null;
    }
}
