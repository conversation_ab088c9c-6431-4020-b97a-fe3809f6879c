package com.welab.crm.interview.dto.workorder;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 工单联系人查询DTO
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class WorkOrderContactQueryDTO implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 是否首次联系
	 */
	private Integer isFirstContact;

	/**
	 * 工单未结案起始天数
	 */
	private Integer notOverStartDays;

	/**
	 * 工单未结案结束天数
	 */
	private Integer notOverEndDays;

	/**
	 * 工单类型列表
	 */
	private List<String> orderTypeList;

	/**
	 * 工单创建起始时间
	 */
	private Date startTime;

	/**
	 * 工单创建结束时间
	 */
	private Date endTime;
	
	
	private Long woTypeId;
	
	private Long woTypeFirId;
	
	private Long woTypeSecId;
	
	private Long woTypeThirId;
	
}
