package com.welab.crm.interview.service;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.UserInfoDTO;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.interview.vo.UserBlackInfoVO;
import com.welab.crm.interview.vo.UserInfoForChat;
import com.welab.crm.interview.vo.UserInfoVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/27 15:01
 */
public interface UserInfoService {


    /**
     * 根据手机号、身份证、贷款号、订单号、userID、uuid查询用户信息
     *
     * @param userDetailQueryDTO
     * @return
     */
    PersonalDetailsVoExpand queryUserInfo(UserDetailQueryDTO userDetailQueryDTO);

    /**
     * 查询用户信息简化版
     * @param userDetailQueryDTO
     * @return
     */
    UserInfoForChat queryUserInfoSimple(UserDetailQueryDTO userDetailQueryDTO);

    /**
     * 根据渠道订单号查询贷款号
     * @param channelOrderNo
     * @return
     */
    String queryApplicationIdByExternalOrderNo(String channelOrderNo);



    /**
     * 用户信息修改
     * @param adminMobile 修改人手机号
     * @param userInfoVo
     * @param userId
     * @return
     */
    Map<String,String> updateUserInfo(String adminMobile, UserInfoVo userInfoVo, Integer userId);


    /**
     * 用户信息修改

     * @param userId
     * @param userInfoVo
     * @param operatorId 操作人Id
     * @return
     */
    int updateUserInfo(Integer userId, UserInfoVo userInfoVo, Integer operatorId);


    /**
     * 查询注销用户信息
     *
     * @param mobile
     * @return
     */
    JSONObject queryBlockUserInfo(String mobile);


    /**
     * 注销用户
     *
     * @param userId
     * @return
     */
    String blockUser(String userId);


    /**
     * 查询钱包用户
     * @param userDetailQueryDTO
     * @return
     */
    WalletUserDTO queryWalletUser(UserDetailQueryDTO userDetailQueryDTO);

    /**
     * 根据手机号查询出全部的用户
     */
    List<UserInfoDTO> mobileQuery(UserDetailQueryDTO dto);


    /**
     * 这个跟上面 queryUserInfo 的不同在于增加了运营商的获取，提供给创研使用
     * @param userDetailQueryDTO
     * @return
     */
    PersonalDetailsVoExpand queryUserInfoPlus(UserDetailQueryDTO userDetailQueryDTO);

    /**
     * 根据手机号获取全部用户信息
     */
    List<UserInfoForChat> queryAllUserInfoByMobile(UserDetailQueryDTO userDetailQueryDTO);

    /**
     * 查询客户信息简化版
     * @param uuid
     * @return
     */
    UserInfoForChat queryUserInfoSimpleVersion(Long uuid);

    /**
     * 三要素认证查询
     */
    String verifyUserInfo(Integer userId, UserInfoVo userInfoVo);

    /**
     * 根据uuid查询用户信息
     * @param uuid uuid
     * @return 客户信息
     */
    UserBlackInfoVO getUserIdByUuid(String uuid);

    /**
     * 根据applicationId获取用户uuid
     * @param applicationId
     * @return
     */
    String getUuidByApplicationId(String applicationId);

    /**
     * 根据userId获取uuid
     * @param userId
     * @return
     */
    String getUuidByUserId(Integer userId);
    
    
}
