package com.welab.crm.interview.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/7
 */
@Data
@ApiModel(value = "修改短信模板请求对象")
public class MessageTemplateUpdateDTO implements Serializable {

    private static final long serialVersionUID = 5337248640176361271L;

    @ApiModelProperty(value = "主键")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "短信模板代码，来自短信系统")
    private String smsCode;

    @ApiModelProperty(value = "短信标题")
    private String title;

    @ApiModelProperty(value = "目录组，字典字段")
    private String directory;

    @ApiModelProperty(value = "模板描述")
    private String description;

    @ApiModelProperty(value = "短信类型，字典字段")
    private String smsType;
    
    @ApiModelProperty(value = "排序字段")
    private Integer sort;
}
