package com.welab.crm.interview.enums;

/**
 * 交易类型枚举类
 * <AUTHOR>
 */
public enum ActionTypeEnum {
    ACTION_DISBURSE("disburse", "借款人放款 "),
    ACTION_INVEST("invest", "Lender 放款"),
    ACTION_INCOME("income", "手续费收入"),
    ACTION_TOPUP_FEE("topup_fee", "充值费用"),
    ACTION_RISK_RETURN("risk_return", "风险垫付还款"),
    ACTION_RETURN("return", "还款"),
    ACTION_COLLECT("collect", "coolpad 放款"),
    ACTION_REPAY("repay", "借款人还款"),
    ACTION_TOPUP("topup", "充值"),
    ACTION_OFFLINE_TOPUP("offline_topup", "线下充值"),
    ACTION_WITHDRAW("withdraw", "提现"),
    ACTION_RISK_PAY("risk_pay", "风险垫付"),
    ACTION_COUPON("coupon", "红包"),
    ACTION_COUPON_REVERSE("coupon_reverse", "红包冲正"),
    ACTION_REVERSE("reverse", "冲正"),
    ACTION_CONSUME("consume", "消费贷款"),
    ACTION_FREEZE("freeze", "冻结"),
    ACTION_UNFREEZE("unfreeze", "解冻"),
    ACTION_CREDIT_IN("credit_in", "债权金额转入"),
    ACTION_CREDIT_OUT("credit_out", "债权金额转出"),
    ACTION_WITHDRAW_FREEZE("withdraw_freeze", "提现账务冻结"),
    ACTION_BID_FREEZE("bid_freeze", "投标账务冻结"),
    ACTION_REVERSE_FREEZE("reverse_freeze", "账务解冻"),
    ACTION_INTERNAL_TRANSFER("internal_transfer", "内部帐号转账"),
    ACTION_INTERNAL_FREEZE("internal_freeze", "内部帐号冻结"),
    ACTION_INTERNAL_UNFREEZE("internal_unfreeze", "内部帐号解冻"),
    ACTION_RISK_WRITEOFF("risk_writeoff", "风险销账"),
    ACTION_TRANSFER_IN("transfer_in", "普通转账转入"),
    ACTION_TRANSFER_OUT("transfer_out", "普通转账转出"),

    ACTION_REFUND_COLLECT("action_refund_collect", "退款给用户"),
    ACTION_REFUND("action_refund", "商家退款"),
    ACTION_REFUND_RETURN("action_refund_return", "退款给投资人"),
    ACTION_PREMIUM_ADVANCE("premium_advance", "保费垫付"),
    ACTION_REIMBURSE("reimburse", "赔付"),
    ACTION_RETURN_PRINCIPAL("return_principal", "还款本金"),
    ACTION_RETURN_INTEREST("return_interest", "还款利息"),
    ACTION_RETURN_OVERDUE_PENALTY("return_overdue_penalty", "还款逾期罚金"),
    ACTION_RETURN_LATE_PERIOD("return_late_period", "还款贷后费用"),

    ACTION_INCOME_OVERDUE_PENALTY("income_overdue_penalty", "逾期罚息收入"),
    ACTION_INCOME_PREMIUM("income_premium", "保费收入"),
    ACTION_INCOME_LATE_PERIOD("income_late_period", "贷后费用收入"),
    FINANCE_ACTION_SYNC("sync", "线下还款，新老账户同步"),

    ACTION_SUBSIDY("subsidy","补贴"),
    ACTION_MARGIN_ADVANCE("margin_advance","本息（保证金）垫付"),
    ACTION_CHARGEUP("chargeup","挂账"),
    ACTION_ADVANCE("advance","垫付"),
    ACTION_UNSETTLE("unsettle","待结算"),
    ACTION_SETTLE("settle","结算"),
    ACTION_CREDIT_TRANSFER("credit_transfer","债权转让");

    private String value;
    private String text;

    public String getValue() {
        return value;
    }

    public static String getType(String value) {
        for (ActionTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type.getText();
            }
        }
        return value;
    }

    private ActionTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
