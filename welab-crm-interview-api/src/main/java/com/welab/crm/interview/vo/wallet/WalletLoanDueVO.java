package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Data
@ApiModel(value = "钱包项目用户分期模式账单详情响应对象")
public class WalletLoanDueVO implements Serializable {

    private static final long serialVersionUID = 1994759997194816354L;

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "期数")
    private Integer tenor;

    @ApiModelProperty(value = "放款金额(交易金额)")
    private BigDecimal amount;

    @ApiModelProperty(value = "全额结清金额")
    private BigDecimal settleAllAmount;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "放款日期（交易日期）")
    private String disbursedAt;

    @ApiModelProperty(value = "结清日期")
    private String closedAt;

    @ApiModelProperty(value = "合作资方名称（资金方）")
    private String partnerName;

    @ApiModelProperty(value = "当前还款期数")
    private Integer currentIndexNo;

    @ApiModelProperty(value = "当期应还金额")
    private BigDecimal currentFeeAmount;

    @ApiModelProperty(value = "当期已还金额")
    private BigDecimal currentSettledAmount;

    @ApiModelProperty(value = "当期未还金额")
    private BigDecimal currentOutstandingAmount;

    @ApiModelProperty(value = "是否全额结清")
    private String isAllowEarlySettle;

    @ApiModelProperty(value = "资方编码")
    private String partnerCode;

    @ApiModelProperty(value = "费率")
    private String loanRate;
}

