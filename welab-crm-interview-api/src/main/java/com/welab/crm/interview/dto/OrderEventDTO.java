/**
 * @Title: OrderEvent.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description 订单事件
 *
 * <AUTHOR>
 * @date 2017-12-13 15:41:38
 * @version v1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderEventDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 机构id
	 */
	private Long orgId;
	/**
	 * 订单
	 */
	private Long orderId;
	/**
	 * 产品名称
	 */
	private String productName;
}
