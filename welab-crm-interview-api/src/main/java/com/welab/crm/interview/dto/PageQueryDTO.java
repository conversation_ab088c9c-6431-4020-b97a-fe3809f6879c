package com.welab.crm.interview.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/16
 */
@Data
@ApiModel(value = "分页查询请求对象")
public class PageQueryDTO implements Serializable {

    private static final long serialVersionUID = -8660148271980704338L;

    @ApiModelProperty(value = "当前页")
    private Integer currentPage = 1;

    @ApiModelProperty(value = "每页行数")
    private Integer rowsPerPage = 20;
}
