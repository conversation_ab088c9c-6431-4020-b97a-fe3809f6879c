package com.welab.crm.interview.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 证明信息
 * Created by Lyman on 2016/11/24.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentVo implements Serializable {
    private static final long serialVersionUID = -7088263506877233281L;

    private String photoType;           //照片类型
    private String originalPhotoUrl;    //原始照片地址
    private String thumbPhotoUrl;    //缩略照片地址
    private Integer id;                 //照片id
    private String photoName;           //照片名称
    private String photoGroup;          //照片类型分组：( 1 , 2 )    1 -> 身份相关照片   2 -> 工作相关照片

    public enum PhotoType {
        // text :   1->身份相关照片   2-> 工作相关照片
        ID_FRONT_PROOF("id_front_proof","1"),
        ID_BACK_PROOF("id_back_proof","1"),
        ID_HANDHELD_PROOF("id_handheld_proof","1"),
        ID_SOCIAL_SECURITY_CARD_PROOF("id_social_security_card_proof","1"),
        ID_HOLD_SOCIAL_SECURITY_CARD_PROOF("id_hold_social_security_card_proof","1"),
        FACE_RECOGNITION("face_recognition_proof","1"),

        EMPLOYMENT_PROOF("employment_proof","2"),
        ADDITIONAL_EMPLOYMENT_PROOF("additional_employment_proof","2"),
        BANK_CARD_TRANSACTION_FLOW_PROOF("bank_card_transaction_flow_proof","2"),
        CREDIT_CARD_BILL_PROOF("credit_card_bill_proof","2");

        private String value ;
        private String text;

        PhotoType(String value, String text) {
            this.value = value;
            this.text = text;
        }

        public static PhotoType getType(String value){
            for(PhotoType photoType : values()){
                if (photoType.getValue().equals(value)) {
                    return photoType;
                }
            }
            return null;
        }

        public String getValue() {
            return value;
        }


        public String getText() {
            return text;
        }

    }


    public enum DocType {
        ID_FRONT_PROOF("id_front_proof"),//身份证正面
        ID_BACK_PROOF("id_back_proof"),//身份证反面
        STUDENT_BOOK_PROFILE_PROOF("student_book_profile_proof"),
        STUDENT_BOOK_COVER_PROOF("student_book_cover_proof"),
        ID_HANDHELD_PROOF("id_handheld_proof"),//手持身份证
        LOAN_AGREEMENT("loan_agreement"),//贷款协议
        LOAN_AGREEMENT_UNMASK("loan_agreement_unmask"),
        EMPLOYMENT_PROOF("employment_proof"),//职业证明
        ADDITIONAL_EMPLOYMENT_PROOF("additional_employment_proof"),//附加职业证明
        RESIDENTIAL_PROOF("residential_proof"),//住宅证明
        ADMIN_UPLOADED("admin_uploaded"),//管理员上传
        ID_SOCIAL_SERUCITY_CARD_PROOF("id_social_security_card_proof"),//社保卡证明
        ID_HOLD_SOCIAL_SERUCITY_CARD_PROOF("id_hold_social_security_card_proof"),//手持社保卡证明
        AGENT_AGREEMENT("agent_agreement"),//代理人协议
        SUPPLEMENT_PROOF("supplement_proof"),//补充照片
        EDUCATION_SYSTEM_INFO_PROOF("education_system_info_proof"),//教育系统信息证明
        BANK_CARD_TRANSACTION_FLOW_PROOF("bank_card_transaction_flow_proof"),//银行卡流水证明
        CREDIT_CARD_BILL_PROOF("credit_card_bill_proof"),//信用卡账单证明
        LOAN_REPAYMENT_CERTIFICATE_PROOF("loan_repayment_certificate_proof"),//贷款还款证明
        CREDIT_REPORT("credit_report"),//征信合同
        FACE_RECOGNITION("face_recognition_proof"),//人脸识别
        RENFA_PROOF("rf_proof"),//人法网图片类型
        SHIXIN_PROOF("sx_proof"),//失信网图片类型
        SHZX_PROOF("shzx_proof"),//上海资信图片类型
        WDHMD_PROOF("wdhmd_proof"),//网贷黑名单图片类型
        GONGSHANG_PROOF("gs_proof"),
        BUSINESS_LICENSE_PROOF("business_license_proof"),//营业执照照片
        SHOP_PROOF("shop_proof");//店铺门头照片

        private String m_value;

        private DocType(String value) {
            m_value = value;
        }

        public String getValue() {
            return m_value;
        }
    }


}
