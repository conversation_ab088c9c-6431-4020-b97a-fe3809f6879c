package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.vo.loan.*;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 贷款信息服务
 * @date 2021/9/29
 */
public interface LoanApplicationService {

    /**
     * 根据客户uuid查询贷款列表
     *
     * @param uuid
     * @return
     */
    List<LoanApplicationVO> getLoanApplicationList(Long uuid);

    /**
     * 根据贷款号或手机号查询贷款详细信息
     *
     * @param applicationId
     * @return
     */
    LoanDetailsVO getLoanDetails(String applicationId);

    /**
     * 根据贷款好查询渠道单号跟资金方单号(借据号)
     * @param applicationId
     * @return
     */
    LoanDetailsVO getExternalOrderNoByApplicationId(String applicationId);

    /**
     * 根据贷款号查询交易记录
     * @param applicationId
     * @return
     */
    List<LoanRecordVO> getOrderRecord(String applicationId);

    /**
     * 根据贷款号查询还款计划列表
     * @param applicationId
     * @return
     */
    List<RepaymentPlanVO> getRepaymentPlan(String applicationId);

    /**
     * 查询在途贷款列表
     *
     * @param userId
     * @return
     */
    List<LoanOutstandingVO> getOutstandingLoanList(Integer userId);

    /**
     * 将贷款修改为支持提前结清
     *
     * @param applicationId
     */
    void allowEarlySettle(String applicationId);

    /**
     * 批量开启提前结清
     *
     * @param applicationIdList
     * @return 失败的贷款号列表
     */
    List<String> allowEarlySettleBatch(List<String> applicationIdList);

    /**
     * 提前结清贷款
     *
     * @param staffMobile   员工手机号
     * @param applicationId
     */
    void earlySettle(String staffMobile, String applicationId);

    /**
     * 批量提前结清
     * @param userId
     * @param staffMobile      员工手机号
     * @param applicationIdList
     * @return 失败的贷款号列表
     */
    List<String> earlySettleBatch(Integer userId, String staffMobile, List<String> applicationIdList);

    /**
     * 获取收支明细
     *
     * @param userId
     * @return
     */
    List<PaymentsDetailsVO> getPaymentsDetails(Integer userId);

    String getProductName(String productCode);


    String getState(String status);

    /**
     * 根据用户id查询(已放款)贷款列表
     * @param userId
     * @return
     */
    List<LoanVO> getLoanVOList(Integer userId);

    /**
     * 获取资金方名称
     */
    String getPartnerName(String partnerCode);


    /**
     * 获取全额结清金额
     * @param applicationId
     * @return
     */
    BigDecimal getLoanBalance(String applicationId);


    /**
     * 根据合同号列表查询合同信息信息
     * @param applications 合同号列表
     * @return
     */
    List<LoanApplicationDTO> getApplicationListByApplications(List<String> applications);


    /**
     * 查询贷款协议文件下载地址
     * @param appNo 贷款号
     */
    Response<byte[]> getApplicationAgreementUrl(String appNo);



    /**
     * 根据过滤条件筛选委外/债转文件导入主页列表
     */
    LoanImportLabelVO getImportLabelInfo(String applicationId);
}
