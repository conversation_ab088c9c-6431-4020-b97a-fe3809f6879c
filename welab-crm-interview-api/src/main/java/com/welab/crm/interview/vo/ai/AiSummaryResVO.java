package com.welab.crm.interview.vo.ai;

import lombok.Data;

import java.io.Serializable;

/**
 * AI总结返回对象
 *
 * <AUTHOR>
 */
@Data
public class AiSummaryResVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer code;


	private String message;

	private SummaryRes data;

	private String uid;

	@Data
	public class SummaryRes implements Serializable{
		private static final long serialVersionUID = 1L;
		/**
		 * 对话
		 */
		private String dialogue;

		/**
		 * 小结
		 */
		private String summary;
	}


}
