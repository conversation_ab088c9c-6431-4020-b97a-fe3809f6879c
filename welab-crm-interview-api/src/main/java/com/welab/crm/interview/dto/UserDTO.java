/**
 * @Title: UserInfo.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.interview.dto;

import java.io.Serializable;

import org.hibernate.validator.constraints.NotBlank;

import com.welab.crm.interview.service.ScaffoldService.SaveUserInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 用户信息
 *
 * <AUTHOR> 请修改作者信息，例如：gorge.guo
 * @date TODO 请修改时间，例如：2017-12-28 16:17:46
 * @version v1.0
 */

@Data
public class UserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", name = "id", example = "1")
    private Long id;

    /**
     * 用户名字
     */
    @ApiModelProperty(value = "用户名", name = "userName", example = "张三")
    @NotBlank(message = "用户名不能为空", groups = {SaveUserInfo.class})
    private String userName;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码", name = "userPassword", example = "123456")
    @NotBlank(message = "用户密码不能为空", groups = {SaveUserInfo.class})
    private String userPassword;

    /**
     * 记录状态。0无效；1有效。逻辑删除时用
     */
    @ApiModelProperty(value = "记录是否有效", name = "isDelete", example = "1")
    private Boolean isDelete;

}