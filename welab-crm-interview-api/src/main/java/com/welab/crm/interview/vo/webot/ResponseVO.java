package com.welab.crm.interview.vo.webot;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 创研接口响应对象.
 */
@Getter
@Setter
@ToString
public class ResponseVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码：状态码，0-成功  1-失败
     */
    private Integer ret;

    /**
     * 返回码对应的描述信息
     */
    private String msg;

    /**
     * 返回结果
     */
    private T data;
}

