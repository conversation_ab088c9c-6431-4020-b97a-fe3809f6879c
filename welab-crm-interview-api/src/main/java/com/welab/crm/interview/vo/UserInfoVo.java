package com.welab.crm.interview.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Lyman on 2016/12/7.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoVo implements Serializable {
	private static final long serialVersionUID = 4165783098686112131L;

	private String name;   		// 姓名
	private Integer role;    	// 身份
	private String mobile;   	//手机号
	private String origin;    	//来源
	private String cnid;        //身份
	private Boolean isAgent;    //类型

}
