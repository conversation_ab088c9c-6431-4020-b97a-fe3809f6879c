package com.welab.crm.interview.dto.wechat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 钱包还款计划DTO
 * <AUTHOR>
 * @date 2020-11-16 10:05:49
 * @version v2.0
 */
@Data
@ApiModel("钱包还款计划实体类")
public class WalletDueDTO implements Serializable {
    private static final long serialVersionUID = 8722938343663897248L;
    /** 还款期数 **/
    @ApiModelProperty(value = "还款期数", name = "index")
    private Integer index;
    /** 已还金额 **/
    @ApiModelProperty(value = "已还金额", name = "sumStillAmount")
    private BigDecimal sumStillAmount;
    /** 应还金额 **/
    @ApiModelProperty(value = "应还金额", name = "sumDueAmount")
    private BigDecimal sumDueAmount;
    /** 还款日期 **/
    @ApiModelProperty(value = "还款日期", name = "dueDate")
    private String dueDate;
    /** 还款计划 **/
    @ApiModelProperty(value = "还款计划", name = "detail")
    private List<WalletDueSettleDTO> detail;
}
