package com.welab.crm.interview.enums.coupon;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 卡券业务类型
 * @date 2022/3/10
 */
public enum CouponBusinessEnum {

    LOAN("loan", "工薪贷"),
    WALLET("wallet", "钱包"),
    ;

    private String value;
    private String text;

    CouponBusinessEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getText(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (CouponBusinessEnum businessEnum : values()) {
            if (businessEnum.getValue().equals(value)) {
                return businessEnum.getText();
            }
        }
        return null;
    }
}
