package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 质检结果DTO
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
@Data
public class QualityInspectionResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 质检状态：0-处理中，1-成功，2-失败
     */
    private Integer status;

    /**
     * 质检分数（0-100）
     */
    private Integer score;

    /**
     * 质检完成时间
     */
    private Date completedTime;

    /**
     * 质检详情
     */
    private String details;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 质检维度评分
     */
    private QualityDimensionScore dimensionScore;

    @Data
    public static class QualityDimensionScore implements Serializable {
        /**
         * 服务态度分数
         */
        private Integer serviceAttitudeScore;

        /**
         * 专业能力分数
         */
        private Integer professionalSkillScore;

        /**
         * 沟通技巧分数
         */
        private Integer communicationSkillScore;

        /**
         * 合规性分数
         */
        private Integer complianceScore;
    }
}
