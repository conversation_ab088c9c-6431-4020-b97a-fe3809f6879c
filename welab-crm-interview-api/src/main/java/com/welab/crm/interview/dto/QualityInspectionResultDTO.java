package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 阿里云质检结果DTO（符合GetResult接口标准）
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Data
public class QualityInspectionResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 质检状态：0-处理中，1-成功，其他-失败
     */
    private Integer status;

    /**
     * 质检分数（0-100）
     */
    private Integer score;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 复核状态（0：未复核，1：已复核）
     */
    private Integer reviewStatus;

    /**
     * 复核结果（0：错误，1：正确，2：部分正确，3：待复核）
     */
    private Integer reviewResult;

    /**
     * 复核意见
     */
    private String comments;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 转写结果
     */
    private List<AsrResult> asrResult;

    /**
     * 质检命中结果
     */
    private List<HitResult> hitResult;

    /**
     * 录音文件信息
     */
    private Recording recording;

    /**
     * 客服信息
     */
    private Agent agent;

    /**
     * 转写结果
     */
    @Data
    public static class AsrResult implements Serializable {
        /**
         * 对话内容
         */
        private String words;

        /**
         * 开始时间（毫秒）
         */
        private Long begin;

        /**
         * 结束时间（毫秒）
         */
        private Long end;

        /**
         * 角色（客服、客户）
         */
        private String role;

        /**
         * 情绪值（0-10）
         */
        private Integer emotionValue;

        /**
         * 语速（字/分钟）
         */
        private Integer speechRate;

        /**
         * 身份标识
         */
        private String identity;
    }

    /**
     * 质检命中结果
     */
    @Data
    public static class HitResult implements Serializable {
        /**
         * 规则ID
         */
        private String rid;

        /**
         * 规则名称
         */
        private String name;

        /**
         * 规则类型
         */
        private String type;

        /**
         * 复核结果
         */
        private Integer reviewResult;

        /**
         * 评分
         */
        private Integer score;

        /**
         * 命中详情
         */
        private List<Hit> hits;
    }

    /**
     * 命中详情
     */
    @Data
    public static class Hit implements Serializable {
        /**
         * 条件ID列表
         */
        private List<String> cid;

        /**
         * 命中的句子信息
         */
        private Phrase phrase;

        /**
         * 命中的关键词
         */
        private List<KeyWord> keyWords;
    }

    /**
     * 句子信息
     */
    @Data
    public static class Phrase implements Serializable {
        /**
         * 对话内容
         */
        private String words;

        /**
         * 开始时间（毫秒）
         */
        private Long begin;

        /**
         * 结束时间（毫秒）
         */
        private Long end;

        /**
         * 角色
         */
        private String role;

        /**
         * 情绪值
         */
        private Integer emotionValue;
    }

    /**
     * 关键词信息
     */
    @Data
    public static class KeyWord implements Serializable {
        /**
         * 关键词内容
         */
        private String val;

        /**
         * 起始位置
         */
        private Integer from;

        /**
         * 结束位置
         */
        private Integer to;

        /**
         * 条件ID
         */
        private String cid;
    }

    /**
     * 录音文件信息
     */
    @Data
    public static class Recording implements Serializable {
        /**
         * 文件名称
         */
        private String name;

        /**
         * 文件URL
         */
        private String url;

        /**
         * 通话时长（秒）
         */
        private Long duration;

        /**
         * 通话ID
         */
        private String callId;

        /**
         * 主叫号码
         */
        private String caller;

        /**
         * 被叫号码
         */
        private String callee;

        /**
         * 呼叫类型
         */
        private Integer callType;

        /**
         * 业务线名称
         */
        private String business;
    }

    /**
     * 客服信息
     */
    @Data
    public static class Agent implements Serializable {
        /**
         * 客服ID
         */
        private String id;

        /**
         * 客服姓名
         */
        private String name;

        /**
         * 技能组
         */
        private String skillGroup;
    }
}
