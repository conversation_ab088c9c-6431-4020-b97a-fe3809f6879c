package com.welab.crm.interview.vo.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/28
 */
@Data
@ApiModel(value = "钱包项目授信额度信息响应对象")
public class WalletGrantQuotaVO implements Serializable {

    private static final long serialVersionUID = 3323688611506177494L;

    @ApiModelProperty(value = "额度编号")
    private String applicationId;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "申请类型")
    private String applyType;

    @ApiModelProperty(value = "订单状态")
    private String state;

    @ApiModelProperty(value = "授信申请时间")
    private String appliedAt;

    @ApiModelProperty(value = "审批时间")
    private String approvedAt;

    @ApiModelProperty(value = "是否加急")
    private String urgent;

    @ApiModelProperty(value = "渠道号")
    private String origin;

}
