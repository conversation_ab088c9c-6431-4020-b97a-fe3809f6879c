package com.welab.crm.interview.service;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.bankcard.BankCardMatchDTO;
import com.welab.crm.interview.vo.bankcard.BankCardUserVO;
import com.welab.crm.interview.vo.bankcard.BankChannelVO;
import com.welab.finance.bankcard.dto.BankCardUnbindDTO;
import com.welab.finance.bankcard.dto.BankCardVerifyDTO;
import com.welab.finance.bankcard.vo.BankCardVerifyVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 银行卡服务
 * @date 2021/10/22
 */
public interface BankCardService {

    /**
     * 根据手机号获得用户银行卡信息
     *
     * @param userId
     * @return
     */
    BankCardUserVO getUserBankCardInfo(Integer userId);


    /**
     * 打开解绑卡开关
     * @param dto
     */
    Response<Object> openUnbind(BankCardUnbindDTO dto);

    /**
     * 获取绑卡详情
     * @param dto
     * @return
     */
    Response<List<BankCardVerifyVO>> getChannelVerify(@RequestBody BankCardVerifyDTO dto);

    /**
     * 银行卡换卡
     *
     * @param staffMobile 员工手机号
     * @param bankCardId 银行卡id
     */
    void changeBankCard(String staffMobile, Long bankCardId);

    /**
     * 绑卡功能查询
     * @param dto
     * @return
     */
    List<BankChannelVO> bankCardMatch(BankCardMatchDTO dto);

    /**
     * 解除银行卡授权
     *
     * @param userId
     * @param bankName
     */
    String releaseBankCardAuth(Integer userId, String bankName);
}
