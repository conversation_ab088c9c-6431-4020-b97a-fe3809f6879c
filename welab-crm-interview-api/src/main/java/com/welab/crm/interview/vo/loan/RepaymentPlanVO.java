package com.welab.crm.interview.vo.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "在途贷款还款明细响应对象")
public class RepaymentPlanVO implements Serializable {

    private static final long serialVersionUID = -237293464438176455L;

    @ApiModelProperty(value = "还款期数")
    private Integer index;

    @ApiModelProperty(value = "已还金额")
    private BigDecimal sumStillAmount;

    @ApiModelProperty(value = "应还金额")
    private BigDecimal sumDueAmount;

    @ApiModelProperty(value = "还款日期")
    private Date dueDate;

    @ApiModelProperty(value = "结算信息")
    private List<RepaymentPlanSettlementVO> detail;
}
