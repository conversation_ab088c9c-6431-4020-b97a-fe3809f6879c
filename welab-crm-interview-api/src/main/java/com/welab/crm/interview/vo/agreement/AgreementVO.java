package com.welab.crm.interview.vo.agreement;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class AgreementVO implements Serializable {

	@JsonProperty("ownerCode")
	private String ownerCode;
	@JsonProperty("businessCode")
	private String businessCode;
	@JsonProperty("code")
	private String code;
	@JsonProperty("name")
	private String name;
	@JsonProperty("url")
	private String url;
	@JsonProperty("version")
	private Integer version;
	@JsonProperty("fileName")
	private String fileName;
	@JsonProperty("categoryCode")
	private String categoryCode;
	@JsonProperty("categoryName")
	private String categoryName;
	@JsonProperty("isFillData")
	private Integer isFillData;
	@JsonProperty("isSign")
	private Integer isSign;
	@JsonProperty("isSilentSign")
	private Integer isSilentSign;
	@JsonProperty("content")
	private String content;
	@JsonProperty("status")
	private Integer status;
	@JsonProperty("releaseTime")
	private String releaseTime;
	@JsonProperty("releaseBy")
	private String releaseBy;
	@JsonProperty("remark")
	private String remark;
	@JsonProperty("dataMd5")
	private String dataMd5;
	@JsonProperty("createBy")
	private String createBy;
	@JsonProperty("createTime")
	private String createTime;
	@JsonProperty("updateBy")
	private String updateBy;
	@JsonProperty("updateTime")
	private String updateTime;
}
