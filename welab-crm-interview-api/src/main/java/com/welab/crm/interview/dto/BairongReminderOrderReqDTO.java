package com.welab.crm.interview.dto;

import com.welab.crm.interview.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "百融催单入参")
public class BairongReminderOrderReqDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "唯一工单编号,字母跟数字组成,50个字符以内")
	@NotBlank(message = "工单编号不能为空")
	private String uniqueOrderNo;
	
	@ApiModelProperty(value = "催单内容")
	@NotBlank(message = "催单内容不能为空")
	private String content;

}
