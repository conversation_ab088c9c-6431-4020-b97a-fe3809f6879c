package com.welab.crm.interview.vo;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON> on 2017/1/5.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonalBasicVo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 8473514392956996760L;

    private Long uuid;
    private String mobile;
    private String cnid;
    private String name;
    private List<LiaisonVo> liaison;
    private String creditLine; //最大额度
    private String availableCredit; // 可用额度
    private String state;

}
