package com.welab.crm.interview.enums;

/**
 * 回购状态枚举
 */
public enum RepurchaseStatusEnum {
	NOT_REPURCHASE(0L,"未回购"),
	REPURCHASED(1L,"已回购"),
	REPURCHASING(2L,"回购中"),
	;
	

	private Long code;
	
	private String desc;

	RepurchaseStatusEnum(Long code, String desc) {
		this.code = code;
		this.desc = desc;
	}
	
	public static String getDescByCode(Long code){
		for (RepurchaseStatusEnum repurchaseStatusEnum : values()) {
			if (repurchaseStatusEnum.code.equals(code)){
				return repurchaseStatusEnum.desc;
			}
		}
		
		return code.toString();
	}
	
}
