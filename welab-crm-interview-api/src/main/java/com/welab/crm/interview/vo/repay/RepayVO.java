package com.welab.crm.interview.vo.repay;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;

/**
 * 还款明细导出vo
 *
 * <AUTHOR>
 * @date 2022-11-03
 */
@Data
@HeadFontStyle(fontHeightInPoints = 12)
public class RepayVO {

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    private String customerName;

    /**
     * 贷款号
     */
    @ExcelProperty(value = "贷款号")
    private String applicationId;

    /**
     * uuid
     */
    @ExcelProperty(value = "uuid")
    private String uuid;

    /**
     * userId
     */
    @ExcelProperty(value = "userId")
    private String userId;

    /**
     * 资金方名称
     */
    @ExcelProperty(value = "资金方名称")
    private String partnerName;


    /**
     * 回购状态
     */
    @ExcelProperty(value = "回购")
    private String repurchaseStatus;

    /**
     * 产品号
     */
    @ExcelProperty(value = "产品号")
    private String productNo;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 渠道名称
     */
    @ExcelProperty(value = "渠道名称")
    private String originName;

    /**
     * 贷款金额
     */
    @ExcelProperty(value = "贷款金额")
    private String loanAmount;

    /**
     * 贷款期数
     */
    @ExcelProperty(value = "贷款期数")
    private String loanTerms;

    /**
     * 已还期数
     */
    @ExcelProperty(value = "已还期数")
    private String paidTerms;

    /**
     * 未还期数
     */
    @ExcelProperty(value = "未还期数")
    private String unPayTerms;

    /**
     * 放款时间
     */
    @ExcelProperty(value = "放款时间")
    private String makeLoanTime;

    /**
     * 利率
     */
    @ExcelProperty(value = "利率")
    private String loanRate;

    /**
     * 结清时间: 贷款号对应结清时间，在途贷款中则为空
     */
    @ExcelProperty(value = "结清时间")
    private String settledTime;

    /**
     * 是否结清: 已结清、未结清
     */
    @ExcelProperty(value = "是否结清")
    private String whetherSettled;

    /**
     * 是否提前结清: 是”则贷款周期内提前结清，“否”则贷款周期内正常还款
     */
    @ExcelProperty(value = "是否提前结清")
    private String earlySettled;

    /**
     * 最近期还款日期，例：2022-10-19；已还清则展示“空”
     */
    @ExcelProperty(value = "当前还款日期")
    private String deadDate;

    /**
     * 最近期还款日期，例：2022-10-19；已还清则展示“空”
     */
    @ExcelProperty(value = "最近应还日期")
    private String lastShouldTime;


    @ExcelProperty(value = "债转公司名称")
    private String loanTransferCompany;

    @ExcelProperty(value = "债转结清时间")
    private String loanTransferTime;

    @ExcelProperty(value = "提前结清金额")
    private String earlySettleAMount;

    /**
     * 应还本金
     */
    @ExcelProperty(value = "应还本金")
    private String shouldCapital;

    /**
     * 应还利息
     */
    @ExcelProperty(value = "应还利息")
    private String shouldInterest;

    /**
     * 应还审批费
     */
    @ExcelProperty(value = "应还审批费")
    private String shouldApproveFee;

    /**
     * 应还管理费
     */
    @ExcelProperty(value = "应还管理费")
    private String shouldMaintenance;

    /**
     * 应还逾期利息
     */
    @ExcelProperty(value = "应还逾期利息")
    private String shouldOverdueInterest;

    /**
     * 应还提现费
     */
    @ExcelProperty(value = "应还提现费")
    private String shouldWithdrawFee;

    /**
     * 应还代扣费
     */
    @ExcelProperty(value = "应还代扣费")
    private String shouldWithholdFee;

    /**
     * 应还逾期管理费
     */
    @ExcelProperty(value = "应还逾期管理费")
    private String shouldOverdueMaintenanceFee;

    /**
     * 应还逾期罚金
     */
    @ExcelProperty(value = "应还逾期罚金")
    private String shouldOverduePenalty;

    /**
     * 应还提前还款罚息
     */
    @ExcelProperty(value = "应还提前还款罚息")
    private String shouldEarlyPenalty;

    /**
     * 应还催收委外费
     */
    @ExcelProperty(value = "应还催收委外费")
    private String shouldOaFee;

    /**
     * 应还保费
     */
    @ExcelProperty(value = "应还保费")
    private String shouldInsureFee;

    /**
     * 应还手机保障费
     */
    @ExcelProperty(value = "应还手机保障费")
    private String shouldMobileGuaranteeFee;

    /**
     * 应还手机买断费
     */
    @ExcelProperty(value = "应还手机买断费")
    private String shouldMobileBuyOutFee;

    /**
     * 应还手机损坏费
     */
    @ExcelProperty(value = "应还手机损坏费")
    private String shouldMobileDamageFee;

    /**
     * 应还红包
     */
    @ExcelProperty(value = "应还红包")
    private String shouldRedPack;

    /**
     * 应还担保费
     */
    @ExcelProperty(value = "应还担保费")
    private String shouldGuaranteeFee;

    /**
     * 应还风险保障服务费
     */
    @ExcelProperty(value = "应还风险保障服务费")
    private String shouldGuaranteeServiceFee;

    /**
     * 应还风险保障金
     */
    @ExcelProperty(value = "应还风险保障金")
    private String shouldGuaranteeAmount;

    /**
     * 应还评审服务费
     */
    @ExcelProperty(value = "应还评审服务费")
    private String shouldReviewFee;

    /**
     * 应还担保咨询服务费
     */
    @ExcelProperty(value = "应还担保咨询服务费")
    private String shouldGuaranteeAskFee;

    /**
     * 应还总金额
     */
    @ExcelProperty(value = "应还总金额")
    private String shouldTotalAmount;

    /**
     * 已还本金
     */
    @ExcelProperty(value = "已还本金")
    private String factCapital;

    /**
     * 已还利息
     */
    @ExcelProperty(value = "已还利息")
    private String factInterest;

    /**
     * 已还审批费
     */
    @ExcelProperty(value = "已还审批费")
    private String factApproveFee;

    /**
     * 已还管理费
     */
    @ExcelProperty(value = "已还管理费")
    private String factMaintenanceFee;

    /**
     * 已还逾期利息
     */
    @ExcelProperty(value = "已还逾期利息")
    private String factOverdueInterest;

    /**
     * 已还提现费
     */
    @ExcelProperty(value = "已还提现费")
    private String factWithdrawFee;

    /**
     * 已还代扣费
     */
    @ExcelProperty(value = "已还代扣费")
    private String factWithholdFee;

    /**
     * 已还逾期管理费
     */
    @ExcelProperty(value = "已还逾期管理费")
    private String factOverdueMaintenanceFee;

    /**
     * 已还逾期罚金
     */
    @ExcelProperty(value = "已还逾期罚金")
    private String factOverduePenalty;

    /**
     * 已还提前还款罚息
     */
    @ExcelProperty(value = "已还提前还款罚息")
    private String factEarlyPenalty;

    /**
     * 已还催收委外费
     */
    @ExcelProperty(value = "已还催收委外费")
    private String factOaFee;

    /**
     * 已还保费
     */
    @ExcelProperty(value = "已还保费")
    private String factInsureFee;

    /**
     * 已还手机保障费
     */
    @ExcelProperty(value = "已还手机保障费")
    private String factMobileGuaranteeFee;

    /**
     * 已还手机买断费
     */
    @ExcelProperty(value = "已还手机买断费")
    private String factMobileBuyOutFee;

    /**
     * 已还手机损坏费
     */
    @ExcelProperty(value = "已还手机损坏费")
    private String factMobileDamageFee;

    /**
     * 已还红包
     */
    @ExcelProperty(value = "已还红包")
    private String factRedPack;

    /**
     * 已还担保费
     */
    @ExcelProperty(value = "已还担保费")
    private String factGuaranteeFee;

    /**
     * 已还风险保障服务费
     */
    @ExcelProperty(value = "已还风险保障服务费")
    private String factGuaranteeServiceFee;

    /**
     * 已还风险保障金
     */
    @ExcelProperty(value = "已还风险保障金")
    private String factGuaranteeAmount;

    /**
     * 已还评审服务费
     */
    @ExcelProperty(value = "已还评审服务费")
    private String factReviewFee;

    /**
     * 已还担保咨询服务费
     */
    @ExcelProperty(value = "已还担保咨询服务费")
    private String factGuaranteeAskFee;

    /**
     * 已还总金额
     */
    @ExcelProperty(value = "已还总金额")
    private String factTotalAmount;

    /**
     * 未还本金
     */
    @ExcelProperty(value = "未还本金")
    private String unpaidCapital;

    /**
     * 未还利息
     */
    @ExcelProperty(value = "未还利息")
    private String unpaidInterest;

    /**
     * 未还审批费
     */
    @ExcelProperty(value = "未还审批费")
    private String unpaidApproveFee;

    /**
     * 未还管理费
     */
    @ExcelProperty(value = "未还管理费")
    private String unpaidMaintenanceFee;

    /**
     * 未还逾期利息
     */
    @ExcelProperty(value = "未还逾期利息")
    private String unpaidOverdueInterest;

    /**
     * 未还提现费
     */
    @ExcelProperty(value = "未还提现费")
    private String unpaidWithdrawFee;

    /**
     * 未还代扣费
     */
    @ExcelProperty(value = "未还代扣费")
    private String unpaidWithholdFee;
    /**
     * 未还逾期管理费
     */
    @ExcelProperty(value = "未还逾期管理费")
    private String unpaidOverdueMaintenanceFee;

    /**
     * 未还逾期罚金
     */
    @ExcelProperty(value = "未还逾期罚金")
    private String unpaidOverduePenalty;

    /**
     * 未还提前还款罚息
     */
    @ExcelProperty(value = "未还提前还款罚息")
    private String unpaidEarlyPenalty;

    /**
     * 未还催收委外费
     */
    @ExcelProperty(value = "未还催收委外费")
    private String unpaidOaFee;

    /**
     * 未还保费
     */
    @ExcelProperty(value = "未还保费")
    private String unpaidInsureFee;

    /**
     * 未还手机保障费
     */
    @ExcelProperty(value = "未还手机保障费")
    private String unpaidMobileGuaranteeFee;

    /**
     * 未还手机买断费
     */
    @ExcelProperty(value = "未还手机买断费")
    private String unpaidMobileBuyOutFee;

    /**
     * 未还手机损坏费
     */
    @ExcelProperty(value = "未还手机损坏费")
    private String unpaidMobileDamageFee;

    /**
     * 未还红包
     */
    @ExcelProperty(value = "未还红包")
    private String unpaidRedPack;

    /**
     * 未还担保费
     */
    @ExcelProperty(value = "未还担保费")
    private String unpaidGuaranteeFee;

    /**
     * 未还风险保障服务费
     */
    @ExcelProperty(value = "未还风险保障服务费")
    private String unpaidGuaranteeServiceFee;

    /**
     * 未还风险保障金
     */
    @ExcelProperty(value = "未还风险保障金")
    private String unpaidGuaranteeAmount;

    /**
     * 未还评审服务费
     */
    @ExcelProperty(value = "未还评审服务费")
    private String unpaidReviewFee;

    /**
     * 未还担保咨询服务费
     */
    @ExcelProperty(value = "未还担保咨询服务费")
    private String unpaidGuaranteeAskFee;

    /**
     * 未还总金额
     */
    @ExcelProperty(value = "未还总金额")
    private String unpaidTotalAmount;

    public RepayVO() {
        shouldCapital = "0";
        shouldInterest = "0";
        shouldApproveFee = "0";
        shouldMaintenance = "0";
        shouldOverdueInterest = "0";
        shouldWithdrawFee = "0";
        shouldWithholdFee = "0";
        shouldOverdueMaintenanceFee = "0";
        shouldOverduePenalty = "0";
        shouldEarlyPenalty = "0";
        shouldOaFee = "0";
        shouldInsureFee = "0";
        shouldMobileGuaranteeFee = "0";
        shouldMobileBuyOutFee = "0";
        shouldMobileDamageFee = "0";
        shouldRedPack = "0";
        shouldGuaranteeFee = "0";
        shouldGuaranteeServiceFee = "0";
        shouldGuaranteeAmount = "0";
        shouldReviewFee = "0";
        shouldGuaranteeAskFee = "0";
        shouldTotalAmount = "0";
        factCapital = "0";
        factInterest = "0";
        factApproveFee = "0";
        factMaintenanceFee = "0";
        factOverdueInterest = "0";
        factWithdrawFee = "0";
        factWithholdFee = "0";
        factOverdueMaintenanceFee = "0";
        factOverduePenalty = "0";
        factEarlyPenalty = "0";
        factOaFee = "0";
        factInsureFee = "0";
        factMobileGuaranteeFee = "0";
        factMobileBuyOutFee = "0";
        factMobileDamageFee = "0";
        factRedPack = "0";
        factGuaranteeFee = "0";
        factGuaranteeServiceFee = "0";
        factGuaranteeAmount = "0";
        factReviewFee = "0";
        factGuaranteeAskFee = "0";
        factTotalAmount = "0";
        unpaidCapital = "0";
        unpaidInterest = "0";
        unpaidApproveFee = "0";
        unpaidMaintenanceFee = "0";
        unpaidOverdueInterest = "0";
        unpaidWithdrawFee = "0";
        unpaidWithholdFee = "0";
        unpaidOverdueMaintenanceFee = "0";
        unpaidOverduePenalty = "0";
        unpaidEarlyPenalty = "0";
        unpaidOaFee = "0";
        unpaidInsureFee = "0";
        unpaidMobileGuaranteeFee = "0";
        unpaidMobileBuyOutFee = "0";
        unpaidMobileDamageFee = "0";
        unpaidRedPack = "0";
        unpaidGuaranteeFee = "0";
        unpaidGuaranteeServiceFee = "0";
        unpaidGuaranteeAmount = "0";
        unpaidReviewFee = "0";
        unpaidGuaranteeAskFee = "0";
        unpaidTotalAmount = "0";
    }
}
