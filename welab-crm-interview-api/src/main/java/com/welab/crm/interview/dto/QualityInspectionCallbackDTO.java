package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 质检回调DTO
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
@Data
public class QualityInspectionCallbackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 通话唯一标识
     */
    private String callId;

    /**
     * 质检状态：1-成功，2-失败
     */
    private Integer status;

    /**
     * 质检分数（成功时）
     */
    private Integer score;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 完成时间戳
     */
    private Long completedTimestamp;
}
