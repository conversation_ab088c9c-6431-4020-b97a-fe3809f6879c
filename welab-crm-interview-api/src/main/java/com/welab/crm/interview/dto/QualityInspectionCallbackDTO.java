package com.welab.crm.interview.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 阿里云质检回调DTO（符合回调标准）
 * <AUTHOR> Generated
 * @date 2024-08-05
 */
@Data
public class QualityInspectionCallbackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 时间戳（毫秒）
     */
    private String timestamp;

    /**
     * 阿里云主账号UID
     */
    private String aliUid;

    /**
     * 签名
     */
    private String signature;

    /**
     * 事件名称（TaskComplete：任务完成）
     */
    private String event;

    /**
     * 质检状态：0-处理中，1-成功，其他-失败
     */
    private Integer status;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 完成时间戳
     */
    private Long completedTimestamp;

    /**
     * 是否验证签名通过
     */
    private transient boolean signatureValid = false;
}
